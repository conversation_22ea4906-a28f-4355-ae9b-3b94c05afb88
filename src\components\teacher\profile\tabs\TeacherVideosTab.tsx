import React from 'react';
import { Video, PlayCircle, Lock } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Teacher } from '@/types/teacher';

interface TeacherVideosTabProps {
  teacher: Teacher;
  onVideoPlay: (videoIndex: number) => void;
}

export const TeacherVideosTab: React.FC<TeacherVideosTabProps> = ({ teacher, onVideoPlay }) => {
  return (
    <div className="space-y-6 p-4 md:p-6">
      <Card className="bg-white border border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-gray-800">
            <Video className="w-6 h-6 text-blue-500" />
            <PERSON>
          </CardTitle>
          {teacher.videoCoursesIntro && (
            <p className="text-gray-600">{teacher.videoCoursesIntro}</p>
          )}
        </CardHeader>
        <CardContent>
          {teacher.videoCourses && teacher.videoCourses.length > 0 ? (
            <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {teacher.videoCourses.map((video, index) => (
                <div
                  key={video.id || index}
                  className="group cursor-pointer transform hover:scale-105 transition-all duration-200"
                  onClick={() => video.free ? onVideoPlay(index) : null}
                >
                  <div className="relative overflow-hidden rounded-lg mb-3 bg-gray-100">
                    <img
                      src={video.thumbnailUrl || '/placeholder.svg'}
                      alt={video.name || 'Video ders'}
                      className="w-full h-32 object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      {video.free ? (
                        <div className="bg-white/90 rounded-full p-3 group-hover:scale-110 transition-transform duration-300">
                          <PlayCircle className="w-6 h-6 text-blue-600" />
                        </div>
                      ) : (
                        <div className="bg-white/90 rounded-full p-3">
                          <Lock className="w-6 h-6 text-gray-600" />
                        </div>
                      )}
                    </div>
                    {video.duration && (
                      <Badge className="absolute bottom-2 right-2 bg-black/70 text-white">
                        {video.duration}
                      </Badge>
                    )}
                    {video.level && (
                      <Badge 
                        className={`absolute top-2 left-2 ${
                          video.level === 'Temel' 
                            ? 'bg-green-500' 
                            : video.level === 'Orta' 
                            ? 'bg-yellow-500' 
                            : 'bg-red-500'
                        } text-white`}
                      >
                        {video.level}
                      </Badge>
                    )}
                    {video.free && (
                      <Badge className="absolute top-2 right-2 bg-blue-500 text-white">
                        Ücretsiz
                      </Badge>
                    )}
                  </div>
                  <h3 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">
                    {video.name || 'Video Başlığı'}
                  </h3>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">Bu öğretmen için henüz video ders bulunmamaktadır.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
