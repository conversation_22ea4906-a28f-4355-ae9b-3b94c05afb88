// src/lib/actions/student.actions.ts
"use server";

// Kaldırıldı: import { PrismaClient } from "@prisma/client";
import { prisma } from '@/lib/prisma'; // Eklendi
// Prisma namespace'i bu dosyada do<PERSON><PERSON> kull<PERSON>, @prisma/client'tan ayrıca import etmeye gerek yok.
import { unstable_noStore as noStore } from 'next/cache';
// import { auth } from "@clerk/nextjs/server"; // Clerk kaldırıldı

// Öğrencinin yaklaşan derslerini getirir
export async function getUpcomingLessonsForStudent(limit: number = 5) {
  // const authData = await auth(); // Clerk kaldırıldı
  let userId = null; // Clerk kaldırıldı // authData.userId;

  // DEV_SKIP_AUTH_MIDDLEWARE aktifken ve userId yoksa (null veya undefined) test için varsayılan ID kullan
  if ((userId === null || userId === undefined) && process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
    // console.warn("DEV_SKIP_AUTH_MIDDLEWARE: getUpcomingLessonsForStudent - Using default 'test-student-id'."); // Removed debug log
    userId = "test-student-id"; // Test için varsayılan bir öğrenci ID'si
  }

  if (!userId) {
    // console.error("getUpcomingLessonsForStudent: userId is missing and DEV_SKIP_AUTH_MIDDLEWARE is not providing a fallback.");
    return [];
  }
  // TODO: Rol kontrolü de eklenebilir (sadece student rolü için)

  noStore();

  try {
    const now = new Date();
    const upcomingBookings = await prisma.booking.findMany({
      where: {
        studentId: userId, // Clerk userId = Student ID varsayımı
        lessonTime: {
          gte: now, // Şu andan sonraki dersler
        },
        // status: 'CONFIRMED', // Sadece onaylanmış dersleri göster?
      },
      orderBy: {
        lessonTime: 'asc', // Yaklaşanlar önce
      },
      take: limit,
      include: {
        teacher: { // Öğretmen bilgilerini dahil et
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profile_image_url: true, // Avatar için
          }
        }
      }
    });

    // Öğretmen isimlerini birleştirelim (veya DTO kullanalım)
    const lessonsWithTeacherName = upcomingBookings.map(booking => ({
        ...booking,
        teacherName: `${booking.teacher.firstName ?? ''} ${booking.teacher.lastName ?? ''}`.trim(),
        teacherProfileImageUrl: booking.teacher.profile_image_url,
    }));


    return lessonsWithTeacherName;

  } catch (error) {
    // console.error(`Failed to fetch upcoming lessons for student (ID: ${userId}):`, error); // Removed debug log
    return [];
  }
  // finally bloğu global instance için kaldırıldı
}

// Öğrenci paneli için öğretmenleri listeleyen Server Action
export async function getTeachersForStudentDashboard(limit: number = 10) {
  noStore(); // Sonuçların cache'lenmemesini sağla

  // Not: Bu action şu anda tüm onaylanmış öğretmenleri listeler.
  // Gelecekte öğrencinin tercihlerine, aldığı derslere veya
  // popülerliğe göre filtrelenmiş/sıralanmış bir liste döndürebilir.
  // Öğrenciye özel bir listeleme için auth() ve userId kontrolü eklenebilir.

  try {
    const teachers = await prisma.teacher.findMany({
      where: {
        is_approved: true, // Sadece onaylanmış öğretmenler
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        shortBio: true, // bio -> shortBio olarak düzeltildi
        profile_image_url: true,
        specializations: true, // Uzmanlık alanları
        hourly_rate: true,
        country: true,
        // average_rating: true, // Varsa ortalama puan
        // _count: { // Varsa yorum sayısı
        //   select: { reviewsReceived: true }
        // }
      },
      take: limit, // Sonuç sayısını sınırla
      orderBy: {
        // createdAt: 'desc' // Veya popülerliğe göre sıralama eklenebilir
        firstName: 'asc' // Şimdilik isme göre sırala
      }
    });

    // Prisma Decimal tipini number'a çevir
    return teachers.map(teacher => ({
      ...teacher,
      hourly_rate: teacher.hourly_rate ? teacher.hourly_rate.toNumber() : null,
    }));

  } catch (error) {
    // console.error("Failed to fetch teachers for student dashboard:", error); // Removed debug log
    return []; // Hata durumunda boş dizi döndür
  }
}

// Öğrencinin geçmiş derslerini getiren Server Action
export async function getStudentPastLessons({ studentId, limit = 5 }: { studentId?: string | null; limit?: number }) {
  noStore();

  let currentStudentId = studentId;
  // const authData = await auth(); // Clerk kaldırıldı

  if (!currentStudentId) {
    // currentStudentId = authData.userId; // Clerk kaldırıldı
    // Geçici olarak, eğer studentId sağlanmazsa ve DEV modu aktifse test ID'si kullanalım
    if (process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
      // console.warn("DEV_SKIP_AUTH_MIDDLEWARE: getStudentPastLessons - No studentId provided, using default 'test-student-id'.");
      currentStudentId = "test-student-id";
    }
  }

  if ((currentStudentId === null || currentStudentId === undefined) && process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
    // console.warn("DEV_SKIP_AUTH_MIDDLEWARE: getStudentPastLessons - Using default 'test-student-id'."); // Removed debug log
    currentStudentId = "test-student-id";
  }

  if (!currentStudentId) {
    // console.error("getStudentPastLessons: studentId is missing. Login required or check DEV_SKIP_AUTH_MIDDLEWARE."); // Removed debug log
    return [];
  }

  try {
    const now = new Date();
    const pastBookings = await prisma.booking.findMany({
      where: {
        studentId: currentStudentId,
        lessonTime: {
          lt: now, // Şu andan önceki dersler
        },
        // status: { // Sadece tamamlanmış veya iptal edilmiş dersler
        //   in: ['COMPLETED', 'CANCELLED_BY_STUDENT', 'CANCELLED_BY_TEACHER', 'CANCELLED_BY_ADMIN'],
        // },
      },
      orderBy: {
        lessonTime: 'desc', // En son tamamlananlar/iptal edilenler önce
      },
      take: limit,
      include: {
        teacher: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profile_image_url: true,
          },
        },
      },
    });

    return pastBookings.map(booking => ({
      ...booking,
      teacherName: `${booking.teacher.firstName ?? ''} ${booking.teacher.lastName ?? ''}`.trim(),
      teacherProfileImageUrl: booking.teacher.profile_image_url,
    }));

  } catch (error) {
    // console.error(`Failed to fetch past lessons for student (ID: ${currentStudentId}):`, error); // Removed debug log
    return [];
  }
}

// TODO: Diğer öğrenciye özel action'lar buraya eklenebilir (ders geçmişi, öğretmenleri listeleme vb.)