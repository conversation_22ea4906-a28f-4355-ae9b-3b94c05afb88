import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(dateString?: string | Date | null): string {
  if (!dateString) {
    return "<PERSON><PERSON><PERSON> be<PERSON>il<PERSON>";
  }
  try {
    const date = new Date(dateString);
    // Geçersiz tarih kontrolü
    if (isNaN(date.getTime())) {
      return "Geçersiz Tarih";
    }
    return new Intl.DateTimeFormat('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      // hour: '2-digit',
      // minute: '2-digit',
    }).format(date);
  } catch (error) {
    console.error("formatDate error:", error);
    return "Tarih formatlanamadı";
  }
}
