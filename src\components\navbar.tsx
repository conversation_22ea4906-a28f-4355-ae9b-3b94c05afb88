"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation"; // useRouter eklendi
import { useState, useEffect, ChangeEvent, FormEvent } from "react"; // ChangeEvent ve FormEvent eklendi
// framer-motion import ediliyor
import * as framerMotion from "framer-motion";
const { motion, AnimatePresence } = framerMotion;
import { Menu, X, Search } from "lucide-react"; // Search ikonu eklendi
import { useLanguage } from "@/lib/i18n/LanguageContext";
import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input"; // HTML input kullanıldığı için kaldırıldı
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"; // Bildirimler için eklendi
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuList,
  // NavigationMenuLink, // Kullanılmadığı için kaldırıldı
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";
import Image from "next/image";

type NavbarProps = {
  onMenuButtonClick?: () => void;
  isDashboardLayout?: boolean; // Layout'tan gelen prop, isDashboard ile karışmaması için farklı isimlendirdim
};

export function Navbar({ onMenuButtonClick, isDashboardLayout }: NavbarProps) {
  const pathname = usePathname();
  const router = useRouter(); // useRouter hook'u eklendi
  const { t } = useLanguage();

  const [showHeader, setShowHeader] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState(""); // Arama state'i eklendi

  useEffect(() => {
    // Client-side kontrolü
    if (typeof window === 'undefined') return;

    let prevScrollY = window.scrollY;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (currentScrollY > prevScrollY && currentScrollY > 50) {
        setShowHeader(false);
      } else if (currentScrollY < prevScrollY) {
        setShowHeader(true);
      }

      prevScrollY = currentScrollY;
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navItems = [
    { name: "Ana Sayfa", href: "/" },
    { name: "Öğretmenler", href: "/ogretmenler" },
    { name: "Kurslar", href: "/kurslar" },
    { name: "Hakkımızda", href: "/hakkimizda" },
    { name: "İletişim", href: "/iletisim" },
    { name: "Yardım Merkezi", href: "/yardim" }, // Yeni link eklendi
  ];

  // Eğer dashboard/admin sayfasındaysa farklı navbar
  // isDashboardLayout prop'u varsa onu kullan, yoksa pathname'e göre belirle
  const isActuallyDashboard = isDashboardLayout !== undefined ? isDashboardLayout :
    (pathname.startsWith("/(dashboard") || // Bu kısım hala geçerli olabilir, eğer prop gelmezse diye
     pathname.startsWith("/admin") ||
     pathname.startsWith("/teacher") ||
     pathname.startsWith("/student"));

  // Arama input değişikliğini yöneten fonksiyon
  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Arama gönderildiğinde çalışacak fonksiyon
  const handleSearchSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault(); // Formun sayfayı yenilemesini engelle
    if (searchTerm.trim()) {
      router.push(`/search-results?q=${encodeURIComponent(searchTerm.trim())}`);
    }
  };

  if (isActuallyDashboard) {
    return (
      <motion.header
        initial={{ y: -100 }}
        animate={{
          y: showHeader ? 0 : -100,
        }}
        transition={{
          type: "spring",
          stiffness: 150,
          damping: 22,
        }}
        className={`sticky top-0 z-50 w-full border-b bg-background transition-opacity duration-300 ${showHeader ? "opacity-100" : "opacity-0"}`}
      >
        <div className="flex h-12 items-center justify-between px-4">
          <Button
            variant="ghost"
            size="icon"
            className="shrink-0 md:hidden"
            onClick={onMenuButtonClick}
          >
            <Menu className="h-5 w-5" />
          </Button>

          <div className="hidden md:flex md:flex-1 md:items-center md:gap-4 md:px-4">
            {/* Arama kutusu kaldırıldı */}
          </div>
          
          <div className="flex items-center gap-2">
            {/* Bildirimler Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="relative">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-5 w-5"
                  >
                    <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9" />
                    <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0" />
                  </svg>
                  {/* TODO: Okunmamış bildirim sayısı dinamik olmalı */}
                  <span className="absolute right-1 top-1 flex h-2 w-2 rounded-full bg-red-600"></span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Bildirimler</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {/* TODO: Gerçek bildirimler buraya gelecek */}
                <DropdownMenuItem disabled className="text-muted-foreground text-sm">
                  Yeni bildirim yok
                </DropdownMenuItem>
                {/* <DropdownMenuItem>Bildirim 1</DropdownMenuItem>
                <DropdownMenuItem>Bildirim 2</DropdownMenuItem> */}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </motion.header>
    );
  }
  return (
    (<header className="sticky top-0 z-40 w-full border-b bg-background" suppressHydrationWarning>
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-8">
          <Link
            href="/"
            className="flex items-center space-x-2 font-bold"
            passHref>
            {/* legacyBehavior (varsa) kaldırıldı ve passHref eklendi */}
            <Image
              src="/logo.png"
              alt="AlmancaABC Logo"
              width={32}
              height={32}
              className="h-8 w-8"
            />
            <span className="text-xl">
              <span className="text-primary">Almanca</span>ABC
            </span>
          </Link>

          <div className="hidden md:block">
            <NavigationMenu>
              <NavigationMenuList>
                {navItems.map((item) => (
                  <NavigationMenuItem key={item.href}>
                    <Link
                      href={item.href}
                      className={cn(
                        navigationMenuTriggerStyle(),
                        pathname === item.href
                          ? "text-primary"
                          : "text-muted-foreground"
                      )}>
                      {item.name}
                    </Link>
                  </NavigationMenuItem>
                ))}
              </NavigationMenuList>
            </NavigationMenu>
          </div>
        </div>

        <div className="flex items-center gap-4">
          {/* Arama Formu */}
          <form onSubmit={handleSearchSubmit} className="hidden md:flex items-center gap-2">
            <div className="relative">
              <input
                type="search"
                placeholder="Öğretmen veya ders ara..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:w-[200px] lg:w-[300px]"
              />
              <Button type="submit" variant="ghost" size="icon" className="absolute right-0 top-0 h-9 w-9">
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </form>

          <div className="hidden md:flex gap-2 h-9">
            <Button variant="outline" size="sm">{t("nav.login")}</Button>
            <Button size="sm">{t("nav.register")}</Button>
          </div>

          <Button
            variant="ghost"
            size="icon"
            className="md:hidden rounded-full"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>
      </div>
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden border-t bg-background"
          >
            <div className="container py-4 space-y-4">
              {/* Mobil Arama Formu */}
              <form onSubmit={handleSearchSubmit} className="flex items-center gap-2">
                <div className="relative flex-1">
                  <input
                    type="search"
                    placeholder="Öğretmen veya ders ara..."
                    value={searchTerm}
                    onChange={handleSearchChange}
                    className="h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                  />
                  <Button type="submit" variant="ghost" size="icon" className="absolute right-0 top-0 h-9 w-9">
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </form>

              <nav className="flex flex-col space-y-2">
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`px-4 py-2 rounded-md ${
                      pathname === item.href ? "bg-accent font-medium" : "hover:bg-accent/50"
                    }`}
                    onClick={() => setIsMobileMenuOpen(false)}
                    // passHref eklendi
                    passHref>
                    {item.name}
                  </Link>
                ))}
              </nav>

              <div className="flex gap-2 pt-2 border-t h-9">
                <Button variant="outline" size="sm" className="flex-1">{t("nav.login")}</Button>
                <Button size="sm" className="flex-1">{t("nav.register")}</Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>)
  );
}
