import { NextRequest, NextResponse } from "next/server";
// import { auth } from "@clerk/nextjs/server"; // Clerk kaldırıldı
// import { setUserRole, getUserRole, isUserAdmin } from "@/lib/actions/user.actions"; // Clerk bağımlı fonksiyonlar kaldırıldı/yorumlandı
import { z } from "zod";

// İstek şeması
const SetRoleSchema = z.object({
  userId: z.string().min(1, "Kullanıcı ID'si gereklidir"),
  role: z.enum(["admin", "teacher", "student"], {
    errorMap: () => ({ message: "Geçerli bir rol belirtilmelidir: admin, teacher veya student" }),
  }),
});

export async function POST(request: NextRequest) {
  try {
    // Clerk kaldırıldığı için bu API rotası geçici olarak devre dışı bırakıldı.
    // Alternatif bir kimlik doğrulama ve yetkilendirme mekanizması entegre edilene kadar kullanılamaz.
    return NextResponse.json(
      {
        error: "Rol atama işlevi geçici olarak devre dışı.",
        message: "Kimlik doğrulama sistemi (Clerk) kaldırıldığı için bu özellik şu anda kullanılamıyor."
      },
      { status: 503 } // Service Unavailable
    );

    /* // Clerk kaldırılmadan önceki kod:
    // İsteği doğrula
    const body = await request.json();
    const validationResult = SetRoleSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Geçersiz istek formatı", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { userId, role } = validationResult.data;

    // İsteği yapan kullanıcının kimliğini doğrula
    // const authData = await auth(); // Clerk kaldırıldı
    // const requestingUserId = authData.userId; // Clerk kaldırıldı
    let requestingUserId: string | null = null;
    if (process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
        requestingUserId = "test-admin-id"; // Test için varsayılan istek yapan kullanıcı
    }


    if (!requestingUserId) {
      return NextResponse.json(
        { error: "Yetkilendirme başarısız" },
        { status: 401 }
      );
    }

    // Sadece admin kullanıcılar rol atayabilir (kendi rolünü değiştirmek isteyen kullanıcı hariç)
    if (userId !== requestingUserId) {
      // const isAdmin = await isUserAdmin(requestingUserId); // isUserAdmin fonksiyonu Clerk bağımlıydı
      const isAdmin = process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true" && requestingUserId === "test-admin-id"; // Geçici admin kontrolü
      
      if (!isAdmin) {
        return NextResponse.json(
          { error: "Başka kullanıcıların rollerini değiştirmek için admin yetkisi gereklidir" },
          { status: 403 }
        );
      }
    }

    // Rolü ayarla
    // await setUserRole(userId, role); // setUserRole fonksiyonu Clerk bağımlıydı

    // Güncellenmiş rolü getir ve yanıt olarak döndür
    // const updatedRole = await getUserRole(userId); // getUserRole fonksiyonu Clerk bağımlıydı
    const updatedRole = role; // Geçici olarak atanan rolü döndür

    return NextResponse.json({
      success: true,
      userId,
      role: updatedRole,
    });
    */
  } catch (error) {
    console.error("Rol atama hatası (API devre dışı):", error);
    return NextResponse.json(
      { error: "Rol atama işlemi sırasında bir hata oluştu (API devre dışı)" },
      { status: 500 }
    );
  }
}
