// src/app/sign-in/page.tsx
"use client";

import { signInWithEmailAndPassword } from "@/lib/actions/auth.actions";
import { useState, useTransition, useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";

export default function SignInPage() {
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);
  const [infoMessage, setInfoMessage] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const router = useRouter(); // Yönlendirme için

  useEffect(() => {
    const message = searchParams.get('message');
    const errorParam = searchParams.get('error'); // Middleware'den gelen hata
    if (message === 'signup_successful_please_signin') {
      setInfoMessage('<PERSON><PERSON>t başarılı! Lütfen giriş yapın.');
    } else if (message === 'signup_successful_check_email') {
      setInfoMessage('Kayıt başarılı! Lütfen e-postanızı kontrol ederek hesabınızı onaylayın ve ardından giriş yapın.');
    } else if (errorParam) {
      setError(decodeURIComponent(errorParam));
    }
  }, [searchParams]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setInfoMessage(null);
    const formData = new FormData(event.currentTarget);
    const redirectUrl = searchParams.get('redirect_url');

    startTransition(async () => {
      try {
        // signInWithEmailAndPassword action'ı başarılı olursa yönlendirme yapar,
        // hata olursa exception fırlatır.
        await signInWithEmailAndPassword(formData, redirectUrl);
        // Başarılı durumda yönlendirme action içinde olacak.
        // Eğer buraya gelinirse ve yönlendirme olmadıysa (ki olmamalı), bir fallback.
        // router.push(redirectUrl || '/(dashboard)');
      } catch (e: any) {
        setError(e.message || "Giriş sırasında bir hata oluştu.");
      }
    });
  };

  return (
    <div style={{ maxWidth: '400px', margin: '50px auto', padding: '20px', border: '1px solid #ccc', borderRadius: '8px' }}>
      <h1>Giriş Yap</h1>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="email">E-posta:</label>
          <input type="email" id="email" name="email" required style={{ width: '100%', padding: '8px', boxSizing: 'border-box' }} />
        </div>
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="password">Şifre:</label>
          <input type="password" id="password" name="password" required style={{ width: '100%', padding: '8px', boxSizing: 'border-box' }} />
        </div>
        {error && <p style={{ color: 'red' }}>{error}</p>}
        {infoMessage && <p style={{ color: 'blue' }}>{infoMessage}</p>}
        <button type="submit" disabled={isPending} style={{ padding: '10px 20px', cursor: 'pointer' }}>
          {isPending ? "Giriş Yapılıyor..." : "Giriş Yap"}
        </button>
      </form>
       <p style={{marginTop: '10px'}}>
        Hesabınız yok mu? <a href="/sign-up">Kayıt Olun</a>
      </p>
    </div>
  );
}
