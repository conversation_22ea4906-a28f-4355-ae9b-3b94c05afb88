{"name": "almancaabc", "version": "0.1.0", "private": true, "scripts": {"dev": "bun --bun run next dev", "build": "bun --bun run next build", "analyze": "ANALYZE=true bun --bun run next build", "start": "bun --bun run next start", "lint": "bun --bun run next lint", "postbuild": "bun --bun run next-sitemap"}, "dependencies": {"@anthropic-ai/claude-code": "^1.0.31", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "flag-icons": "^7.5.0", "framer-motion": "^12.18.1", "input-otp": "^1.4.2", "lucide-react": "^0.522.0", "next": "^15.3.4", "next-sitemap": "^4.2.3", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-icons": "^5.5.0", "react-player": "^2.16.0", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.4", "resend": "^4.6.0", "sonner": "^2.0.5", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@next/bundle-analyzer": "^15.3.4", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.29.0", "eslint-config-next": "^15.3.4", "prisma": "^6.10.1", "tailwindcss": "^4.1.10", "typescript": "^5.8.3"}, "prisma": {"seed": "bun run prisma/seed.ts"}}