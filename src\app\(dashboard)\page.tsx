// src/app/(dashboard)/page.tsx
"use client"; // Hook kullanımı için

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
// import { useAuth, useUser } from '@clerk/nextjs'; // Clerk kaldırıldı
import { Loader2 } from 'lucide-react'; // Yükleme ikonu

// Tip tanımı
type Role = 'admin' | 'teacher' | 'student' | 'parent' | string | null | undefined;

export default function DashboardPage() {
  const router = useRouter();
  // const { isLoaded, isSignedIn, orgRole } = useAuth(); // Clerk kaldırıldı
  // const { user } = useUser(); // Clerk kaldırıldı

  // Clerk kaldırıldığı için isLoaded ve isSignedIn doğrudan kullanılamaz.
  // DEV_SKIP_AUTH_MIDDLEWARE durumuna göre bir mantık oluşturulacak.
  const isDevSkipAuth = process.env.NEXT_PUBLIC_DEV_SKIP_AUTH_MIDDLEWARE === "true" || process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true";


  useEffect(() => {
    // console.log("DashboardPage: DEV_SKIP_AUTH_MIDDLEWARE =", process.env.NEXT_PUBLIC_DEV_SKIP_AUTH_MIDDLEWARE || process.env.DEV_SKIP_AUTH_MIDDLEWARE);
    if (isDevSkipAuth) {
      // Geliştirme modunda ve kimlik doğrulama atlanıyorsa, varsayılan bir role göre yönlendir.
      // Bu rol, test senaryosuna göre ayarlanabilir. Şimdilik 'admin' varsayalım.
      const devRole: Role = 'admin'; // Test için varsayılan rol
      console.log("DEV_SKIP_AUTH_MIDDLEWARE: DashboardPage - Redirecting based on devRole:", devRole);

      switch (devRole) {
        case 'admin':
          router.replace('/admin');
          break;
        case 'teacher':
          router.replace('/teacher');
          break;
        case 'student':
          router.replace('/student');
          break;
        case 'parent':
          router.replace('/parent');
          break;
        default:
          console.warn("DEV_SKIP_AUTH_MIDDLEWARE: Unknown devRole, redirecting to / as default.");
          router.replace('/');
          break;
      }
    } else {
      // Kimlik doğrulama atlanmıyorsa (production veya Clerk aktifken),
      // ve Clerk hook'ları artık yoksa, kullanıcıyı ana sayfaya yönlendir.
      // Gerçek bir kimlik doğrulama sistemi entegre edildiğinde bu mantık güncellenmeli.
      console.log("DashboardPage: No active session or DEV_SKIP_AUTH_MIDDLEWARE is false, redirecting to /");
      router.replace('/');
    }
  }, [router, isDevSkipAuth]);

  // Yönlendirme sırasında her zaman bir yükleme göstergesi göster
  return (
    <div className="flex h-screen items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
      <p className="ml-2">Yönlendiriliyorsunuz...</p>
    </div>
  );
}