'use server';

import prisma from '@/lib/prisma';
import { Prisma } from '@prisma/client'; // Decimal tipi için

interface TeacherSearchResult {
  id: string;
  firstName: string | null;
  lastName: string | null;
  title: string | null;
  profile_image_url: string | null;
  country: string | null;
  average_rating: number | null;
  hourly_rate: Prisma.Decimal | null; // Prisma.Decimal olarak güncellendi
  specializations: string[];
  languages: string[];
  is_verified: boolean;
  badges: string[]; // badges eklendi
  // reviewCount için _count ekleyebiliriz veya Teacher modelinde tutabiliriz.
  // Şimdilik TeacherCard tarafında varsayılan 0 kullanılacak.
}

export async function searchTeachersAction(query: string): Promise<TeacherSearchResult[]> {
  if (!query || query.trim() === "") {
    return [];
  }

  const searchTerm = query.trim();

  try {
    const teachers = await prisma.teacher.findMany({
      where: {
        AND: [
          { is_approved: true }, // <PERSON><PERSON>e onaylı öğretmenler
          { is_visible: true },  // Sadece görünür öğretmenler
        ],
        OR: [
          {
            firstName: {
              contains: searchTerm,
              mode: 'insensitive', // Büyük/küçük harf duyarsız
            },
          },
          {
            lastName: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
          {
            title: { // Öğretmen unvanı/başlığı
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
          {
            bio: { // Bio alanında da arama
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
          // Dizi alanlarında arama için 'has' kullanılabilir ancak bu tam eşleşme gerektirir.
          // Daha esnek bir arama için (örneğin 'Alman' içeren 'Almanca Öğretmenliği' gibi),
          // bu alanların veritabanında full-text search için ayrıca indekslenmesi veya
          // sorgunun daha karmaşık hale getirilmesi gerekebilir.
          // Şimdilik temel metin alanlarına odaklanıyoruz.
        ],
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        title: true,
        profile_image_url: true,
        country: true,
        average_rating: true,
        hourly_rate: true,
        specializations: true,
        languages: true,
        is_verified: true,
        badges: true,
        // availabilitySlots ilişkisi üzerinden müsaitlik bilgisi çekilebilir ama bu sorguyu karmaşıklaştırır.
        // Şimdilik TeacherCard'a boş dizi geçeceğiz.
      },
      take: 25, // Sonuç sayısını sınırla
    });
    return teachers.map(teacher => ({
      ...teacher,
      hourly_rate: teacher.hourly_rate || new Prisma.Decimal(0),
      average_rating: teacher.average_rating || 0,
    }));
  } catch (error) {
    // Removed console.error - return empty array for error cases
    return [];
  }
}