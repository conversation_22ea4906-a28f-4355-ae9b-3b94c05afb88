import React from 'react';
import { Credit<PERSON>ard, CheckCircle, Clock, Star, Zap, Target, Users, Video, TrendingUp } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Teacher, ClientCoursePackage as Package, PackageOption } from '@/types/teacher';

interface TeacherCoursesTabProps {
  teacher: Teacher;
  onPackageSelect: (pkg: Package, option: PackageOption) => void;
}

export const TeacherCoursesTab: React.FC<TeacherCoursesTabProps> = ({ teacher, onPackageSelect }) => {
  const packageIcons: { [key: string]: React.ElementType } = {
    '1': Target,
    'birebir': Target,
    '2': Users,
    'grup': Users,
    '3': Video,
    'video': Video
  };

  const packageColors: { [key: string]: string } = {
    '1': "from-blue-500 to-blue-600",
    'birebir': "from-blue-500 to-blue-600",
    '2': "from-green-500 to-green-600",
    'grup': "from-green-500 to-green-600",
    '3': "from-purple-500 to-purple-600",
    'video': "from-purple-500 to-purple-600"
  };

  const allPackages = [
    ...(teacher.oneOnOnePackages || []),
    ...(teacher.groupCourses || [])
  ];

  return (
    <div className="w-full p-3 sm:p-4 lg:p-6 box-border overflow-x-hidden">
      <div className="mb-4 lg:mb-6 w-full">
        <h2 className="text-xl lg:text-2xl font-bold text-gray-900 mb-2 lg:mb-3">Kurslar ve Paketler</h2>
        <p className="text-sm lg:text-base text-gray-600 w-full lg:max-w-3xl">
          Almanca öğrenme hedeflerinize uygun kurs seçeneklerini keşfedin. Her seviye için özel olarak tasarlanmış programlarımızla hızlı ve etkili öğrenin.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6 w-full">
        {allPackages.map(pkg => {
          const IconComponent = packageIcons[pkg.type?.toLowerCase() || pkg.id.toString()] || Target;
          const gradientClass = packageColors[pkg.type?.toLowerCase() || pkg.id.toString()] || "from-gray-500 to-gray-600";
          
          return (
            <div key={pkg.id} className="group w-full">
              <div className="bg-white border-2 border-gray-200 rounded-xl p-4 lg:p-5 hover:border-gray-300 hover:shadow-lg transition-all duration-300 relative h-full flex flex-col w-full box-border">
                <div className="text-center mb-3 lg:mb-4">
                  <div className={`w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-br ${gradientClass} rounded-xl flex items-center justify-center mx-auto mb-2 lg:mb-3 shadow-lg`}>
                    <IconComponent className="w-5 h-5 lg:w-6 lg:h-6 text-white" />
                  </div>
                  <h3 className="text-lg lg:text-xl font-bold text-gray-900 mb-1">{pkg.name}</h3>
                  <p className="text-gray-600 text-xs lg:text-sm">{pkg.shortDesc}</p>
                </div>

                <p className="text-gray-700 text-center mb-3 lg:mb-4 flex-grow text-sm">{pkg.description}</p>

                {pkg.features && pkg.features.length > 0 && (
                  <div className="mb-4 lg:mb-6">
                    <h4 className="font-semibold text-gray-900 mb-2 lg:mb-3 text-sm">Bu paket şunları içerir:</h4>
                    <ul className="space-y-1.5 lg:space-y-2">
                      {pkg.features.map((feature, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <CheckCircle className="w-3.5 h-3.5 lg:w-4 lg:h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700" style={{ fontSize: '13px' }}>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="mt-auto">
                  {pkg.options && pkg.options.length > 0 ? (
                    <div className="space-y-2 lg:space-y-3 w-full">
                      {pkg.options.map((option: PackageOption, index: number) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-3 hover:border-gray-300 transition-colors w-full">
                          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-2 gap-2">
                            <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                              <span className="font-bold text-gray-900 text-sm">{option.sessions || option.name} Ders</span>
                              {option.duration && (
                                <div className="flex items-center gap-1 text-gray-500">
                                  <Clock className="w-3 h-3" />
                                  <span className="text-xs">{option.duration}</span>
                                </div>
                              )}
                            </div>
                            {option.discount && (
                              <Badge className="bg-red-100 text-red-700 text-xs self-start sm:self-center">{option.discount}</Badge>
                            )}
                          </div>
                          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                            <div className="flex flex-col sm:flex-row sm:items-center gap-1">
                              <span className="text-lg font-bold text-gray-900">₺{option.price}</span>
                              {option.perLesson && <span className="text-xs text-gray-500">(₺{option.perLesson}/ders)</span>}
                            </div>
                            <Button
                              onClick={() => onPackageSelect(pkg, option)}
                              className={`bg-gradient-to-r ${gradientClass} hover:opacity-90 text-white shadow-md hover:shadow-lg transition-all duration-300 text-xs px-3 py-2 w-full sm:w-auto`}
                            >
                              <CreditCard className="w-3 h-3 mr-1" />
                              Seç
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="border border-gray-200 rounded-lg p-3 w-full">
                       <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                          <span className="text-lg font-bold text-gray-900">₺{pkg.price}</span>
                          <Button
                            onClick={() => onPackageSelect(pkg, { price: pkg.price })}
                            className={`bg-gradient-to-r ${gradientClass} hover:opacity-90 text-white shadow-md hover:shadow-lg transition-all duration-300 text-xs px-3 py-2 w-full sm:w-auto`}
                          >
                            <CreditCard className="w-3 h-3 mr-1" />
                            Seç
                          </Button>
                        </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 lg:gap-4 mt-6 lg:mt-8 mb-6 lg:mb-8 w-full">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-3 lg:p-4 rounded-xl border border-blue-200 flex flex-col h-full">
          <div className="flex items-center gap-3 flex-grow">
            <div className="w-8 h-8 lg:w-10 lg:h-10 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
              <Zap className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
            </div>
            <div>
              <h3 className="font-bold text-blue-900 text-sm lg:text-base">Hızlı İlerleme</h3>
              <p className="text-blue-700 text-xs lg:text-sm">Yapılandırılmış öğretim metodları</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-green-50 to-green-100 p-3 lg:p-4 rounded-xl border border-green-200 flex flex-col h-full">
          <div className="flex items-center gap-3 flex-grow">
            <div className="w-8 h-8 lg:w-10 lg:h-10 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
              <Star className="w-4 h-4 lg:w-5 lg:h-5 text-white fill-current" />
            </div>
            <div>
              <h3 className="font-bold text-green-900 text-sm lg:text-base">%95 Başarı Oranı</h3>
              <p className="text-green-700 text-xs lg:text-sm">Kanıtlanmış sonuçlar</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-3 lg:p-4 rounded-xl border border-purple-200 lg:col-span-1 flex flex-col h-full">
          <div className="flex items-center gap-3 flex-grow">
            <div className="w-8 h-8 lg:w-10 lg:h-10 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
              <TrendingUp className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
            </div>
            <div>
              <h3 className="font-bold text-purple-900 text-sm lg:text-base">Esnek Program</h3>
              <p className="text-purple-700 text-xs lg:text-sm">Size uygun saatlerde</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-4 lg:p-6 text-white text-center">
        <h3 className="text-lg lg:text-xl font-bold mb-2 lg:mb-3">Hangi kursun size uygun olduğundan emin değil misiniz?</h3>
        <p className="text-blue-100 mb-3 lg:mb-4 text-sm">15 dakikalık ücretsiz danışmanlık seansında size en uygun programı belirleyelim.</p>
        <Button className="bg-white text-blue-600 hover:bg-gray-100 font-semibold px-4 lg:px-6 py-2 text-sm">
          Ücretsiz Danışmanlık Al
        </Button>
      </div>
    </div>
  );
};
