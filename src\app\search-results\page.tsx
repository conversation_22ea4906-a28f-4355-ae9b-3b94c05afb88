// src/app/search-results/page.tsx
import { searchTeachersAction } from "@/lib/actions/search.actions"; // .ts uzantısı kaldırıldı
import { TeacherCard } from "@/components/TeacherCard";
import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import type { Teacher } from "@/types/teacher";

async function SearchResultsList({ query }: { query: string }) {
  if (!query) {
    return <p className="text-center text-muted-foreground">Lütfen aramak için bir terim girin.</p>;
  }

  const searchResults = await searchTeachersAction(query);

  if (!searchResults || searchResults.length === 0) {
    // ESLint hatası için " kullanıldı
    return <p className="text-center text-muted-foreground">"{query}" ile eşleşen öğretmen bulunamadı.</p>;
  }

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {searchResults.map((teacherResult) => {
        const cardTeacher: Teacher & { title?: string | undefined } = { // title tipi güncellendi
          id: teacherResult.id,
          // clerkId kaldırıldı, Teacher tipinde yok ve id yeterli.
          name: `${teacherResult.firstName || ''} ${teacherResult.lastName || ''}`.trim(),
          firstName: teacherResult.firstName || '',
          lastName: teacherResult.lastName || '',
          title: teacherResult.title ?? undefined, // null ise undefined ata
          bio: '', 
          specializations: teacherResult.specializations || [], 
          levels: [], 
          hourly_rate: teacherResult.hourly_rate ? parseFloat(teacherResult.hourly_rate.toString()) : 0, 
          profile_image_url: teacherResult.profile_image_url || "/logo.png", 
          intro_video_url: null, 
          country: teacherResult.country || "Bilinmiyor",
          city: null, 
          languages: teacherResult.languages || [], 
          is_approved: true, 
          is_verified: teacherResult.is_verified || false, 
          average_rating: teacherResult.average_rating || 0, 
          created_at: new Date(), 
          updated_at: new Date(), 
          education: null,
          experience: null,
          certificates: null,
          stats: null,
          // TeacherCard'ın kullandığı proplar için eşleştirmeler:
          avatar: teacherResult.profile_image_url || "/logo.png",
          specialties: teacherResult.specializations || [],
          price: teacherResult.hourly_rate ? parseFloat(teacherResult.hourly_rate.toString()) : 0,
          rating: teacherResult.average_rating || 0,
          verified: teacherResult.is_verified || false,
          // TeacherCard'ın beklediği diğer alanlar (eğer varsa ve action'dan gelmiyorsa varsayılan)
          reviewCount: 0, 
          badges: teacherResult.badges || [], 
          availability: [], 
        };

        return (
          <TeacherCard
            key={teacherResult.id}
            teacher={cardTeacher}
          />
        );
      })}
    </div>
  );
}

function SearchResultsSkeleton() {
  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {[...Array(8)].map((_, i) => (
        <div key={i} className="flex flex-col space-y-3">
          <Skeleton className="h-[125px] w-full rounded-xl" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-[200px]" />
            <Skeleton className="h-4 w-[150px]" />
          </div>
        </div>
      ))}
    </div>
  );
}

export default function SearchResultsPage({
  searchParams,
}: {
  searchParams?: {
    q?: string;
  };
}) {
  const query = searchParams?.q || "";

  return (
    <div className="container py-8">
      <h1 className="mb-6 text-3xl font-bold">
        {/* ESLint hatası için " kullanıldı */}
        Arama Sonuçları {query && <span className="text-primary">"{query}"</span>}
      </h1>
      <Suspense fallback={<SearchResultsSkeleton />}>
        <SearchResultsList query={query} />
      </Suspense>
    </div>
  );
}