"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
// import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar" // Ma<PERSON><PERSON>st<PERSON> görünümü kaldırıldığı için gereksiz
import { Clock, Heart } from "lucide-react" // Calendar, Star kaldırıldı
import type { TeacherProfile } from "@/types/teacher"
// import { Badge } from "@/components/ui/badge" // Masaüstü görünümü kaldırıldığı için gereksiz
import { useMediaQuery } from '@/hooks/useMediaQuery';
// import { format } from "date-fns" // Artık kullanılmıyor
// import { tr } from "date-fns/locale" // Artık kullanılmıyor
// import { VerifiedBadge } from "@/components/VerifiedBadge"; // Ma<PERSON><PERSON><PERSON><PERSON> görün<PERSON><PERSON><PERSON> kaldırıldığ<PERSON> için gereksiz

interface TeacherBookingBarProps {
  teacher: TeacherProfile
  className?: string
}

export function TeacherBookingBar({ teacher, className }: TeacherBookingBarProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isFavorited, setIsFavorited] = useState(false)

  const isMobile = useMediaQuery("(max-width: 768px)");

  useEffect(() => {
    // Sadece mobil ise görünür yap
    setIsVisible(isMobile);
  }, [isMobile]); // Sadece isMobile değiştiğinde çalıştır

  // getNextAvailableSlot fonksiyonu kaldırıldı, artık kullanılmıyor

  return (
    <AnimatePresence>
      {/* isVisible kontrolü artık sadece mobil durumu kontrol ediyor */}
      {isVisible && (
        <motion.div
          initial={{ y: 100 }} // Sadece alttan gelme animasyonu
          animate={{ y: 0 }}
          exit={{ y: 100 }}
          transition={{ type: "tween", duration: 0.3 }}
          className={`
            fixed w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm
            shadow-lg z-50
            bottom-0 left-0 border-t {/* Sadece altta sabit */}
            ${className}
          `}
        >
          <div className="container mx-auto px-4">
            {/* Sadece mobil görünüm kaldı */}
            <div className="flex items-center justify-between h-20">
               {/* Fiyat */}
               <div className="text-left"> {/* Hizalama sola alındı */}
                  <div className="text-xl font-bold text-primary">{teacher.hourlyRate}₺</div>
                  <div className="text-xs text-muted-foreground">/ders</div>
               </div>

               {/* Butonlar */}
               <div className="flex items-center gap-2"> {/* flex-grow kaldırıldı */}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsFavorited(!isFavorited)}
                    className="text-muted-foreground hover:text-primary"
                    aria-label="Favorilere Ekle/Çıkar"
                  >
                    <Heart
                      className={`w-5 h-5 transition-colors ${isFavorited ? "fill-red-500 text-red-500" : ""}`}
                    />
                  </Button>
                  <Button
                    size="lg"
                    className="bg-[#FF6200] hover:bg-[#e55800] text-white font-medium px-4 flex-grow" // flex-grow eklendi
                    onClick={() => {
                      const scheduleElement = document.getElementById('teacher-schedule-card');
                      scheduleElement?.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }}
                  >
                    <Clock className="w-5 h-5 mr-2" />
                    Ders Planla
                  </Button>
               </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}