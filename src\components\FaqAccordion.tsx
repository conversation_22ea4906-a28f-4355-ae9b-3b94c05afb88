"use client";

import React, { useState, useMemo } from 'react';
import { Search, HelpCircle, ChevronsUpDown, ChevronsDownUp } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import type { FaqItem } from '@/lib/data/faq-data'; // Merkezi FaqItem tipini import ediyoruz

interface FaqAccordionProps {
  items: FaqItem[];
  title?: string; // title prop'u eklendi
  subtitle?: string;
}

const FaqAccordion: React.FC<FaqAccordionProps> = ({ items = [], title = "Sıkça Sorulan Sorular", subtitle = "Merak ettiğiniz soruların cevaplarını burada bulabilirsiniz." }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [openItems, setOpenItems] = useState<string[]>([]);

  const filteredFaqs = useMemo(() => items.filter(faq =>
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
  ), [searchTerm, items]);

  const allItemValues = useMemo(() => filteredFaqs.map(faq => `item-${faq.id}`), [filteredFaqs]);

  const handleToggleAll = (open: boolean) => {
    if (open) {
      setOpenItems(allItemValues);
    } else {
      setOpenItems([]);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto shadow-xl dark:bg-gray-800">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <HelpCircle className="h-12 w-12 text-blue-600 dark:text-blue-400" />
        </div>
        <CardTitle className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400">
          {title}
        </CardTitle>
        <p className="text-muted-foreground dark:text-gray-300 mb-6">
          {subtitle}
        </p>
        <div className="relative max-w-lg mx-auto mb-4">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground dark:text-gray-400" />
          <Input
            type="text"
            placeholder="Sorularda ara..."
            className="w-full pl-10 pr-4 py-3 rounded-full border-border focus:ring-2 focus:ring-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100 dark:focus:ring-blue-400"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        {filteredFaqs.length > 0 && (
          <div className="flex justify-center space-x-3 mb-6">
            <Button variant="outline" size="sm" onClick={() => handleToggleAll(true)} disabled={openItems.length === allItemValues.length}>
              <ChevronsUpDown className="mr-2 h-4 w-4" /> Tümünü Aç
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleToggleAll(false)} disabled={openItems.length === 0}>
              <ChevronsDownUp className="mr-2 h-4 w-4" /> Tümünü Kapat
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent>
        {filteredFaqs.length > 0 ? (
          <Accordion
            type="multiple"
            className="w-full space-y-3"
            value={openItems}
            onValueChange={setOpenItems}
          >
            {filteredFaqs.map((faq) => (
              <AccordionItem value={`item-${faq.id}`} key={faq.id} className="bg-white dark:bg-gray-700/50 rounded-xl shadow-lg overflow-hidden transition-all duration-300 ease-in-out hover:shadow-2xl border border-slate-200 dark:border-gray-700">
                <AccordionTrigger className="w-full flex justify-between items-center p-6 text-left hover:bg-slate-50 dark:hover:bg-gray-600/50 transition-colors duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-sky-500 focus-visible:ring-opacity-75 hover:no-underline">
                  <span className="text-lg font-semibold text-slate-800 dark:text-gray-100 pr-4">{faq.question}</span>
                </AccordionTrigger>
                <AccordionContent className="overflow-hidden transition-max-height duration-700 ease-in-out">
                  <div
                    className="px-6 pb-6 pt-2 text-slate-600 dark:text-gray-300 leading-relaxed border-t border-slate-200 dark:border-gray-600"
                    dangerouslySetInnerHTML={{ __html: faq.answer }}
                  />
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        ) : (
          <div className="text-center py-10">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted dark:bg-gray-700 mb-4">
              <Search className="h-8 w-8 text-muted-foreground dark:text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-foreground dark:text-gray-100 mb-2">Aramanızla Eşleşen Soru Bulunamadı</h3>
            <p className="text-muted-foreground dark:text-gray-300">
              Lütfen farklı anahtar kelimelerle tekrar deneyin veya destek ekibimizle iletişime geçin.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FaqAccordion;