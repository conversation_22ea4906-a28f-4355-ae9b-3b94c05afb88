"use client"

import * as React from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Facebook, Instagram, Twitter, Youtube, Linkedin, MessageSquare, Send } from "lucide-react"
import { sendNewsletterEmail } from "@/lib/actions/email.actions"
import { toast } from "sonner"

const socialLinks = [
  {
    name: "WhatsApp Kanalı",
    icon: <MessageSquare className="h-5 w-5 text-gray-400 hover:text-[#FF6200] transition-colors" />,
    href: "https://whatsapp.com/channel/0029Va7U3OEIyPtT3ljHfm2c",
  },
  { 
    name: "Facebook", 
    icon: <Facebook className="h-5 w-5 text-gray-400 hover:text-[#FF6200] transition-colors" />, 
    href: "https://facebook.com/almancaabc" 
  },
  { 
    name: "Instagram", 
    icon: <Instagram className="h-5 w-5 text-gray-400 hover:text-[#FF6200] transition-colors" />, 
    href: "https://instagram.com/almancaabc" 
  },
  { 
    name: "Twitter", 
    icon: <Twitter className="h-5 w-5 text-gray-400 hover:text-[#FF6200] transition-colors" />, 
    href: "https://twitter.com/almancaabc" 
  },
  { 
    name: "Youtube", 
    icon: <Youtube className="h-5 w-5 text-gray-400 hover:text-[#FF6200] transition-colors" />, 
    href: "https://youtube.com/almancaabc" 
  },
  { 
    name: "LinkedIn", 
    icon: <Linkedin className="h-5 w-5 text-gray-400 hover:text-[#FF6200] transition-colors" />, 
    href: "https://linkedin.com/company/almancaabc/" 
  },
]

const quickLinks = [
  { name: "Anasayfa", href: "/" },
  { name: "Kurslar", href: "/kurslar" },
  { name: "Öğretmenler", href: "/teachers" },
  { name: "Blog", href: "/blog" }, // Blog linki varsa kalabilir, yoksa kaldırılabilir.
  { name: "Hakkımızda", href: "/hakkimizda" },
  { name: "İletişim", href: "/iletisim" },
  { name: "Yardım Merkezi", href: "/yardim" }, // Bu doğru kalacak
  { name: "SSS", href: "/sss" }, // Yeni URL
]

const legalLinks = [
  { name: "Gizlilik Politikası", href: "/gizlilik-politikasi" }, // Yeni URL
  { name: "Kullanım Koşulları", href: "/kullanim-kosullari" }, // Yeni URL
  { name: "Üyelik Sözleşmesi", href: "/uyelik-sozlesmesi" }, // Yeni URL
  { name: "KVKK Aydınlatma Metni", href: "/kvkk-aydinlatma-metni" }, // Yeni URL
]

export function Footer() {
  const [email, setEmail] = React.useState('')
  const [previousEmails, setPreviousEmails] = React.useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = React.useState(false)
  const [isSubmitting, setIsSubmitting] = React.useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    try {
      const result = await sendNewsletterEmail(email)
      if (result.success) {
        toast.success('Aboneliğiniz başarıyla oluşturuldu!', {
          position: 'bottom-center',
        })
        setPreviousEmails(prev => [...new Set([email, ...prev])])
        setEmail('')
      } else {
        toast.error(result.error || 'Bir hata oluştu', {
          position: 'bottom-center',
        })
      }
    } catch (_error) {
      // console.error("Newsletter subscription error:", _error); - REMOVED
      toast.error('Beklenmeyen bir hata oluştu', {
        position: 'bottom-center',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSuggestionClick = (suggestedEmail: string) => {
    setEmail(suggestedEmail)
    setShowSuggestions(false)
  }

  return (
    (<footer className="bg-gray-950 text-gray-300 border-t border-gray-800">
      <div className="container mx-auto px-4 py-12">
        <div className="grid gap-12 md:grid-cols-2 lg:grid-cols-4">
          {/* Logo ve Açıklama */}
          <div className="space-y-6">
            <Link href="/" className="inline-block">
              <span className="text-2xl font-bold bg-gradient-to-r from-[#FF6200] to-amber-500 bg-clip-text text-transparent">
                AlmancaABC
              </span>
            </Link>
            <p className="text-gray-400">
              Almanca öğrenmek ABC kadar kolay! Türkiye&apos;nin ve Almanya&apos;nın lider online Almanca eğitim platformu.
            </p>
            
            {/* Abonelik Formu */}
            <div className="space-y-2 relative">
              <h3 className="text-sm font-medium text-white">Yeniliklerden haberdar olun</h3>
              <form 
                className="flex gap-2" 
                autoComplete="on"
                onSubmit={handleSubmit}
              >
                <input
                  type="email"
                  name="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  onFocus={() => setShowSuggestions(true)}
                  onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                  placeholder="E-posta adresiniz"
                  className="flex-1 px-3 py-2 text-sm rounded-md border border-gray-700 bg-gray-900 focus:outline-none focus:ring-2 focus:ring-[#FF6200] focus:border-transparent"
                  required
                />
                {showSuggestions && previousEmails.length > 0 && (
                  <div className="absolute z-10 mt-1 w-full bg-gray-900 border border-gray-700 rounded-md shadow-lg">
                    {previousEmails.map((emailItem) => ( // email -> emailItem olarak değiştirildi, state ile karışmaması için
                      (<div 
                        key={emailItem} 
                        className="px-3 py-2 text-sm text-gray-300 hover:bg-gray-800 cursor-pointer"
                        onClick={() => handleSuggestionClick(emailItem)}
                      >
                        {emailItem}
                      </div>)
                    ))}
                  </div>
                )}
                <Button 
                  type="submit" 
                  className="bg-[#FF6200] hover:bg-[#FF6200]/90 h-auto py-2"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Gönderiliyor...' : <Send className="h-3 w-3" />}
                </Button>
              </form>
            </div>
          </div>
          
          {/* Hızlı Linkler */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">
              Hızlı Linkler
            </h3>
            <nav className="space-y-2">
              {quickLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="block text-gray-400 hover:text-[#FF6200] transition-colors"
                  // Eğer blog linki harici bir siteye gidiyorsa yeni sekmede aç
                  target={link.name === "Blog" && link.href.startsWith("http") ? "_blank" : undefined}
                  rel={link.name === "Blog" && link.href.startsWith("http") ? "noopener noreferrer" : undefined}>
                  {link.name}
                </Link>
              ))}
            </nav>
          </div>
          
          {/* Yasal Linkler */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">
              Yasal
            </h3>
            <nav className="space-y-2">
              {legalLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="block text-gray-400 hover:text-[#FF6200] transition-colors">
                  {link.name}
                </Link>
              ))}
            </nav>
          </div>
          
          {/* İletişim Bilgileri */}
          <div className="space-y-4">
           <h3 className="text-lg font-semibold text-white">
             Bize Ulaşın
           </h3>
           <div className="text-gray-400 space-y-1">
             <p>AlmancaABC</p>
             <p>Danziger Str. 2</p>
             <p>63739 Aschaffenburg</p>
             <p>Almanya</p>
             {/* Opsiyonel:
             <p className="mt-2">
               <a href="tel:+491784441011" className="hover:text-[#FF6200] transition-colors">P: +49 ************</a>
             </p>
             <p>
               <a href="mailto:<EMAIL>" className="hover:text-[#FF6200] transition-colors">E: <EMAIL></a>
             </p>
             */}
           </div>
          </div>

          {/* Sosyal Medya */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">
              Bizi Takip Edin
            </h3>
            <div className="flex flex-wrap gap-3">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  title={social.name}
                  className="group flex items-center justify-center h-10 w-10 rounded-full bg-gray-900 hover:bg-[#FF6200]/10 text-gray-400 hover:text-[#FF6200] border border-gray-800 transition-all hover:border-[#FF6200]/50"
                >
                  <span className="group-hover:scale-110 transition-transform">
                    {social.icon}
                  </span>
                  <span className="sr-only">{social.name}</span>
                </a>
              ))}
            </div>
          </div>
        </div>
        
        {/* Alt Kısım - Telif Hakkı */}
        <div className="mt-12 pt-6 border-t border-gray-800">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-gray-500 text-sm">
              &copy; {new Date().getFullYear()} AlmancaABC. Tüm hakları saklıdır.
            </p>
            <div className="flex flex-wrap gap-4">
              {legalLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="text-gray-500 hover:text-[#FF6200] text-sm transition-colors">
                  {link.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>)
  );
}

export default Footer
