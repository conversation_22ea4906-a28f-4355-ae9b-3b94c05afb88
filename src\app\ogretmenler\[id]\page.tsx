// src/app/ogretmenler/[id]/page.tsx
export const revalidate = 3600; // 1 saat

import React from 'react';
import { notFound } from 'next/navigation';
import { getTeacherProfileData } from "@/lib/actions/teacher.actions";
import TeacherProfileClient from '@/components/teacher/TeacherProfileClient';
import { Prisma } from '@prisma/client';

// JSON-LD Schema tipleri ve fonksiyonları buraya eklenebilir veya ayrı bir dosyadan import edilebilir.
// Şimdilik basit tutmak adına bu dosyada bırakıyorum.
// ... (Schema.org tipleri ve generateTeacherSchema fonksiyonu burada yer alacak)


// export async function generateMetadata(
//   { params }: TeacherProfilePageProps
// ): Promise<Metadata> {
//   const teacher = await getTeacherProfileData(params.id);

//   if (!teacher) {
//     return {
//       title: "Öğretmen Bulunamadı | AlmancaABC",
//       description: "Aradığınız öğretmen profili bulunamadı veya henüz onaylanmamış.",
//     };
//   }
  
//   const teacherName = teacher.name || `${teacher.firstName} ${teacher.lastName}`;
//   const description = teacher.shortBio || `AlmancaABC platformunda ${teacherName} ile online Almanca öğrenin.`;
//   const imageUrl = teacher.avatar || '/placeholder.svg';

//   return {
//     title: `${teacherName} - Online Almanca Öğretmeni`,
//     description: description,
//     openGraph: {
//       title: `${teacherName} - Online Almanca Öğretmeni`,
//       description: description,
//       images: [imageUrl],
//     },
//   };
// }

const TeacherProfilePage = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const teacherData = await getTeacherProfileData(id);

  if (!teacherData) {
    notFound();
  }

  // TeacherProfileClient'in beklediği prop'ları hazırlayalım.
  // getTeacherProfileData'dan gelen veriyi TeacherProfileClientData tipine dönüştürelim.
  const teacherForClient = {
    id: teacherData.id,
    name: teacherData.name || `${teacherData.firstName} ${teacherData.lastName}`,
    firstName: teacherData.firstName,
    lastName: teacherData.lastName,
    email: teacherData.email || undefined,
    avatar: teacherData.avatar || undefined,
    bio: teacherData.bio || undefined,
    title: teacherData.title || undefined,
    shortBio: teacherData.shortBio || '',
    specializations: teacherData.specializations,
    levels: teacherData.levels,
    hourly_rate: teacherData.hourly_rate,
    intro_video_url: teacherData.intro_video_url || undefined,
    country: teacherData.country || undefined,
    city: teacherData.city || undefined,
    nativeLanguage: teacherData.nativeLanguage || undefined,    languages: teacherData.languages,
    isVerified: teacherData.isVerified,
    isOnline: true, // Şimdilik sabit olarak true, daha sonra gerçek veri ile değiştirilecek
    experienceYears: teacherData.experienceYears?.toString() || undefined,
    average_rating: teacherData.average_rating,
    totalReviews: teacherData.reviewCount,
    activeStudentCount: teacherData.activeStudentCount || 0,
    completedLessons: teacherData.totalLessons,
    timezone: teacherData.timezone,
    created_at: teacherData.created_at.toISOString(),
    updated_at: teacherData.updated_at.toISOString(),
    education: teacherData.education,
    certificates: teacherData.certificates,
    reviews: teacherData.reviewsReceived.map(review => ({
      id: review.id,
      rating: review.rating,
      comment: review.comment || undefined,
      date: review.createdAt.toISOString(),
      student: {
        id: review.student.id,
        name: `${review.student.firstName || ''} ${review.student.lastName || ''}`.trim() || 'Bir Öğrenci',
        avatar: review.student.profile_image_url || null,
      },
    })),
    lessonPackages: teacherData.lessonPackages.map(pkg => ({
      ...pkg,
      description: pkg.description || undefined,
      level: pkg.level || undefined,
      thumbnailUrl: pkg.thumbnailUrl || undefined,
      createdAt: pkg.createdAt.toISOString(),
      updatedAt: pkg.updatedAt.toISOString(),
      price: new Prisma.Decimal(pkg.price).toNumber(),
      pricePerLessonCalculated: pkg.pricePerLessonCalculated ? new Prisma.Decimal(pkg.pricePerLessonCalculated).toNumber() : null,
      groupCourseStartDate: pkg.groupCourseStartDate ? pkg.groupCourseStartDate.toISOString() : null,
      studentCapacity: pkg.studentCapacity || null,
      groupCourseSchedule: pkg.groupCourseSchedule || null,
      videoCourseTotalHours: pkg.videoCourseTotalHours || null,
      videoCourseIsPopular: pkg.videoCourseIsPopular || null,
      videoCourseMaterials: pkg.videoCourseMaterials || null,
    })),
    oneOnOnePackages: teacherData.oneOnOnePackages.map(pkg => ({
      ...pkg,
      description: pkg.description || undefined,
      level: pkg.level || undefined,
      thumbnailUrl: pkg.thumbnailUrl || undefined,
      createdAt: pkg.createdAt.toISOString(),
      updatedAt: pkg.updatedAt.toISOString(),
      price: new Prisma.Decimal(pkg.price).toNumber(),
      pricePerLessonCalculated: pkg.pricePerLessonCalculated ? new Prisma.Decimal(pkg.pricePerLessonCalculated).toNumber() : null,
      groupCourseStartDate: pkg.groupCourseStartDate ? pkg.groupCourseStartDate.toISOString() : null,
      studentCapacity: pkg.studentCapacity || null,
      groupCourseSchedule: pkg.groupCourseSchedule || null,
      videoCourseTotalHours: pkg.videoCourseTotalHours || null,
      videoCourseIsPopular: pkg.videoCourseIsPopular || null,
      videoCourseMaterials: pkg.videoCourseMaterials || null,
    })),
    groupCourses: teacherData.groupCourses.map(pkg => ({
      ...pkg,
      description: pkg.description || undefined,
      level: pkg.level || undefined,
      thumbnailUrl: pkg.thumbnailUrl || undefined,
      createdAt: pkg.createdAt.toISOString(),
      updatedAt: pkg.updatedAt.toISOString(),
      price: new Prisma.Decimal(pkg.price).toNumber(),
      pricePerLessonCalculated: pkg.pricePerLessonCalculated ? new Prisma.Decimal(pkg.pricePerLessonCalculated).toNumber() : null,
      groupCourseStartDate: pkg.groupCourseStartDate ? pkg.groupCourseStartDate.toISOString() : null,
      studentCapacity: pkg.studentCapacity || null,
      groupCourseSchedule: pkg.groupCourseSchedule || null,
      videoCourseTotalHours: pkg.videoCourseTotalHours || null,
      videoCourseIsPopular: pkg.videoCourseIsPopular || null,
      videoCourseMaterials: pkg.videoCourseMaterials || null,
    })),
    videoCourses: teacherData.videoCourses.map(pkg => ({
      ...pkg,
      description: pkg.description || undefined,
      level: pkg.level || undefined,
      thumbnailUrl: pkg.thumbnailUrl || undefined,
      createdAt: pkg.createdAt.toISOString(),
      updatedAt: pkg.updatedAt.toISOString(),
      price: new Prisma.Decimal(pkg.price).toNumber(),
      pricePerLessonCalculated: pkg.pricePerLessonCalculated ? new Prisma.Decimal(pkg.pricePerLessonCalculated).toNumber() : null,
      groupCourseStartDate: pkg.groupCourseStartDate ? pkg.groupCourseStartDate.toISOString() : null,
      studentCapacity: pkg.studentCapacity || null,
      groupCourseSchedule: pkg.groupCourseSchedule || null,
      videoCourseTotalHours: pkg.videoCourseTotalHours || null,
      videoCourseIsPopular: pkg.videoCourseIsPopular || null,
      videoCourseMaterials: pkg.videoCourseMaterials || null,
    })),
    teacherFaqs: teacherData.teacherFaqs || [],
  };

  return (
    <div className="min-h-screen bg-gray-50" suppressHydrationWarning>
      <React.Suspense fallback={<div>Yükleniyor...</div>}>
        <TeacherProfileClient teacher={teacherForClient} />
        {/* <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(generateTeacherSchema(teacherData)) }}
        /> */}
      </React.Suspense>
    </div>
  );
}

export default TeacherProfilePage;
