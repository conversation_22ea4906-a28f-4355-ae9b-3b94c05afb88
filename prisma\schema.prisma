// prisma/schema.prisma

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL") // Pooled bağ<PERSON>ılar için
}

// Öğretmen modeli
model Teacher {
  id                String   @id // Clerk User ID ile eşleşecek
  firstName         String?
  lastName          String?
  email             String?  @unique // Yeni eklendi
  name              String?  // Yeni eklendi
  avatar            String?  // profile_image_url yerine
  bio               String?  // aboutMe yerine
  title             String?  // Öğretmenin unvanı, örn: "Sertifikalı Almanca Öğretmeni & Dil Bilimci"
  shortBio          String?  // Hero bölümündeki kısa tanıtım metni
  specializations   String[] @default([]) // Uzmanlık alanları, örn: ["Günlük Konuşma Almancası", "İş Almancası"]
  levels            String[] @default([]) // Öğrettiği seviyeler (A1, B2 vb.)
  hourly_rate       Decimal? // Saatlik ders ücreti
  intro_video_url   String?  // YouTube veya benzeri link (Tanıtım videosu için)
  country           String?  // Ülke kodu (örn: "DE", "TR")
  city              String?
  nativeLanguage    String?  // Öğretmenin anadili (örn: "Almanca")
  languages         String[] @default([]) // Konuştuğu diğer diller (örn: ["İngilizce", "Türkçe"])
  is_approved       Boolean  @default(false) // Admin onayı
  isVerified        Boolean  @default(false) // Ek doğrulama (örn: diploma, mavi tik için)
  is_visible        Boolean  @default(true)  // Profilin listelenip listelenmeyeceği
  experienceYears   Int?     // Deneyim yılı (örn: 10)
  average_rating    Float?   @default(0) // Hesaplanan ortalama puan
  totalReviews      Int?     @default(0) // Toplam yorum sayısı
  activeStudentCount Int?    @default(0) // Aktif öğrenci sayısı
  completedLessons  Int?     @default(0) // Tamamlanan / Verilen ders sayısı
  timezone          String?  // Yeni eklendi
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  // JSON alanları
  education         Json? // örn: [{ year: "2013", degree: "Doktora, Dil Bilimi", institution: "Ludwig Maximilian Üniversitesi", university: "Münih, Almanya" }]
  certificates      Json? // örn: [{ name: "DaF Sertifikası", issuer: "Goethe Enstitüsü", year: "2012" }]
  
  // İlişkiler
  availabilitySlots AvailabilitySlot[]
  bookingsAsTeacher Booking[]          @relation("TeacherBookings")
  reviewsReceived   Review[]           @relation("TeacherReviews")
  lessonPackages    LessonPackage[]
  teacherFaqs       TeacherFAQ[]
}

// Ders Paketi modeli (LingoTeach örneğine göre güncellendi)
model LessonPackage {
  id                      String    @id @default(cuid())
  teacherId               String
  teacher                 Teacher   @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  type                    String    // "ONE_ON_ONE", "GROUP", "VIDEO_COURSE"
  name                    String    // Paket adı, örn: "Birebir Online Dersler - 4 Ders" veya "Almanca Temel Seviye (A1-A2) Video Kursu"
  description             String?   // Paket veya kurs açıklaması
  price                   Decimal   // Paketin veya kursun toplam fiyatı
  isActive                Boolean   @default(true)
  createdAt               DateTime  @default(now())
  updatedAt               DateTime  @updatedAt

  // Birebir ve Grup Dersleri için ortak olabilecek alanlar
  lessonsInPackage        Int?      // Paketteki toplam ders sayısı (birebir, grup)
  lessonDurationMinutes   Int?      // Bir dersin süresi (dakika)
  pricePerLessonCalculated Decimal? // Hesaplanan ders başı ücret (otomatik veya manuel)
  discountPercentage      Int?      // İndirim oranı (örn: 7, 13, 20)
  features                String[]  @default([]) // Pakete dahil özellikler (örn: ["Kişiselleştirilmiş ders planı", "Haftalık ödevler"])
  level                   String?   // Dersin veya paketin seviyesi (örn: "A1-A2", "İleri Seviye")

  // Sadece Grup Dersleri için alanlar
  studentCapacity         String?   // örn: "2-4 kişilik gruplar"
  groupCourseStartDate    DateTime? // Grup dersi başlangıç tarihi
  groupCourseSchedule     String?   // Grup dersi programı açıklaması (örn: "Haftada 2 gün, Salı-Perşembe 19:00-20:30")
  enrolledStudentCount    Int?      @default(0)

  // Sadece Video Kursları için alanlar
  videoCourseTotalLessons Int?      // Video kursundaki toplam ders sayısı
  videoCourseTotalHours   Float?    // Video kursunun toplam süresi (saat)
  videoCourseMaterials    String?   // Video kursuna dahil materyaller (örn: "video, çalışma materyalleri, alıştırmalar")
  videoCourseIsPopular    Boolean?  @default(false) // Popüler kurs etiketi için
  youtubePlaylistUrl      String?   // Video kursunun YouTube playlist linki
  thumbnailUrl            String?   // Video kursu için kapak resmi URL'si

  @@index([teacherId])
  @@index([type])
}

// Öğretmene Özel SSS Modeli (Yeni)
model TeacherFAQ {
  id         String   @id @default(cuid())
  teacherId  String
  teacher    Teacher  @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  question   String
  answer     String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([teacherId])
}

// Öğrenci modeli
model Student {
  id                 String   @id // Clerk User ID ile eşleşecek
  firstName          String? // Clerk'ten alınabilir veya profil sayfasında güncellenebilir
  lastName           String? // Clerk'ten alınabilir veya profil sayfasında güncellenebilir
  email              String   @unique // Clerk'ten alınacak
  phone              String?
  profile_image_url  String?
  learningGoals      String?
  proficiencyLevel   String? // örn: A2, B1
  favoriteTeacherIds String[] @default([])
  created_at         DateTime @default(now())
  updated_at         DateTime @updatedAt

  // İlişkiler
  bookingsAsStudent Booking[] @relation("StudentBookings")
  reviewsWritten    Review[]  @relation("StudentReviews")
  parentId          String?   // Veli ID'si (opsiyonel)
  parent            Parent?   @relation("ParentChildren", fields: [parentId], references: [id]) // Veli ilişkisi
  // MVP Sonrası: courseEnrollments CourseEnrollment[], resourcePurchases DigitalResourcePurchase[], consultationsReceived ConsultationBooking[]
}

// Veli modeli (MVP sonrası düşünülebilir ama eklenmiş)
model Parent {
  id         String    @id // Clerk User ID ile eşleşecek
  firstName  String?
  lastName   String?
  // email ve phone Clerk'ten alınabilir
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt

  // İlişkiler
  children Student[] @relation("ParentChildren") // Bir velinin birden fazla öğrencisi olabilir
}

// Öğretmen müsaitlik zaman dilimi modeli
model AvailabilitySlot {
  id        String   @id @default(cuid())
  startTime DateTime
  endTime   DateTime
  isBooked  Boolean  @default(false)
  teacherId String

  // İlişkiler
  teacher Teacher @relation(fields: [teacherId], references: [id], onDelete: Cascade) // Öğretmen silinirse slotlar da silinsin
  booking Booking? // Bu slot için yapılan rezervasyon (1'e 1 ilişki)
}

// Ders rezervasyon modeli
model Booking {
  id                 String   @id @default(cuid())
  status             String // örn: PENDING_PAYMENT, CONFIRMED, COMPLETED, CANCELLED
  lessonTime         DateTime // Dersin başlangıç zamanı (AvailabilitySlot'tan gelir ama burada da tutulabilir)
  durationMinutes    Int      // Ders süresi (örn: 50)
  pricePaid          Decimal? // Ödenen ücret
  commissionRate     Decimal? // Rezervasyon anındaki komisyon oranı
  commissionFee      Decimal? // Hesaplanan komisyon ücreti
  notes              String?  // Öğrencinin veya öğretmenin dersle ilgili notları
  lessonType         String?  // örn: TRIAL, STANDARD, BUSINESS_GERMAN
  created_at         DateTime @default(now())
  updated_at         DateTime @updatedAt
  studentId          String
  teacherId          String
  availabilitySlotId String   @unique // Bir slota sadece bir booking olabilir

  // İlişkiler
  availabilitySlot AvailabilitySlot @relation(fields: [availabilitySlotId], references: [id], onDelete: Restrict) // Slot silinirse booking kalmalı mı? Restrict daha güvenli.
  student            Student          @relation("StudentBookings", fields: [studentId], references: [id], onDelete: Cascade) // Öğrenci silinirse booking silinsin
  teacher            Teacher          @relation("TeacherBookings", fields: [teacherId], references: [id], onDelete: Cascade) // Öğretmen silinirse booking silinsin
  payment            Payment?         // Bu rezervasyon için yapılan ödeme
  review             Review?          // Bu rezervasyon için yazılan değerlendirme
  videoSession       VideoSession?    // Bu rezervasyon için oluşturulan video oturumu
}

// Ödeme modeli
model Payment {
  id                    String   @id @default(cuid())
  stripePaymentIntentId String?  @unique // Stripe ödeme ID'si
  // paypalOrderId String? // MVP Sonrası
  // iyzicoPaymentId String? // MVP Sonrası
  amount                Decimal  // Ödenen miktar
  currency              String   // Para birimi (örn: EUR, TRY)
  status                String   // Ödeme durumu (örn: succeeded, pending, failed)
  created_at            DateTime @default(now())
  bookingId             String   @unique // Hangi rezervasyona ait olduğu

  // İlişkiler
  booking Booking @relation(fields: [bookingId], references: [id], onDelete: Cascade) // Booking silinirse ödeme de silinsin (veya Restrict?)
}

// Video oturumu modeli
model VideoSession {
  id                 String   @id @default(cuid())
  videoProvider      String   // "zoom", "videosdk", "dyte"
  meetingId          String?  // Sağlayıcıdan gelen toplantı ID
  startUrl           String?  // Öğretmen başlangıç linki (Zoom için)
  joinUrl            String?  // Öğrenci katılım linki
  password           String?  // Toplantı şifresi (varsa)
  scheduledStartTime DateTime // Planlanan başlangıç zamanı (Booking'den gelir)
  // scheduledEndTime DateTime? // Eklenebilir
  // actualStartTime DateTime? // Eklenebilir
  // actualEndTime DateTime? // Eklenebilir
  bookingId          String   @unique // Hangi rezervasyona ait olduğu

  // İlişkiler
  booking Booking @relation(fields: [bookingId], references: [id], onDelete: Cascade) // Booking silinirse video oturumu da silinsin
}

// Değerlendirme modeli
model Review {
  id        String   @id @default(cuid())
  rating    Int      // 1-5 arası puan
  comment   String?
  createdAt DateTime @default(now())
  bookingId String   @unique // Hangi rezervasyona ait olduğu
  studentId String
  teacherId String

  // İlişkiler
  booking Booking @relation(fields: [bookingId], references: [id], onDelete: Cascade) // Booking silinirse değerlendirme de silinsin
  student Student @relation("StudentReviews", fields: [studentId], references: [id], onDelete: Cascade) // Öğrenci silinirse değerlendirmeleri silinsin
  teacher Teacher @relation("TeacherReviews", fields: [teacherId], references: [id], onDelete: Cascade) // Öğretmen silinirse değerlendirmeleri silinsin
}

// Aktivite log modeli (MVP sonrası düşünülebilir ama eklenmiş)
model ActivityLog {
  id         String   @id @default(cuid())
  timestamp  DateTime @default(now())
  actorId    String? // İşlemi yapan kullanıcı ID'si (Clerk ID)
  actorRole  String? // İşlemi yapanın rolü (admin, teacher, student, parent, system)
  actionType String // Yapılan işlem tipi (örn: TEACHER_APPROVED, BOOKING_CREATED)
  entityType String? // Etkilenen varlık tipi (Teacher, Booking)
  entityId   String? // Etkilenen varlık ID'si
  details    Json?   // İşlemle ilgili ek detaylar (örn: değişen alanlar)

  @@index([timestamp])
  @@index([actionType])
  @@index([entityType, entityId])
}
