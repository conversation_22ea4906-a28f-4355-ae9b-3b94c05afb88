'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

// Zod şeması: Öğretmen başvuru formu alanları için validasyon kuralları
const teacherApplicationFormSchema = z.object({
  bio: z.string().min(10, {
    message: 'Biyografi en az 10 karakter olmalıdır.',
  }).max(500, {
    message: 'Biyografi en fazla 500 karakter olmalıdır.',
  }),
  specializations: z.string().min(5, {
    message: '<PERSON>zmanlık alanları en az 5 karakter olmalıdır.',
  }), // Virgülle ayrılmış string olarak alabiliriz şimdilik
  hourlyRate: z.coerce.number().positive({
    message: 'Saatlik ücret pozitif bir sayı olmalıdır.',
  }).min(1, {
    message: 'Saatlik ücret en az 1 olmalıdır.',
  }),
  profileImageUrl: z.string().url({
    message: 'Geçerli bir URL giriniz.',
  }).optional().or(z.literal('')), // Opsiyonel URL
  introVideoUrl: z.string().url({
    message: 'Geçerli bir YouTube URL\'si giriniz.',
  }).optional().or(z.literal('')), // Opsiyonel YouTube URL
  certificates: z.string().optional(), // Opsiyonel metin alanı
  experienceYears: z.coerce.number().int().min(0, {
    message: 'Deneyim yılı negatif olamaz.',
  }).optional(),
});

// Form değerlerinin tipi
type TeacherApplicationFormValues = z.infer<typeof teacherApplicationFormSchema>;

// Varsayılan form değerleri
const defaultValues: Partial<TeacherApplicationFormValues> = {
  bio: '',
  specializations: '',
  hourlyRate: undefined,
  profileImageUrl: '',
  introVideoUrl: '',
  certificates: '',
  experienceYears: undefined,
};

export const TeacherApplicationForm = () => {
  const form = useForm<TeacherApplicationFormValues>({
    resolver: zodResolver(teacherApplicationFormSchema),
    defaultValues,
    mode: 'onChange',
  });

  // Form gönderildiğinde çalışacak fonksiyon
  function onSubmit(data: TeacherApplicationFormValues) {
    // console.log('Öğretmen Başvuru Formu Gönderildi:', data); - REMOVED
    // TODO: Başvuru verilerini işleyecek Server Action çağrılacak
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {/* Biyo Alanı */}
        <FormField
          control={form.control}
          name="bio"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Biyografi</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Kendinizden bahsedin..."
                  className="resize-y"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Öğrencilerin sizi tanımasını sağlayacak kısa bir biyografi yazın.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Uzmanlık Alanları Alanı */}
        <FormField
          control={form.control}
          name="specializations"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Uzmanlık Alanları</FormLabel>
              <FormControl>
                <Input placeholder="Örn: A1-A2 Seviye, İş Almancası, Test Hazırlığı" {...field} />
              </FormControl>
              <FormDescription>
                Hangi konularda ders verdiğinizi belirtin (virgülle ayırarak).
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Saatlik Ücret Alanı */}
        <FormField
          control={form.control}
          name="hourlyRate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Saatlik Ücret (€)</FormLabel>
              <FormControl>
                <Input type="number" placeholder="Örn: 20" {...field} onChange={event => field.onChange(+event.target.value)} />
              </FormControl>
              <FormDescription>
                Bir saatlik ders için belirlediğiniz ücret.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Profil Fotoğrafı URL Alanı */}
        <FormField
          control={form.control}
          name="profileImageUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Profil Fotoğrafı URL</FormLabel>
              <FormControl>
                <Input placeholder="Örn: https://example.com/profil.jpg" {...field} />
              </FormControl>
              <FormDescription>
                Profilinizde görünecek fotoğrafınızın URL&apos;si.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Tanıtım Videosu URL Alanı */}
        <FormField
          control={form.control}
          name="introVideoUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tanıtım Videosu (YouTube URL)</FormLabel>
              <FormControl>
                <Input placeholder="Örn: https://www.youtube.com/watch?v=..." {...field} />
              </FormControl>
              <FormDescription>
                Öğrencilerin sizi daha iyi tanıması için bir YouTube tanıtım videosu linki.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Sertifikalar Alanı */}
        <FormField
          control={form.control}
          name="certificates"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Sertifikalar / Başarılar</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Sahip olduğunuz sertifikaları veya başarıları belirtin..."
                  className="resize-y"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Eğitim geçmişiniz veya önemli başarılarınız.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Deneyim Yılları Alanı */}
        <FormField
          control={form.control}
          name="experienceYears"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Deneyim Yılı</FormLabel>
              <FormControl>
                <Input type="number" placeholder="Örn: 5" {...field} onChange={event => field.onChange(+event.target.value)} />
              </FormControl>
              <FormDescription>
                Almanca öğretimi alanındaki deneyim süreniz.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit">Başvuruyu Gönder</Button>
      </form>
    </Form>
  );
};
