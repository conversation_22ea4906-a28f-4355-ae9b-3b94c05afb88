"use client"

import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star } from 'lucide-react';
import type { TeacherReview } from '@/types/teacher'; // TeacherStats importu kaldırıldı

// Bu dosyadaki Review interface'i kaldırıldı, TeacherReview kullanılacak

interface StudentReviewsProps {
  teacherId: string; // number -> string
  // TODO: Değerlendirmeleri prop olarak al veya burada fetch et
  initialReviews?: TeacherReview[]; // Review[] -> TeacherReview[]
  // stats: TeacherStats; // stats prop'u kaldırıldı, bu bilgi TeacherProfileClient'ta zaten mevcut
}

// <PERSON><PERSON><PERSON> (TeacherReview tipine uygun)
const sampleReviews: TeacherReview[] = [
  {
    id: "r1", // number -> string
    studentName: "Mehm<PERSON> Y.",
    studentAvatar: "/placeholder.svg?height=40&width=40",
    rating: 5,
    comment: "Frau Schmidt harika bir öğretmen! Çok sabırlı ve konuları çok iyi anlatıyor. Kısa sürede büyük ilerleme kaydettim.",
    date: "2025-03-20", // Daha standart format
    helpful: 12,
    lessonType: "Özel Ders"
  },
  {
    id: "r2", // number -> string
    studentName: "Ayşe K.",
    rating: 4,
    comment: "Dersler verimli geçiyor, özellikle konuşma pratiği konusunda çok yardımcı oldu. Tavsiye ederim.",
    date: "2025-03-15",
    helpful: 5
  },
   {
    id: "r3", // number -> string
    studentName: "Can T.",
    studentAvatar: "/placeholder.svg?height=40&width=40",
    rating: 5,
    comment: "İş Almancası konusunda aradığım öğretmeni buldum. Çok profesyonel ve bilgili.",
    date: "2025-03-10",
    helpful: 8,
    lessonType: "İş Almancası Kursu"
  },
];

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function StudentReviews({ teacherId, initialReviews = sampleReviews }: StudentReviewsProps) { // stats prop'u kaldırıldı

  // TODO: teacherId prop'unu kullanarak belirli öğretmenin yorumlarını filtrele/fetch et
  // TODO: Daha fazla değerlendirme yükleme mantığı eklenebilir

  if (!initialReviews || initialReviews.length === 0) {
    return <p className="text-muted-foreground">Bu öğretmen için henüz değerlendirme yapılmamış.</p>;
  }

  return (
    <div className="space-y-6">
      {initialReviews.map((review) => (
        <div key={review.id} className="flex gap-4 border-b pb-6 last:border-b-0 last:pb-0">
          <Avatar className="h-10 w-10">
            <AvatarImage src={review.studentAvatar} alt={review.studentName} />
            <AvatarFallback>{review.studentName.split(' ').map(n => n[0]).join('')}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex justify-between items-center mb-1">
              <span className="font-semibold">{review.studentName}</span>
              {/* Tarih formatlaması eklenebilir */}
              <span className="text-xs text-muted-foreground">{new Date(review.date).toLocaleDateString("tr-TR")}</span>
            </div>
            <div className="flex items-center mb-2">
              {Array.from({ length: 5 }).map((_, index) => (
                <Star
                  key={index}
                  className={`h-4 w-4 ${index < review.rating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`}
                />
              ))}
            </div>
            {review.lessonType && (
                <p className="text-xs text-muted-foreground mb-1">Ders: {review.lessonType}</p>
            )}
            <p className="text-sm text-gray-700">{review.comment}</p>
             {/* TODO: Faydalı bulma butonu eklenebilir */}
             {/* <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground mt-2">
                 <ThumbsUp className="w-4 h-4 mr-2" />
                 {review.helpful} kişi faydalı buldu
             </Button> */}
          </div>
        </div>
      ))}
       {/* TODO: "Daha Fazla Yorum Göster" butonu eklenebilir */}
    </div>
  );
}