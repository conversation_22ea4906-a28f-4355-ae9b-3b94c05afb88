"use client"

import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'

interface SpecialtyFilterProps {
  selected: string[]
  onChange: (specialties: string[]) => void
  onClose: () => void
}

const SPECIALTIES = [
  { category: 'Seviyeler', items: ['A1-A2', 'B1-B2', 'C1-C2'] },
  { category: 'Sınavlar', items: ['TestDaF', 'Goethe Sınavları', 'TELC'] },
  { category: 'Özel Alanlar', items: ['İş Almancası', 'Akademik Almanca', 'Tıp Almancası'] }
]

export function SpecialtyFilter({ selected, onChange, onClose }: SpecialtyFilterProps) {
  const handleToggle = (specialty: string) => {
    if (selected.includes(specialty)) {
      onChange(selected.filter(s => s !== specialty))
    } else {
      onChange([...selected, specialty])
    }
  }

  const handleClear = () => onChange([])

  return (
    <ScrollArea className="h-[400px] pr-4">
      <div className="grid gap-4">
        {SPECIALTIES.map(category => (
          <div key={category.category}>
            <h3 className="font-semibold mb-2">{category.category}</h3>
            <div className="grid grid-cols-2 gap-2">
              {category.items.map(specialty => (
                <div key={specialty} className="flex items-center space-x-2">
                  <Checkbox
                    id={`specialties-${specialty}`}
                    checked={selected.includes(specialty)}
                    onCheckedChange={() => handleToggle(specialty)}
                  />
                  <Label htmlFor={`specialties-${specialty}`}>{specialty}</Label>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
      <div className="flex justify-end gap-2 mt-4">
        <Button variant="outline" onClick={handleClear}>Temizle</Button>
        <Button onClick={onClose}>Uygula</Button>
      </div>
    </ScrollArea>
  )
}
