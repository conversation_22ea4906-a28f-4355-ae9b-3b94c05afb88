// import { auth } from "@clerk/nextjs/server"; // Clerk kaldır<PERSON>ld<PERSON>
import { redirect } from "next/navigation";
import { prisma } from "@/lib/prisma"; // Prisma client'ın buradan export edildiğini varsayıyoruz
import { TeacherProfileForm } from "@/components/forms/TeacherProfileForm"; // Form bileşeninin yolu (Named import)
import TeacherAvailabilityCalendar from "@/components/calendar/TeacherAvailabilityCalendar"; // Takvim bileşeni importu

// Decimal türünü JSON.stringify ile uyumlu hale getirmek için replacer fonksiyonu
// Bu, server component'ten client component'e prop geçerken Decimal hatasını önler.
const decimalReplacer = (key: string, value: unknown) => { // 'any' yerine 'unknown' kullanıldı
  if (typeof value === 'object' && value !== null && value.constructor && value.constructor.name === 'Decimal') {
    return value.toString();
  }
  return value;
};


export default async function TeacherProfilePage() {
  // const { userId } = await auth(); // Clerk kaldırıldı
  let userId: string | null = null;

  if (process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
    // console.warn("DEV_SKIP_AUTH_MIDDLEWARE: TeacherProfilePage - Using default 'test-teacher-id'.");
    userId = "test-teacher-id"; // Test için varsayılan öğretmen ID'si
  } else {
    // Gerçek bir kimlik doğrulama sistemi olmadığında, bu sayfanın nasıl davranacağına karar verilmesi gerekir.
    // Şimdilik, userId yoksa giriş sayfasına yönlendirme devam edecek.
    // Alternatif olarak, Supabase Auth veya başka bir sistemden session bilgisi alınabilir.
  }

  if (!userId) {
    // Middleware tarafından ele alınmalı, ancak ek kontrol
    // TODO: Clerk kaldırıldığı için "/sign-in" yerine uygun bir genel giriş/hata sayfasına yönlendirilmeli
    redirect("/"); // Şimdilik ana sayfaya yönlendir
  }

  // Kullanıcı ID'sine göre öğretmen verisini çek
  const teacher = await prisma.teacher.findUnique({
    where: {
      id: userId, // Clerk userId yerine kullanılan test ID'si veya gelecekteki auth sisteminden gelen ID
    },
  });

  if (!teacher) {
    // Kullanıcı giriş yapmış ancak öğretmen kaydı yoksa veya rolü yanlışsa bu durum olabilir
    // (Middleware bunu engellemeli)
    return (
      <div className="p-4 md:p-6">
        <h1 className="text-xl font-semibold mb-4">Profil Bulunamadı</h1>
        <p>Öğretmen profiliniz henüz oluşturulmamış veya bir hata oluştu.</p>
      </div>
    );
  }

  // Teacher objesini client component'e geçmeden önce serialize et
  // Decimal alanlar sorun çıkarabileceği için özel replacer kullan
  const serializedTeacher = JSON.parse(JSON.stringify(teacher, decimalReplacer));


  return (
    <div className="p-4 md:p-6">
      <h1 className="text-2xl font-bold mb-6">Öğretmen Profilim</h1>

      {teacher.is_approved ? (
        <div>
          <p className="mb-4 text-muted-foreground">
            Profilinizi buradan düzenleyebilirsiniz.
          </p>
          {/* Çekilen öğretmen verisini forma prop olarak geç */}
          {/* Prop geçerken serialize edilmiş veriyi kullan */}
          <TeacherProfileForm teacherData={serializedTeacher} />

          {/* Müsaitlik Takvimi */}
          <div className="mt-8 pt-8 border-t">
            <h2 className="text-xl font-semibold mb-4">Müsaitlik Takvimi</h2>
            <p className="text-muted-foreground mb-6">
              Öğrencilerin ders rezervasyonu yapabilmesi için müsait olduğunuz zaman dilimlerini belirleyin.
            </p>
            <TeacherAvailabilityCalendar teacherId={teacher.id} isEditable={true} />
          </div>
        </div>
      ) : (
        <div className="border border-yellow-300 bg-yellow-50 p-4 rounded-md">
          <h2 className="text-lg font-semibold text-yellow-800 mb-2">
            Profiliniz Onay Bekliyor
          </h2>
          <p className="text-yellow-700">
            Profil bilgileriniz henüz yönetici tarafından onaylanmamıştır. Onay
            sürecinden sonra profilinizi düzenleyebilir ve öğrenciler tarafından
            görüntülenebilir hale getirebilirsiniz.
          </p>
        </div>
      )}
    </div>
  );
}
