import React from 'react';
import { Video, ChevronLeft, ChevronRight } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Teacher } from '@/types/teacher';
import { AspectRatio } from '../ui/aspect-ratio';

interface VideoPlayerModalProps {
  showVideoModal: boolean;
  onClose: () => void;
  teacher: Teacher;
  currentVideoIndex: number;
  onVideoIndexChange: (index: number) => void;
}

export const VideoPlayerModal: React.FC<VideoPlayerModalProps> = ({
  showVideoModal,
  onClose,
  teacher,
  currentVideoIndex,
  onVideoIndexChange
}) => {
  const currentVideo = teacher.videoCourses?.[currentVideoIndex];

  const getYouTubeEmbedUrl = (url: string | undefined) => {
    if (!url) return null;
    try {
      const urlObj = new URL(url);
      const videoId = urlObj.searchParams.get('v');
      if (videoId) {
        return `https://www.youtube.com/embed/${videoId}?autoplay=1`;
      }
      // Handle youtu.be links
      if (urlObj.hostname === 'youtu.be') {
        return `https://www.youtube.com/embed/${urlObj.pathname.slice(1)}?autoplay=1`;
      }
    } catch {
      console.error("Invalid YouTube URL:", url);
      return null;
    }
    return null;
  };

  const embedUrl = getYouTubeEmbedUrl(currentVideo?.youtubeLink);

  return (
    <Dialog open={showVideoModal} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl p-4 sm:p-6">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-gray-800">
            <Video className="w-5 h-5 text-blue-500" />
            {currentVideo?.name || 'Video Ders'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="w-full">
          <AspectRatio ratio={16 / 9} className="bg-black rounded-lg overflow-hidden">
            {embedUrl ? (
              <iframe
                src={embedUrl}
                title={currentVideo?.name || 'YouTube video player'}
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
                className="w-full h-full"
              ></iframe>
            ) : (
              <div className="w-full h-full flex items-center justify-center text-white bg-gray-800">
                <p>Video yüklenemedi.</p>
              </div>
            )}
          </AspectRatio>
        </div>
        
        <div className="flex gap-2 justify-center mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onVideoIndexChange(Math.max(0, currentVideoIndex - 1))}
            disabled={currentVideoIndex === 0}
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Önceki
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onVideoIndexChange(Math.min((teacher.videoCourses?.length || 1) - 1, currentVideoIndex + 1))}
            disabled={currentVideoIndex === (teacher.videoCourses?.length || 1) - 1}
          >
            Sonraki
            <ChevronRight className="w-4 h-4 ml-1" />
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
