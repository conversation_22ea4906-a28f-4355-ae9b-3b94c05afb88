// Örnek veri - Gerçek uygulamada Prisma ile veritabanından çekilecek
export async function getTeachers() {
  // Simüle edilmiş veri
  return [
    {
      id: "1",
      name: "<PERSON><PERSON> Yılmaz",
      email: "<EMAIL>",
      status: "active",
    },
    {
      id: "2",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      status: "active",
    },
    {
      id: "3",
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      status: "pending",
    },
    {
      id: "4",
      name: "<PERSON><PERSON><PERSON><PERSON> Çelik",
      email: "<EMAIL>",
      status: "active",
    },
    {
      id: "5",
      name: "<PERSON>",
      email: "<EMAIL>",
      status: "pending",
    },
  ];
}
