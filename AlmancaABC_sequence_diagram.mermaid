sequenceDiagram
    participant Client as Web Client
    participant MobileClient as Mobile Client (MVP+)
    participant NextAPI as Next.js API Routes
    participant Clerk
    participant SupabaseD<PERSON> as Supabase DB (Prisma)
    participant SupabaseStorage as Supabase Storage
    participant ZoomAPI
    participant StripeAPI
    participant TranslationService as Ext. Translation Service (MVP+)
    participant DictionaryService as Ext. Dictionary Service (MVP+)
    participant VideoHosting as Ext. Video Hosting (Mux/Cloudinary) (MVP+)

    %% --- MVP Flows (Simplified from MVP Diagram) ---

    %% User Registration (Assuming Clerk Hosted UI or Custom Form calling API)
    Client->>NextAPI: initiateSignUp(email, password, role)
    NextAPI->>Clerk: createUser(email, password, publicMetadata={role})
    Clerk-->>NextAPI: {userId, session}
    alt Create Profile in DB (Optional, can be done via webhook)
        NextAPI->>SupabaseDB: createTeacher/StudentProfile(userId, initialData)
        SupabaseDB-->>NextAPI: profile
    end
    NextAPI-->>Client: {success, session}

    %% User Login
    Client->>NextAPI: initiateSignIn(email, password)
    NextAPI->>Clerk: attemptSignIn(...)
    Clerk-->>NextAPI: {session}
    NextAPI-->>Client: {success, session}

    %% Teacher Sets Availability
    Client->>NextAPI: setAvailability(teacherId, slots)
    NextAPI->>SupabaseDB: saveAvailabilitySlots(teacherId, slots)
    SupabaseDB-->>NextAPI: success
    NextAPI-->>Client: success

    %% Student Books Lesson
    Client->>NextAPI: initiateBooking(teacherId, slotId)
    NextAPI->>SupabaseDB: checkAvailability(slotId)
    SupabaseDB-->>NextAPI: {available: true}
    NextAPI->>StripeAPI: createPaymentIntent(...)
    StripeAPI-->>NextAPI: paymentIntent
    NextAPI-->>Client: {bookingId, paymentIntentClientSecret}
    Client->>StripeAPI: confirmPayment(...)
    StripeAPI-->>Client: {success: true}
    Client->>NextAPI: finalizeBooking(bookingId, paymentIntentId)
    NextAPI->>SupabaseDB: updateBooking(bookingId, status='CONFIRMED', paymentId)
    NextAPI->>SupabaseDB: markSlotAsBooked(slotId)
    NextAPI->>ZoomAPI: createMeeting(...) 
    ZoomAPI-->>NextAPI: {meetingId, startUrl, joinUrl}
    NextAPI->>SupabaseDB: createVideoSession(bookingId, zoomDetails)
    SupabaseDB-->>NextAPI: videoSession
    NextAPI-->>Client: {success: true, bookingDetails}

    %% Joining Video Lesson
    Client->>NextAPI: getJoinInfo(bookingId)
    NextAPI->>SupabaseDB: getVideoSession(bookingId)
    SupabaseDB-->>NextAPI: {zoomJoinUrl, meetingId}
    NextAPI-->>Client: {joinUrl}
    Client->>ZoomAPI: joinMeeting(joinUrl) 
    ZoomAPI-->>Client: Connected

    %% Post-Lesson Review
    Client->>NextAPI: submitReview(bookingId, rating, comment)
    NextAPI->>SupabaseDB: createReview(bookingId, studentId, teacherId, rating, comment)
    SupabaseDB-->>NextAPI: success
    NextAPI-->>Client: success

    %% --- Post-MVP Flows ---

    %% Teacher Creates Offline Course
    Client->>NextAPI: createCourse(title, description, price)
    NextAPI->>SupabaseDB: saveCourse(teacherId, courseData)
    SupabaseDB-->>NextAPI: courseId
    loop For each lesson video
        Client->>NextAPI: uploadVideo(courseId, moduleTitle, lessonTitle, videoFile)
        NextAPI->>VideoHosting: upload(videoFile)
        VideoHosting-->>NextAPI: videoUrl
        NextAPI->>SupabaseDB: saveLesson(courseId, moduleTitle, lessonTitle, videoUrl)
    end
    NextAPI-->>Client: {success: true, courseId}

    %% Student Enrolls in Offline Course
    Client->>NextAPI: enrollCourse(courseId)
    NextAPI->>StripeAPI: createPaymentIntent(coursePrice) 
    StripeAPI-->>NextAPI: paymentIntent
    NextAPI-->>Client: paymentIntentClientSecret
    Client->>StripeAPI: confirmPayment(...)
    StripeAPI-->>Client: {success: true}
    Client->>NextAPI: finalizeEnrollment(courseId, paymentIntentId)
    NextAPI->>SupabaseDB: createCourseEnrollment(studentId, courseId)
    SupabaseDB-->>NextAPI: success
    NextAPI-->>Client: {success: true}

    %% Student Accesses Course Video (Mobile Example)
    MobileClient->>NextAPI: getCourseLesson(lessonId)
    NextAPI->>SupabaseDB: checkEnrollment(studentId, courseId)
    SupabaseDB-->>NextAPI: {isEnrolled: true}
    NextAPI->>SupabaseDB: getLesson(lessonId)
    SupabaseDB-->>NextAPI: {videoUrl, title} 
    NextAPI-->>MobileClient: {videoUrl, title}
    MobileClient->>VideoHosting: streamVideo(videoUrl)

    %% User Requests Document Translation
    Client->>NextAPI: requestTranslation(file, sourceLang, targetLang)
    NextAPI->>SupabaseStorage: uploadFile(file)
    SupabaseStorage-->>NextAPI: fileUrl
    NextAPI->>TranslationService: submitJob(fileUrl, sourceLang, targetLang) 
    TranslationService-->>NextAPI: jobId
    NextAPI->>SupabaseDB: saveTranslationRequest(userId, jobId, fileUrl, status='PENDING')
    NextAPI-->>Client: {success: true, requestId}

    %% User Searches Dictionary
    Client->>NextAPI: searchDictionary(term)
    NextAPI->>DictionaryService: lookup(term) 
    DictionaryService-->>NextAPI: results
    NextAPI-->>Client: results

    %% Admin Approves Teacher
    AdminClient->>NextAPI: approveTeacher(teacherId)
    NextAPI->>SupabaseDB: updateTeacher(teacherId, {is_approved: true})
    SupabaseDB-->>NextAPI: success
    NextAPI-->>AdminClient: success