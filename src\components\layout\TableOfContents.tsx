'use client';

import React, { useEffect, useState, useRef } from 'react';
import Link from 'next/link';

interface Heading {
  id: string;
  text: string;
  level: number;
  element: HTMLHeadingElement;
}

interface TableOfContentsProps {
  contentRef: React.RefObject<HTMLElement | null>; // Ana içerik alanına referans (null olabilir)
}

const slugify = (text: string): string => {
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-') // Boşlukları - ile değiştir
    .replace(/[^\w-]+/g, '') // Alfanümerik olmayan karakterleri kaldır (tire hariç)
    .replace(/--+/g, '-'); // Birden fazla tireyi tek tire yap
};

const TableOfContents: React.FC<TableOfContentsProps> = ({ contentRef }) => {
  const [headings, setHeadings] = useState<Heading[]>([]);
  const [activeId, setActiveId] = useState<string | null>(null);
  const observer = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    if (!contentRef.current) return;

    const headingElements = Array.from(
      contentRef.current.querySelectorAll('h2, h3')
    ) as HTMLHeadingElement[];

    const newHeadings = headingElements.map((heading) => { // 'index' kaldırıldı
      const text = heading.textContent || '';
      let id = heading.id || slugify(text);
      
      // ID'nin benzersiz olduğundan emin ol
      let counter = 1;
      const originalId = id; // 'let' yerine 'const' kullanıldı
      while (document.getElementById(id) && document.getElementById(id) !== heading) {
        id = `${originalId}-${counter}`;
        counter++;
      }
      heading.id = id;

      return {
        id,
        text,
        level: parseInt(heading.tagName.substring(1), 10),
        element: heading,
      };
    });
    setHeadings(newHeadings);

    // Intersection Observer kurulumu
    if (observer.current) observer.current.disconnect();
    
    observer.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveId(entry.target.id);
          }
        });
      },
      { rootMargin: '0px 0px -70% 0px' } // Ekranın üst %30'unda aktif olacak
    );

    newHeadings.forEach((heading) => {
      if (heading.element) {
        observer.current?.observe(heading.element);
      }
    });

    return () => {
      observer.current?.disconnect();
    };
  }, [contentRef]);

  if (headings.length === 0) {
    return null; // Başlık yoksa hiçbir şey gösterme
  }

  return (
    (<nav className="mb-8 p-4 bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200/50 dark:border-slate-700/30 sticky top-24 max-h-[calc(100vh-12rem)] overflow-y-auto">
      <h2 className="text-xl font-semibold mb-4 text-slate-700 dark:text-slate-200 border-b border-slate-300 dark:border-slate-600 pb-3">İçindekiler</h2>
      <ul className="space-y-2 mt-4">
        {headings.map((heading) => (
          <li key={heading.id} className={`${heading.level === 3 ? 'ml-4 pl-3 border-l-2 border-slate-300 dark:border-slate-600' : 'font-medium'}`}>
            <Link
              href={`#${heading.id}`}
              className={`block text-sm py-1 px-2 rounded-lg transition-all duration-150 ease-in-out group
                ${activeId === heading.id
                  ? 'bg-sky-500/10 text-sky-500 font-medium'
                  : 'text-slate-700 dark:text-slate-300 hover:bg-slate-200/20 dark:hover:bg-slate-700/20 hover:text-slate-900 dark:hover:text-slate-100'
                }
              `}
              onClick={(e) => {
                e.preventDefault();
                document.getElementById(heading.id)?.scrollIntoView({
                  behavior: 'smooth',
                  block: 'start',
                });
                // Adres çubuğunu güncellemek için (isteğe bağlı)
                // window.history.pushState(null, '', `#${heading.id}`);
                setActiveId(heading.id);
              }}>
              {heading.text}
            </Link>
          </li>
        ))}
      </ul>
    </nav>)
  );
};

export default TableOfContents;