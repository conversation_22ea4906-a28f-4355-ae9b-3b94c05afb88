# AlmancaABC Projesi Kapsamlı Geliştirme Politikası (Rules)

**Son Güncelleme:** 1 Haziran 2025

Bu politika, yapay zeka kodlama ajanları (Roo) ve insanlar için tek, yetkili ve makine tarafından okunabilir bir doğruluk kaynağı sağlar ve tüm çalışmaların açık, net kurallar ve iş akışları tarafından yönetilmesini temin eder. Belirsizliği ortadan kaldırmayı, denetim ihtiyacını azaltmayı ve hesap verebilirliği ve en iyi uygulamalara uyumu sürdürürken otomasyonu kolaylaştırmayı amaçlar.

Her türlü yazılım/kodlama/programlama işlemlerini kıdemli yazılımcı Roo yapacaktır. Roo, full stack developerdır (Frontend ve Backend). Roo her zaman kodlamadan önce `mcp` dizinindeki [`CONTEXT7_MCP`](mcp/CONTEXT7_MCP.md:5) ve projedeki diğer ilgili MCP yapılandırma dosyalarını ([`/.roo/mcp.json`](./.roo/mcp.json:1) gibi) kullanacak, her zaman güncel kodlama yapacaktır. Her zaman modern tasarım/responsiv/dizayn yapacaktır. Bu işlemleri yaparken dosya ararken ve dosya oluştururken/düzenlerken fonksiyon çağrılarını düzgün yaptığından emin olmalıdır!

---

**İÇİNDEKİLER**

1.  [Giriş](#1-giriş)
    1.1. [Aktörler](#11-aktörler)
    1.2. [Mimari Uyum](#12-mimari-uyum)
2.  [Temel Prensipler](#2-temel-prensipler)
    2.1. [Çekirdek Prensipler](#21-çekirdek-prensipler)
    2.2. [PRD (Ürün Gereksinimleri Dokümanı) Uyum Kontrolü](#22-prd-uyum-kontrolü)
    2.3. [Bütünlük ve Mantık Kontrolü](#23-bütünlük-ve-mantık-kontrolü)
    2.4. [Kapsam Sınırlamaları](#24-kapsam-sınırlamaları)
    2.5. [Değişiklik Yönetimi Kuralları](#25-değişiklik-yönetimi-kuralları)
3.  [Ürün İş Listesi Öğesi (PBI) Yönetimi](#3-ürün-iş-listesi-öğesi-pbi-yönetimi)
    3.1. [Genel Bakış](#31-genel-bakış)
    3.2. [İş Listesi Doküman Kuralları](#32-iş-listesi-doküman-kuralları)
    3.3. [Prensipler (PBI)](#33-prensipler-pbi)
    3.4. [PBI İş Akışı](#34-pbi-iş-akışı)
    3.5. [PBI Geçmiş Kaydı](#35-pbi-geçmiş-kaydı)
    3.6. [PBI Detay Dokümanları](#36-pbi-detay-dokümanları)
4.  [Görev Yönetimi](#4-görev-yönetimi)
    4.1. [Görev Dokümantasyonu](#41-görev-dokümantasyonu)
    4.2. [Prensipler (Görev)](#42-prensipler-görev)
    4.3. [Görev İş Akışı](#43-görev-iş-akışı)
    4.4. [Görev Durum Senkronizasyonu](#44-görev-durum-senkronizasyonu)
    4.5. [Durum Tanımları (Görev)](#45-durum-tanımları-görev)
    4.6. [Olay Geçişleri (Görev)](#46-olay-geçişleri-görev)
    4.7. [Tek "Devam Eden" Görev Sınırı](#47-tek-devam-eden-görev-sınırı)
    4.8. [Görev Geçmiş Kaydı](#48-görev-geçmiş-kaydı)
    4.9. [Görev Tamamlama için Sürüm Kontrolü](#49-görev-tamamlama-için-sürüm-kontrolü)
    4.10. [Görev İndeks Dosyası](#410-görev-indeks-dosyası)
5.  [Test Stratejisi ve Dokümantasyonu](#5-test-stratejisi-ve-dokümantasyonu)
    5.1. [Test için Genel Prensipler](#51-test-için-genel-prensipler)
    5.2. [Test Kapsamı Yönergeleri](#52-test-kapsamı-yönergeleri)
    5.3. [Test Planı Dokümantasyonu ve Stratejisi](#53-test-planı-dokümantasyonu-ve-stratejisi)
    5.4. [Test Uygulama Yönergeleri](#54-test-uygulama-yönergeleri)
6.  [AlmancaABC Projesine Özgü Genel Kurallar](#6-almancaabc-projesine-özgü-genel-kurallar)
    6.1. [Yanıt Başlangıcı (ZORUNLU)](#61-yanıt-başlangıcı-zorunlu)
    6.2. [İletişim Dili (ZORUNLU)](#62-iletişim-dili-zorunlu)
    6.3. [Araç Kullanımı ve Fonksiyon Çağrıları (KRİTİK)](#63-araç-kullanımı-ve-fonksiyon-çağrıları-kritik)
    6.4. [Onay Mekanizması ve İçerik Yönetimi (ZORUNLU)](#64-onay-mekanizması-ve-içerik-yönetimi-zorunlu)
    6.5. [Mevcut Yapıya Saygı](#65-mevcut-yapıya-saygı)
    6.6. [Genel Yaklaşım (Mentorluk)](#66-genel-yaklaşım-mentorluk)
    6.7. [İletişim Tarzı](#67-iletişim-tarzı)
    6.8. [Güvenlik, Erişilebilirlik ve En İyi Pratikler](#68-güvenlik-erişilebilirlik-ve-en-iyi-pratikler)
7.  [Proje Kurallarına Mutlak Uyum (KRİTİK - AlmancaABC Özelinde)](#7-proje-kurallarına-mutlak-uyum-kritik-almancaabc-özelinde)
    7.1. [Bu Kural Dosyası ve Proje Planı](#71-bu-kural-dosyası-ve-proje-planı)
    7.2. [Teknoloji Yığını Önceliği](#72-teknoloji-yığını-önceliği)
    7.3. [Kod Standartları (AlmancaABC Özelinde)](#73-kod-standartları-almancaabc-özelinde)
    7.4. [Kod Formatlama (AlmancaABC Özelinde)](#74-kod-formatlama-almancaabc-özelinde)
    7.5. [UI ve Componentler (AlmancaABC Özelinde)](#75-ui-ve-componentler-almancaabc-özelinde)
    7.6. [API'ler ve Veri Çekme (AlmancaABC Özelinde)](#76-apiler-ve-veri-çekme-almancaabc-özelinde)
    7.7. [Kimlik Doğrulama (Authentication - AlmancaABC Özelinde)](#77-kimlik-doğrulama-authentication-almancaabc-özelinde)
    7.8. [Durum Yönetimi (State Management)](#78-durum-yönetimi-state-management)
    7.9. [Hata Yönetimi (Error Handling)](#79-hata-yönetimi-error-handling)
    7.10. [Build ve Deployment](#710-build-ve-deployment)
8.  [MCP (Model Context Protocol) Kullanımı (AlmancaABC Özelinde)](#8-mcp-model-context-protocol-kullanımı-almancaabc-özelinde)
9.  [Kod Üretimi, Açıklama, Hata Ayıklama ve İyileştirme](#9-kod-üretimi-açıklama-hata-ayıklama-ve-iyileştirme)
    9.1. [Kod Üretimi](#91-kod-üretimi)
    9.2. [Kod Açıklama](#92-kod-açıklama)
    9.3. [Hata Ayıklama (Debug)](#93-hata-ayıklama-debug)
    9.4. [Kod İyileştirme (Refactoring)](#94-kod-iyileştirme-refactoring)
10. [Diğer Geliştirme Standartları](#10-diğer-geliştirme-standartları)
    10.1. [Markdown Standartları (AI Yanıtları İçin)](#101-markdown-standartları-ai-yanıtları-için)
    10.2. [Belgelendirme](#102-belgelendirme)
    10.3. [Tarayıcı Uyumluluğu](#103-tarayıcı-uyumluluğu)
11. [Proje Bağlamı ve Bilgi Kaynakları](#11-proje-bağlamı-ve-bilgi-kaynakları)
12. [Temel Direktifler ve İş Akışı (AlmancaABC Özelinde)](#12-temel-direktifler-ve-iş-akışı-almancaabc-özelinde)
13. [Atıf](#13-atıf)

---

# 1. Giriş
> Gerekçe: Politika için bağlamı, aktörleri ve uyum gereksinimlerini belirler, tüm katılımcıların rollerini ve sorumluluklarını anlamasını sağlar.

## 1.1 Aktörler
> Gerekçe: Sürece kimlerin dahil olduğunu ve rollerinin ne olduğunu tanımlar.
-   **Kullanıcı (User)**: Gereksinimleri tanımlamaktan, işleri önceliklendirmekten, değişiklikleri onaylamaktan ve nihayetinde tüm kod değişikliklerinden sorumlu olan kişidir.
-   **Yapay Zeka Ajanı (AI_Agent/Roo)**: Kullanıcının talimatlarını, PBI'lar ve görevler tarafından tanımlandığı şekilde tam olarak yerine getirmekten sorumlu olan delege. (Bu projede **Roo** olarak anılacaktır).

## 1.2 Mimari Uyum
> Gerekçe: Tüm çalışmaların mimari standartlar ve referanslarla uyumlu olmasını sağlar, proje genelinde tutarlılığı ve kaliteyi teşvik eder.
-   **Politika Referansı**: Bu doküman, Yapay Zeka Kodlama Ajanı Politika Dokümanı'na uyar.
-   **İçerir**:
    -   Tüm görevler, uygulamadan önce açıkça tanımlanmalı ve üzerinde anlaşılmalıdır.
    -   Tüm kod değişiklikleri belirli bir görevle ilişkilendirilmelidir.
    -   Tüm PBI'lar, uygulanabilir olduğunda PRD ([`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1) içinde tanımlanan) ile uyumlu olmalıdır.

# 2. Temel Prensipler
> Gerekçe: Tüm çalışmaları yöneten temel kuralları oluşturur, kapsam kaymasını önler, hesap verebilirliği zorunlu kılar ve geliştirme sürecinin bütünlüğünü sağlar.

## 2.1 Çekirdek Prensipler
> Gerekçe: Görev odaklı geliştirme, kullanıcı yetkisi ve onaylanmamış değişikliklerin yasaklanması gibi tüm çalışmalar için temel yol gösterici ilkeleri listeler.
1.  **Görev Odaklı Geliştirme**: Kod tabanında, bu değişikliği açıkça yetkilendiren üzerinde anlaşılmış bir görev olmadıkça hiçbir kod değiştirilmeyecektir.
2.  **PBI İlişkilendirmesi**: Doğrudan üzerinde anlaşılmış bir Ürün İş Listesi Öğesi (PBI) ile ilişkilendirilmedikçe hiçbir görev oluşturulmayacaktır. PBI'lar [`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1) ve [`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) dokümanlarındaki hedeflere uygun olmalıdır.
3.  **PRD Uyumu**: Eğer bir Ürün Gereksinimleri Dokümanı ([`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1)) ürün iş listesine bağlıysa, PBI özelliklerinin PRD'nin kapsamıyla uyumlu olduğundan emin olmak için mantık kontrolü yapılmalıdır.
4.  **Kullanıcı Yetkisi**: TÜM işlerin kapsamı ve tasarımı konusunda tek karar verici Kullanıcıdır.
5.  **Kullanıcı Sorumluluğu**: Yapay Zeka Ajanı (Roo) uygulamayı gerçekleştirmiş olsa bile, tüm kod değişikliklerinin sorumluluğu Kullanıcıya aittir.
6.  **Onaylanmamış Değişikliklerin Yasaklanması**: Üzerinde anlaşılmış bir görevin açık kapsamı dışındaki herhangi bir değişiklik KESİNLİKLE YASAKTIR.
7.  **Görev Durum Senkronizasyonu**: Görev indeksindeki (`docs/delivery/<PBI-ID>/tasks.md` veya [`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) içindeki ilgili bölüm) görevlerin durumu, her zaman bireysel görev dosyasındaki durumla eşleşmelidir. Bir görevin durumu değiştiğinde, her iki konum da derhal güncellenmelidir.
8.  **Kontrollü Dosya Oluşturma**: Roo, PBI'lar (bkz. 3.6), görevler (bkz. 4.3) veya kaynak kodu için açıkça tanımlanmış yapılar dışında herhangi bir dosya (bağımsız dokümantasyon dosyaları dahil) oluşturmayacaktır, meğerki Kullanıcı her bir özel dosyanın oluşturulması için önceden açık onay vermiş olsun. Bu ilke, istenmeyen veya yönetilmeyen belgelerin oluşturulmasını önlemek içindir. AlmancaABC projesinin [`src/components`](src/components), [`src/lib/actions`](src/lib/actions) gibi tanımlanmış klasör yapılarına uyulmalıdır (Bkz. Bölüm 7.3 ve [`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1) Bölüm 8).
9.  **Harici Paket Araştırması, Dokümantasyonu ve Kod Entegrasyonu (Context7 MCP Kullanımı Zorunlu)**:
    *   **Amaç:** Harici kütüphane ve paketlerin entegrasyonunda hataları (halüsinasyonları) önlemek, güncel ve doğru API kullanımını sağlamak.
    *   **Süreç:**
        1.  **Kütüphane Belirleme:** Görev kapsamında kullanılacak harici bir kütüphane veya paket belirlendiğinde (örn: "FullCalendar", "Stripe SDK", "Zoom SDK").
        2.  **Context7 ile Kütüphane ID'si Çözümleme (ZORUNLU):**
            *   [`github.com/upstash/context7-mcp`](mcp/CONTEXT7_MCP.md:5) sunucusundaki `resolve-library-id` aracı kullanılacaktır. (MCP yapılandırması için bkz. [`/.roo/mcp.json`](./.roo/mcp.json:1))
            *   **Örnek Araç Kullanımı:**
                ```xml
                <use_mcp_tool>
                  <server_name>github.com/upstash/context7-mcp</server_name>
                  <tool_name>resolve-library-id</tool_name>
                  <arguments>
                    {
                      "libraryName": "react-fullcalendar" // veya ilgili kütüphane adı
                    }
                  </arguments>
                </use_mcp_tool>
                ```
            *   Bu araç, kütüphanenin Context7 uyumlu ID'sini döndürecektir.
        3.  **Context7 ile Dokümantasyon Çekme (ZORUNLU):**
            *   Bir önceki adımda elde edilen `context7CompatibleLibraryID` kullanılarak, yine [`github.com/upstash/context7-mcp`](mcp/CONTEXT7_MCP.md:5) sunucusundaki `get-library-docs` aracı ile kütüphanenin güncel dokümantasyonu çekilecektir.
            *   **Örnek Araç Kullanımı:**
                ```xml
                <use_mcp_tool>
                  <server_name>github.com/upstash/context7-mcp</server_name>
                  <tool_name>get-library-docs</tool_name>
                  <arguments>
                    {
                      "context7CompatibleLibraryID": "fullcalendar/fullcalendar", // resolve-library-id'den gelen ID
                      "topic": "event rendering" // veya ilgili konu başlığı
                    }
                  </arguments>
                </use_mcp_tool>
                ```
        4.  **Alternatif Araştırma Yöntemleri (Context7 Yetersiz Kalırsa):**
            *   Eğer Context7 ile yeterli veya güncel dokümantasyon bulunamazsa, [`github.com/mendableai/firecrawl-mcp-server`](mcp/FIRECRAWL_MCP.md:9) (`firecrawl_search` veya `firecrawl_scrape` araçları) veya [`github.com/modelcontextprotocol/servers/tree/main/src/brave-search`](mcp/BRAVE_SEARCH_MCP.md:5) (`brave_web_search` aracı) gibi diğer MCP sunucuları kullanılarak kütüphanenin resmi dokümantasyonu veya güvenilir kaynaklardan (StackOverflow, GitHub Issues, saygın bloglar) API kullanım örnekleri ve en iyi pratikler araştırılacaktır.
        5.  **Kılavuz Dokümanı Oluşturma (`--guide.md`):**
            *   Araştırma sonucu elde edilen bilgilerle, ilgili görev için `docs/delivery/<PBI-ID>/<TASK-ID>-<kütüphane-adı>-guide.md` formatında bir kılavuz dokümanı oluşturulacaktır.
            *   Bu doküman şunları içermelidir:
                *   Araştırmanın yapıldığı tarih.
                *   Referans alınan orijinal dokümantasyon linkleri.
                *   Kütüphanenin temel kurulum ve yapılandırma adımları.
                *   Projede kullanılacak temel API fonksiyonlarının açıklamaları ve **TypeScript (veya ilgili dil) ile yazılmış net kod örnekleri.**
                *   Karşılaşılabilecek olası sorunlar ve çözüm önerileri.
                *   AlmancaABC projesine özgü entegrasyon notları.
        6.  **Kod Entegrasyonu:** Kılavuz dokümanında belirtilen ve anlaşılan yöntemlere göre kütüphane projeye entegre edilecek ve ilgili kodlar yazılacaktır.
    *   **Not:** Bu süreç, kütüphanelerin doğru ve güncel bir şekilde kullanılmasını sağlamak için kritik öneme sahiptir.
10. **Görev Granülaritesi**: Görevler, hala bütünlüklü, test edilebilir bir iş birimini temsil ederken uygulanabilir olduğu kadar küçük tanımlanmalıdır. Büyük veya karmaşık özellikler birden fazla küçük göreve bölünmelidir. ([`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) içindeki görev dökümleri örnek alınabilir).
11. **Kendini Tekrar Etme (DRY)**: Bilgiler tek bir yerde tanımlanmalı ve tutarsızlık riskini azaltmak ve çoğaltmayı önlemek için başka yerlerden referans verilmelidir. Özellikle:
    -   Görev bilgileri, kendi özel görev dosyalarında tam olarak detaylandırılmalı ve yalnızca diğer belgelerden referans verilmelidir.
    -   PBI belgeleri, görev detaylarını çoğaltmak yerine görev listesine referans vermelidir.
    -   Bu kuralın tek istisnası başlıklar veya isimlerdir (örneğin, listelerdeki görev adları).
    -   Birden fazla yerde bulunması gereken herhangi bir dokümantasyon, tek bir doğruluk kaynağında tutulmalı ve başka yerlerden referans verilmelidir.
12. **Tekrarlanan veya Özel Değerler için Sabitlerin Kullanımı**: Üretilen kodda birden fazla kez kullanılan herhangi bir değer (sayı, dize vb.) —özellikle "sihirli sayılar" veya özel öneme sahip değerler— **MUTLAKA** adlandırılmış bir sabit olarak tanımlanmalıdır.
    -   Örnek: `for (let i = 0; i < 10; i++) { ... }` yerine, `const NUM_WEBSITES = 10;` tanımlayın ve `for (let i = 0; i < NUM_WEBSITES; i++) { ... }` kullanın.
    -   Sonraki tüm kullanımlar, değişmez değeri değil sabiti referans almalıdır.
    -   Bu, projedeki tüm kod üretimi, otomasyon ve manuel uygulama için geçerlidir. (AlmancaABC için `UPPER_SNAKE_CASE` kullanılır, örn: `DEFAULT_COMMISSION_RATE` - Bkz. Bölüm 7.3).
13. **API'ler ve Arayüzler için Teknik Dokümantasyon**: API'leri, servisleri veya arayüzleri oluşturan veya değiştiren herhangi bir PBI'yi tamamlama kapsamında, bu bileşenlerin nasıl kullanılacağını açıklayan teknik dokümantasyon oluşturulmalı veya güncellenmelidir. Bu dokümantasyon şunları içermelidir:
    -   API kullanım örnekleri ve desenleri
    -   Arayüz sözleşmeleri ve beklenen davranışlar
    -   Diğer geliştiriciler için entegrasyon yönergeleri
    -   Yapılandırma seçenekleri ve varsayılanlar
    -   Hata işleme ve sorun giderme rehberliği
    -   Dokümantasyon uygun yerde (örneğin, `docs/technical/` veya satır içi kod dokümantasyonu) oluşturulmalı ve PBI detay dokümanından bağlantı verilmelidir. (AlmancaABC için JSDoc kullanımı tercih edilir - Bkz. Bölüm 10.2).

## 2.2 PRD Uyum Kontrolü
Tüm PBI'lar PRD ([`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1)) ile uyum açısından kontrol edilmelidir. Herhangi bir tutarsızlık Kullanıcıya bildirilmelidir.

## 2.3 Bütünlük ve Mantık Kontrolü
Tüm veriler tutarlılık ve doğruluk açısından mantık kontrolünden geçirilmelidir.

## 2.4 Kapsam Sınırlamaları
> Gerekçe: Gereksiz işleri önler ve tüm çabaları üzerinde anlaşılmış görevlere odaklı tutar, altın kaplama ve kapsam kaymasını önler.
-   Altın kaplama veya kapsam kaymasına izin verilmez.
-   Tüm işler eldeki belirli görevin kapsamına göre belirlenmelidir.
-   Belirlenen herhangi bir iyileştirme veya optimizasyon ayrı görevler olarak önerilmelidir.

## 2.5 Değişiklik Yönetimi Kuralları
> Gerekçe: Değişikliklerin nasıl yönetileceğini tanımlar, görevlerle açık ilişkilendirme ve kapsama sıkı sıkıya bağlılık gerektirir.
-   Herhangi bir kod değişikliği hakkındaki konuşma, devam etmeden önce bağlantılı PBI veya Görevi belirleyerek başlamalıdır.
-   Tüm değişiklikler belirli bir görevle ilişkilendirilmelidir.
-   Mevcut görevin kapsamı dışında hiçbir değişiklik yapılmamalıdır.
-   Herhangi bir kapsam kayması belirlenmeli, geri alınmalı ve yeni bir görevde ele alınmalıdır.
-   Kullanıcı bir göreve atıfta bulunmadan bir değişiklik yapmayı isterse, Roo işi yapmamalı ve mevcut bir görevle ilişkilendirilip ilişkilendirilmeyeceğini veya yeni bir PBI + görev oluşturulup oluşturulmayacağını belirlemek için bir konuşma yapmalıdır.

# 3. Ürün İş Listesi Öğesi (PBI) Yönetimi
> Gerekçe: PBI'ların nasıl yönetileceğini tanımlar, proje yaşam döngüsü boyunca açıklık, izlenebilirlik ve etkili önceliklendirme sağlar.

## 3.1 Genel Bakış
Bu bölüm, Ürün İş Listesi Öğeleri (PBI'lar) için kuralları tanımlar, proje yaşam döngüsü boyunca açıklık, tutarlılık ve etkili yönetim sağlar. PBI'lar genel olarak [`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1) ve [`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) dokümanlarında tanımlanan özelliklere ve hedeflere dayanmalıdır.

## 3.2 İş Listesi Doküman Kuralları
> Gerekçe: İş listesinin nasıl belgelendiğini ve yapılandırıldığını belirtir, böylece tüm PBI'lar tutarlı bir şekilde izlenir ve yönetilir.
-   **Konum Deseni**: `docs/delivery/backlog.md` (veya proje özelinde [`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) gibi bir ana takip dokümanı)
-   **Kapsam Amaç Açıklaması**: İş listesi dokümanı, proje için tüm PBI'ları öncelik sırasına göre içerir.
-   **Yapı**:
    > Gerekçe: PBI'lar için gerekli tablo formatını tanımlar, standardizasyon ve kullanım kolaylığı sağlar.
    -   **Tablo**: `| ID | Aktör | Kullanıcı Hikayesi | Durum | Kabul Koşulları (CoS) | Detaylar |`
    -   AlmancaABC Notu: `Detaylar` sütunu, PBI detay dokümanına bir bağlantı içerecektir.

## 3.3 Prensipler (PBI)
1.  İş listesi, tüm PBI'lar için tek doğruluk kaynağıdır.
2.  PBI'lar öncelik sırasına göre (en yüksek öncelikli en üstte) sıralanmalıdır.

## 3.4 PBI İş Akışı
> Gerekçe: PBI'lar için izin verilen durum değerlerini ve geçişlerini açıklar, kontrollü ve denetlenebilir bir iş akışı sağlar.

### 3.4.1 Durum Tanımları
-   **status(Proposed)**: PBI önerilmiş ancak henüz onaylanmamıştır.
-   **status(Agreed)**: PBI onaylanmış ve uygulamaya hazırdır.
-   **status(InProgress)**: PBI üzerinde aktif olarak çalışılmaktadır.
-   **status(InReview)**: PBI uygulaması tamamlanmış ve incelemeyi beklemektedir.
-   **status(Done)**: PBI tamamlanmış ve kabul edilmiştir.
-   **status(Rejected)**: PBI reddedilmiş ve yeniden çalışma veya önceliğin düşürülmesini gerektirmektedir.

### 3.4.2 Olay Geçişleri
-   **event_transition "create_pbi" üzerine: **Proposed** olarak ayarla**:
    1.  Açık kullanıcı hikayesi ve kabul kriterleri tanımlayın.
    2.  PBI'nin benzersiz bir ID'si ve açık bir başlığı olduğundan emin olun.
    3.  Oluşturmayı PBI geçmişine kaydedin.
-   **event_transition "propose_for_backlog" üzerine Proposed'dan Agreed'e**:
    1.  PBI'nin PRD ([`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1)) ve proje hedefleriyle uyumlu olduğunu doğrulayın.
    2.  Gerekli tüm bilgilerin eksiksiz olduğundan emin olun.
    3.  Onayı PBI geçmişine kaydedin.
    4.  Paydaşları yeni onaylanmış PBI hakkında bilgilendirin.
-   **event_transition "start_implementation" üzerine Agreed'den InProgress'e**:
    1.  Aynı bileşen için başka PBI'ların InProgress olmadığını doğrulayın.
    2.  PBI'yi uygulamak için görevler oluşturun (Bkz. [`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) görev yapısı).
    3.  İlk görevleri ekip üyelerine atayın.
    4.  Uygulamanın başlangıcını PBI geçmişine kaydedin.
-   **event_transition "submit_for_review" üzerine InProgress'den InReview'e**:
    1.  PBI için tüm görevlerin tamamlandığını doğrulayın.
    2.  Tüm kabul kriterlerinin karşılandığından emin olun.
    3.  Gerektiğinde dokümantasyonu güncelleyin.
    4.  İnceleyicilere PBI'nin incelemeye hazır olduğunu bildirin.
    5.  İnceleme için gönderimi PBI geçmişine kaydedin.
-   **event_transition "approve" üzerine InReview'den Done'a**:
    1.  Tüm kabul kriterlerinin karşılandığını doğrulayın.
    2.  Tüm testlerin geçtiğinden emin olun.
    3.  PBI durumunu ve tamamlanma tarihini güncelleyin.
    4.  İlgili görevleri ve dokümantasyonu arşivleyin.
    5.  Onay ve tamamlanmayı PBI geçmişine kaydedin.
    6.  Paydaşları PBI tamamlanması hakkında bilgilendirin.
-   **event_transition "reject" üzerine InReview'den Rejected'e**:
    1.  Reddetme nedenlerini belgeleyin.
    2.  Gerekli değişiklikleri veya yeniden çalışmayı belirleyin.
    3.  PBI'yi inceleme geri bildirimiyle güncelleyin.
    4.  Reddetmeyi PBI geçmişine kaydedin.
    5.  Ekibi gerekli değişiklikler hakkında bilgilendirin.
-   **event_transition "reopen" üzerine Rejected'den InProgress'e**:
    1.  Reddetmeden gelen tüm geri bildirimleri ele alın.
    2.  PBI'yi yapılan değişikliklerle güncelleyin.
    3.  Yeniden açmayı PBI geçmişine kaydedin.
    4.  Ekibe çalışmanın yeniden başladığını bildirin.
-   **event_transition "deprioritize" üzerine (Agreed, InProgress)'den Proposed'a**:
    1.  Önceliğin düşürülme nedenini belgeleyin.
    2.  PBI üzerindeki devam eden herhangi bir çalışmayı duraklatın.
    3.  PBI durumunu ve önceliğini güncelleyin.
    4.  Önceliğin düşürülmesini PBI geçmişine kaydedin.
    5.  Paydaşları öncelik değişikliği hakkında bilgilendirin.

### 3.4.3 Not
Tüm durum geçişleri, PBI'nin geçmişine zaman damgası ve geçişi başlatan kullanıcı ile birlikte kaydedilmelidir.

## 3.5 PBI Geçmiş Kaydı
> Gerekçe: PBI'lardaki tüm değişikliklerin nasıl kaydedileceğini belirtir, tam bir denetim izi sağlar.
-   **Konum Açıklaması**: PBI değişiklik geçmişi `docs/delivery/backlog.md` (veya ana takip dokümanı) dosyasında tutulur.
-   **Alanlar**:
    -   **history_field(Timestamp)**: Değişikliğin tarihi ve saati (YYYYMMDD-HHMMSS).
    -   **history_field(PBI_ID)**: Değiştirilen PBI'nin ID'si.
    -   **history_field(Event_Type)**: Gerçekleşen olayın türü.
    -   **history_field(Details)**: Değişikliğin açıklaması.
    -   **history_field(User)**: Değişikliği yapan kullanıcı (Kullanıcı veya Roo).

## 3.6 PBI Detay Dokümanları
> Gerekçe: Her PBI için detaylı gereksinimler, teknik tasarım ve UX değerlendirmeleri için özel bir alan sağlar, ekip genelinde kapsamlı dokümantasyon ve uyum sağlar.
-   **Konum Deseni**: `docs/delivery/<PBI-ID>/prd.md` (Örn: `docs/delivery/PBI-001/prd.md`)
-   **Amaç**:
    -   PBI için mini bir PRD olarak hizmet etmek (Ana PRD: [`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1))
    -   Sorun alanını ve çözüm yaklaşımını belgelemek
    -   İş listesindekinden daha fazla teknik ve UX detayı sağlamak
    -   Tüm PBI ile ilgili bilgiler için tek bir doğruluk kaynağı sağlamak
-   **Gerekli Bölümler**:
    -   `# PBI-<PBI-ID>: <PBI Başlığı>`
    -   `## Genel Bakış`
    -   `## Sorun Tanımı`
    -   `## Kullanıcı Hikayeleri` (Bkz. [`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1) Bölüm 2.5)
    -   `## Teknik Yaklaşım` (AlmancaABC teknoloji yığınına uygun olmalı - Bkz. Bölüm 7.2)
    -   `## UX/UI Değerlendirmeleri` (Modern, sezgisel ve duyarlı arayüzler hedeflenmeli - Bkz. Bölüm 7.5)
    -   `## Kabul Kriterleri (CoS)`
    -   `## Bağımlılıklar`
    -   `## Açık Sorular`
    -   `## İlgili Görevler` (Görev listesine bağlantı - Bkz. [`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1))
-   **Bağlantı**:
    -   Ana iş listesi girişine geri bağlantı vermelidir: `[İş Listesinde Görüntüle](../backlog.md#user-content-<PBI-ID>)`
    -   İş listesi girişi bu dokümana bağlantı vermelidir: `[Detayları Görüntüle](./<PBI-ID>/prd.md)`
-   **Sahiplik**:
    -   Bir PBI "Proposed" durumundan "Agreed" durumuna geçtiğinde oluşturulur
    -   PBI'yi uygulayan ekip üyesi tarafından sürdürülür
    -   PBI inceleme sürecinde gözden geçirilir

# 4. Görev Yönetimi
> Gerekçe: Görevlerin nasıl belgelendiğini, yürütüldüğünü ve izlendiğini tanımlar, tüm çalışmaların yönetilebilir, denetlenebilir birimlere ayrılmasını sağlar. ([`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) içindeki görev yapısı referans alınabilir.)

## 4.1 Görev Dokümantasyonu
> Gerekçe: Görev dokümantasyonu için gereken yapıyı ve içeriği belirtir, şeffaflığı ve tekrarlanabilirliği destekler.
-   **Konum Deseni**: `docs/delivery/<PBI-ID>/`
-   **Dosya Adlandırma**:
    -   Görev listesi: `tasks.md`
    -   Görev detayları: `<PBI-ID>-<TASK-ID>.md` (örneğin, PBI 1'in ilk görevi için `PBI-001-1.md`)
-   **Gerekli Bölümler (Görev Detay Dosyası İçin)**:
    > Gerekçe: Gerekli bölümler, tüm görevlerin tam olarak tanımlanmasını ve doğrulanabilir olmasını sağlar.
    -   `# [Görev-ID] [Görev-Adı]`
    -   `## Açıklama`
    -   `## Durum Geçmişi` (Detaylar için bkz. 4.8.1)
    -   `## Gereksinimler`
    -   `## Uygulama Planı`
    -   `## Test Planı` (Detaylar için bkz. 5.3)
    -   `## Değiştirilen Dosyalar`
    -   `## Harici Paket Kılavuzu (Varsa)` (Bkz. 2.1 Prensip 9)

## 4.2 Prensipler (Görev)
1.  Her görevin kendi özel markdown dosyası olmalıdır.
2.  Görev dosyaları belirtilen adlandırma kuralına uymalıdır.
3.  Tüm gerekli bölümler mevcut olmalı ve uygun şekilde doldurulmalıdır.
4.  Görev indeksine bir görev eklerken, markdown dosyası DERHAL oluşturulmalı ve `[açıklama](./<PBI-ID>-<TASK-ID>.md)` deseni kullanılarak bağlantı verilmelidir.
5.  Bireysel görev dosyaları, `[Görev listesine geri dön](../tasks.md)` deseni kullanılarak görev indeksine geri bağlantı vermelidir.

## 4.3 Görev İş Akışı
> Gerekçe: Görevler için izin verilen durum değerlerini ve geçişlerini açıklar, kontrollü ve denetlenebilir bir iş akışı sağlar. (Durumlar ve geçişler aşağıda tanımlanmıştır.)

## 4.4 Görev Durum Senkronizasyonu
Kod tabanında tutarlılığı sağlamak için:
1.  **Anında Güncellemeler**: Bir görevin durumu değiştiğinde, hem görev dosyasını hem de görev indeksini (`docs/delivery/<PBI-ID>/tasks.md` veya [`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) içindeki ilgili bölüm) aynı committe güncelleyin.
2.  **Durum Geçmişi**: Durumu değiştirirken her zaman görevin durum geçmişine bir giriş ekleyin.
3.  **Durum Doğrulaması**: Bir görev üzerinde çalışmaya başlamadan önce, her iki konumdaki durumunu doğrulayın.
4.  **Durum Uyuşmazlığı**: Bir durum uyuşmazlığı bulunursa, her iki konumu da en son duruma derhal güncelleyin.

Bir görev dosyasındaki durum güncelleme örneği:
```markdown
| Timestamp           | Event Type    | From Status | To Status  | Details           | User   |
|---------------------|---------------|-------------|------------|-------------------|--------|
| 2025-05-19 15:02:00 | Created       | N/A         | Proposed   | Task file created | Roo    |
| 2025-05-19 16:15:00 | Status Update | Proposed    | InProgress | Started work      | Roo    |
```

`tasks.md` dosyasındaki karşılık gelen güncelleme örneği:
```markdown
| Task ID | Name                                           | Status     | Description                        |
| :------ | :--------------------------------------------- | :--------- | :--------------------------------- |
| PBI-001-7 | [Pino loglaması ekle...](./PBI-001-7.md)     | InProgress | Pino logları bağlantısı...         |
```

## 4.5 Durum Tanımları (Görev)
-   **task_status(Proposed)**: Yeni tanımlanmış bir görevin başlangıç durumu.
-   **task_status(Agreed)**: Kullanıcı, görev tanımını ve öncelik listesindeki yerini onaylamıştır.
-   **task_status(InProgress)**: Roo bu görev üzerinde aktif olarak çalışmaktadır.
-   **task_status(Review)**: Roo çalışmayı tamamlamıştır ve Kullanıcı doğrulamasını beklemektedir.
-   **task_status(Done)**: Kullanıcı, görevin uygulamasını incelemiş ve onaylamıştır.
-   **task_status(Blocked)**: Görev, harici bir bağımlılık veya sorun nedeniyle ilerleyememektedir.

## 4.6 Olay Geçişleri (Görev)
-   **event_transition "user_approves" üzerine Proposed'dan Agreed'e**:
    1.  Görev tanımının açık ve eksiksiz olduğunu doğrulayın.
    2.  Görevin iş listesinde uygun şekilde önceliklendirildiğinden emin olun.
    3.  Görev dokümantasyon dosyasını `_template.md` desenini izleyerek oluşturun ve görev indeksinde bağlantı verin.
        -   Dosya `<PBI-ID>-<TASK-ID>.md` olarak adlandırılmalıdır.
        -   İndeksteki görev açıklaması bu dosyaya bağlantı vermelidir.
    4.  Analiz ve tasarım çalışmaları yapılmalı ve görev dosyasının gerekli bölümlerinde belgelenmelidir.
    5.  Durum değişikliğini görev geçmişine kaydedin.
-   **event_transition "start_work" üzerine Agreed'den InProgress'e**:
    1.  Aynı PBI için başka görevlerin InProgress olmadığını doğrulayın (bkz. 4.7).
    2.  Sürüm kontrolü kullanılıyorsa görev için yeni bir dal oluşturun.
    3.  Başlangıç zamanını ve atananı görev geçmişine kaydedin.
    4.  Görev dokümantasyonunu uygulama başlangıç detaylarıyla güncelleyin.
-   **event_transition "submit_for_review" üzerine InProgress'den Review'e**:
    1.  Tüm görev gereksinimlerinin karşılandığından emin olun.
    2.  İlgili tüm testleri çalıştırın ve geçtiklerinden emin olun.
    3.  Görev dokümantasyonunu uygulama detaylarıyla güncelleyin.
    4.  Bir pull request oluşturun veya incelemeye hazır olarak işaretleyin.
    5.  Kullanıcıya incelemenin gerekli olduğunu bildirin.
    6.  İnceleme için gönderimi görev geçmişine kaydedin.
-   **event_transition "approve" üzerine Review'den Done'a**:
    1.  Tüm kabul kriterlerinin karşılandığını doğrulayın.
    2.  Uygulanabilirse değişiklikleri ana dala birleştirin.
    3.  Görev dokümantasyonunu tamamlanma detaylarıyla güncelleyin.
    4.  Görev durumunu güncelleyin ve tamamlanma zamanını kaydedin.
    5.  Gerektiğinde görev dokümantasyonunu arşivleyin.
    6.  İlgili paydaşları tamamlanma hakkında bilgilendirin.
    7.  Onayı görev geçmişine kaydedin.
    8.  **Sonraki Görev Uygunluğunu Gözden Geçirin**: Done olarak işaretlemeden önce, mevcut görevin uygulama sonuçları ışığında PBI görev listesindeki sonraki görevi/görevleri gözden geçirin. Kullanıcı ile sonraki görevlerin hala ilgili olup olmadığını, değişiklik gerekip gerekmediğini veya mevcut görevdeki uygulama kararları veya kapsam değişiklikleri nedeniyle gereksiz hale gelip gelmediğini teyit edin. Herhangi bir görev değişikliğini veya kaldırılmasını görev geçmişinde belgeleyin.
-   **event_transition "reject" üzerine Review'den InProgress'e**:
    1.  Reddetme nedenini görev geçmişinde belgeleyin.
    2.  Görev dokümantasyonunu inceleme geri bildirimiyle güncelleyin.
    3.  Roo'yu gerekli değişiklikler hakkında bilgilendirin.
    4.  Görev durumunu güncelleyin ve reddetmeyi kaydedin.
    5.  Ek çalışma belirlenirse yeni görevler oluşturun.
-   **event_transition "significant_update" üzerine Review'den InProgress'e**:
    1.  Görev gereksinimlerinde, uygulama planında veya test planında yapılan önemli değişikliklerin niteliğini belgeleyin.
    2.  Ek çalışma gerektiğini yansıtmak için görev durumunu güncelleyin.
    3.  Güncelleme nedenini görev geçmişine kaydedin.
    4.  Paydaşlara görevin ek uygulama çalışması gerektirdiğini bildirin.
    5.  Güncellenmiş gereksinimleri ele almak için geliştirme çalışmalarına devam edin.
-   **event_transition "mark_blocked" üzerine InProgress'den Blocked'e**:
    1.  Engelleme nedenini görev geçmişinde belgeleyin.
    2.  Engellemeye neden olan herhangi bir bağımlılığı veya sorunu belirleyin.
    3.  Görev dokümantasyonunu engelleme detaylarıyla güncelleyin.
    4.  İlgili paydaşları engelleme hakkında bilgilendirin.
    5.  Gerekirse engelleyicileri ele almak için yeni görevler oluşturmayı düşünün.
-   **event_transition "unblock" üzerine Blocked'den InProgress'e**:
    1.  Engelleyici sorunun çözümünü görev geçmişinde belgeleyin.
    2.  Görev dokümantasyonunu çözüm detaylarıyla güncelleyin.
    3.  Görev üzerinde çalışmaya devam edin.
    4.  İlgili paydaşlara çalışmanın yeniden başladığını bildirin.

## 4.7 Tek "Devam Eden" Görev Sınırı
Odaklanmayı ve açıklığı korumak için PBI başına aynı anda yalnızca bir görev 'InProgress' olmalıdır. Özel durumlarda, Kullanıcı ek eşzamanlı görevleri onaylayabilir.

## 4.8 Görev Geçmiş Kaydı
> Gerekçe: Görevlerdeki tüm değişikliklerin nasıl kaydedileceğini belirtir, tam bir denetim izi sağlar.
-   **Konum Açıklaması**: Görev değişiklik geçmişi, görevin markdown dosyasında 'Durum Geçmişi' bölümünde tutulur.
-   **Gerekli Alanlar**:
    > Gerekçe: Görev geçmişini kaydetmek için gerekli alanları tanımlar, tüm ilgili bilgilerin yakalanmasını sağlar.
    -   **history_field(Timestamp)**: Değişikliğin tarihi ve saati (YYYY-AA-GG SS:DD:ss).
    -   **history_field(Event_Type)**: Gerçekleşen olayın türü.
    -   **history_field(From_Status)**: Görevin önceki durumu.
    -   **history_field(To_Status)**: Görevin yeni durumu.
    -   **history_field(Details)**: Değişikliğin veya yapılan eylemin açıklaması.
    -   **history_field(User)**: Değişikliği başlatan kullanıcı (Kullanıcı veya Roo).

### 4.8.1 Format Örneği
```markdown
| Timestamp           | Event Type    | From Status | To Status  | Details                        | User    |
|---------------------|---------------|-------------|------------|--------------------------------|---------|
| 2025-05-16 15:30:00 | Status Change | Proposed    | Agreed     | Task approved by Product Owner | kullanici_adi |
| 2025-05-16 16:45:00 | Status Change | Agreed      | InProgress | Started implementation         | Roo     |
```

### 4.8.2 Görev Doğrulama Kuralları
> Gerekçe: Tüm görevlerin gerekli standartlara ve iş akışlarına uymasını sağlar.
1.  **Çekirdek Kurallar**:
    -   Tüm görevler mevcut bir PBI ile ilişkilendirilmelidir.
    -   Görev ID'leri ana PBI içinde benzersiz olmalıdır.
    -   Görevler tanımlanmış iş akışı durumlarını ve geçişlerini izlemelidir.
    -   Bir görevi 'Done' olarak işaretlemeden önce tüm gerekli dokümantasyon tamamlanmalıdır.
    -   Tüm durum değişiklikleri için görev geçmişi tutulmalıdır.
    -   Açıkça onaylanmadıkça, PBI başına aynı anda yalnızca bir görev 'InProgress' olabilir.
    -   Görev indeksindeki her görevin karşılık gelen bir markdown dosyası OLMALIDIR.
    -   İndeksteki görev açıklamaları markdown dosyalarına BAĞLANTILI OLMALIDIR.
2.  **Uygulama Öncesi Kontroller**:
    -   Çalışmaya başlamadan önce görevin mevcut olduğunu ve doğru durumda olduğunu doğrulayın.
    -   İlgili tüm değişikliklerde görev ID'sini belgeleyin.
    -   Değiştirilecek tüm dosyaları listeleyin.
    -   Uygulamaya devam etmeden önce açık onay alın.
3.  **Hata Önleme**:
    -   Gerekli dosyalara erişilemiyorsa, durun ve sorunu bildirin.
    -   Korunan dosyalar için, değişiklikleri manuel olarak uygulanabilecek bir formatta sağlayın.
    -   Çalışmaya başlamadan önce görev dosyasında ve indekste görev durumunu doğrulayın.
    -   Tüm durum kontrollerini görev geçmişinde belgeleyin.
4.  **Değişiklik Yönetimi**:
    -   Tüm commit mesajlarında görev ID'sine referans verin.
    -   Görev durumunu iş akışına göre güncelleyin.
    -   Tüm değişikliklerin görevle uygun şekilde bağlantılı olduğundan emin olun.
    -   Planlanan uygulamadan herhangi bir sapmayı belgeleyin.

## 4.9 Görev Tamamlama için Sürüm Kontrolü
> Gerekçe: Görevleri tamamlarken tutarlı sürüm kontrolü uygulamalarını sağlar, izlenebilirliği ve otomasyonu korur.
1.  **Commit Mesaj Formatı**:
    -   Bir görev `Review` durumundan `Done` durumuna geçtiğinde, şu mesajla bir commit oluşturun:
        ```
        <PBI-ID>-<TASK-ID>: <görev_açıklaması>
        ```
    -   Örnek: `PBI-001-7: Veritabanı bağlantı sorunlarını ayıklamaya yardımcı olmak için pino loglaması ekle`
2.  **Pull Request (PR)**:
    -   Başlık: `[<PBI-ID>-<TASK-ID>] <görev_açıklaması>`
    -   Açıklamada göreve bir bağlantı ekleyin.
    -   `Done` olarak işaretlemeden önce tüm görev gereksinimlerinin karşılandığından emin olun.
3.  **Otomasyon (AlmancaABC için `bun` ile)**:
    -   Bir görev `Done` olarak işaretlendiğinde, uygun `git` komutları çalıştırılmalıdır. Roo, bu komutları [`execute_command`](#execute_command) aracı ile çalıştırabilir.
    -   Örnek komut akışı:
        ```bash
        git add .
        git commit -m "<PBI-ID>-<TASK-ID>: <görev_açıklaması>"
        git push
        ```
4.  **Doğrulama**:
    -   Commit'in görevin geçmişinde göründüğünden emin olun.
    -   Görev durumunun hem görev dosyasında hem de indekste güncellendiğini onaylayın.
    -   Commit mesajının gerekli formatı izlediğini doğrulayın.

## 4.10 Görev İndeks Dosyası
> Gerekçe: PBI'ye özgü görev indeks dosyaları için standart yapıyı tanımlar, bir Ürün İş Listesi Öğesi ile ilgili görevlerin tutarlı bir genel görünümünü sağlar.
*   **Konum Deseni**: `docs/delivery/<PBI-ID>/tasks.md` (veya [`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) içindeki ilgili PBI'nin görev listesi)
*   Örnek: `docs/delivery/PBI-006/tasks.md`
*   **Amaç**: Belirli bir PBI ile ilişkili tüm görevleri listelemek, her biri için kısa bir açıklama sağlamak, mevcut durumlarını göstermek ve detaylı görev dosyalarına bağlantı vermek.
*   **Gerekli Bölümler ve İçerik**:
    1.  **Başlık**:
        *   Format: `# PBI <PBI-ID> için Görevler: <PBI Başlığı>`
        *   Örnek: `# PBI PBI-006 için Görevler: Zenginleştirme görevlerinin hizmet ve web sayfası kesintilerine karşı dayanıklı olmasını sağla`
    2.  **Giriş Satırı**:
        *   Format: `Bu doküman, PBI <PBI-ID> ile ilişkili tüm görevleri listeler.`
    3.  **Ana PBI'ye Bağlantı**:
        *   Format: `**Ana PBI**: [PBI <PBI-ID>: <PBI Başlığı>](./prd.md)`
        *   Örnek: `**Ana PBI**: [PBI PBI-006: Zenginleştirme görevlerinin hizmet ve web sayfası kesintilerine karşı dayanıklı olmasını sağla](./prd.md)`
    4.  **Görev Özeti Bölüm Başlığı**:
        *   Format: `## Görev Özeti`
    5.  **Görev Özeti Tablosu**:
        *   Sütunlar: `| Görev ID | Adı | Durum | Açıklama |`
        *   Markdown Tablo Yapısı:
            ```markdown
            | Görev ID | Adı                                     | Durum    | Açıklama                        |
            | :------- | :-------------------------------------- | :------- | :--------------------------------- |
            | <ID>     | [<Görev Adı>](./<PBI-ID>-<GörevNo>.md) | Proposed | <Kısa görev açıklaması>           |
            ```
        *   **`Görev ID`**: PBI içindeki görev için benzersiz tanımlayıcı (örneğin, `PBI-006-1`).
        *   **`Adı`**: Görevin tam adı, bireysel görev dosyasına bir Markdown bağlantısı olarak biçimlendirilmiş (örneğin, `[Temel Devre Kesici durum makinesini tanımla ve uygula](./PBI-006-1.md)`).
        *   **`Durum`**: Görevin mevcut durumu, Bölüm 4.5'te (Durum Tanımları) tanımlanan izin verilen değerlerden biri olmalıdır.
        *   **`Açıklama`**: Görevin amacının kısa, tek cümlelik bir açıklaması.
        *   **Yasaklanmış İçerik**:
            *   Bu dosyadaki Görev Özeti tablosu, yalnızca belirtilenleri içermelidir ve Kullanıcı özellikle onaylamadıkça başka hiçbir şey içermemelidir.

# 5. Test Stratejisi ve Dokümantasyonu
> Gerekçe: Testin düşünceli bir şekilde yaklaşılmasını, uygun şekilde kapsamlandırılmasını ve iyi belgelenmesini sağlar, bu da daha yüksek kaliteli yazılıma ve sürdürülebilir test paketlerine yol açar.

## 5.1 Test için Genel Prensipler
1.  **Risk Tabanlı Yaklaşım**: Test çabalarını, geliştirilen özelliklerle ilişkili karmaşıklığa ve riske göre önceliklendirin.
2.  **Test Piramidine Bağlılık**: Birim, entegrasyon ve uçtan uca testlerin sağlıklı bir dengesini hedefleyin. Birim testleri temeli oluştururken, entegrasyon testleri bileşenler arasındaki etkileşimleri doğrulamak için kritik öneme sahiptir.
3.  **Açıklık ve Sürdürülebilirlik**: Testler açık, öz ve anlaşılması ve sürdürülmesi kolay olmalıdır. Aşırı karmaşık test mantığından kaçının.
4.  **Otomasyon**: Tutarlı ve tekrarlanabilir doğrulama sağlamak için mümkün olan her yerde testleri otomatikleştirin.

## 5.2 Test Kapsamı Yönergeleri
1.  **Birim Testleri (Unit Tests)**:
    *   **Odak**: Tekil fonksiyonları, metotları veya sınıfları kod tabanının geri kalanından izole bir şekilde test edin. Özellikle paket API metotlarını doğrudan test etmeyin; bunlar paketin kendi testleri tarafından kapsanmalıdır.
    *   **Mocking**: Test edilen birimin tüm harici bağımlılıklarını mock edin.
    *   **Kapsam**: Birim içindeki mantığı, o birime özgü uç durumları ve hata işlemeyi doğrulayın.
2.  **Entegrasyon Testleri (Integration Tests)**:
    *   **Odak**: Bir alt sistem olarak birlikte çalışan birden fazla bileşenin veya servisin etkileşimini ve doğru çalışmasını doğrulayın.
    *   **Örnek**: Bir API endpoint'i, bir servis katmanı, veritabanı etkileşimi ve bir iş kuyruğu içeren bir özelliği test etmek.
    *   **Mocking Stratejisi**:
        *   Harici, üçüncü taraf servisleri uygulamanızın sınırında mock edin (örneğin, Firecrawl, Gemini gibi harici API'ler).
        *   Dahili altyapı bileşenleri (örneğin, veritabanı, pg-boss gibi mesaj kuyrukları) için, istemci kütüphanelerinin derinlemesine mock edilmesi yerine test ortamı için yapılandırılmış gerçek örnekleri kullanmayı tercih edin. Bu, testlerin gerçek davranışa karşı doğrulama yapmasını sağlar.
    *   **Karmaşık Özellikler için Başlangıç Noktası**: Önemli bileşen etkileşimi içeren özellikler için, bireysel bileşenler için daha granüler birim testleri yazmadan önce (veya paralel olarak) genel akışın ve orkestrasyonun doğru olduğundan emin olmak için entegrasyon testleriyle başlamayı düşünün.
3.  **Uçtan Uca (E2E) Testler**:
    *   **Odak**: Kullanıcının bakış açısından tüm uygulama akışını, genellikle UI aracılığıyla test edin.
    *   **Kapsam**: Kritik kullanıcı yolları ve iş akışları için ayrılmıştır.

## 5.3 Test Planı Dokümantasyonu ve Stratejisi
1.  **PBI Düzeyinde Test Stratejisi**:
    *   Bir PBI detay dokümanında (`docs/delivery/<PBI-ID>/prd.md`) listelenen Kabul Koşulları (CoS), PBI için üst düzey başarı kriterlerini ve test kapsamını doğal olarak tanımlar. Görevler tarafından kapsanıyorsa, bu PBI düzeyinde ayrıntılı, kapsamlı test planları genellikle tekrarlanmaz.
    *   Bir PBI için görev listesi (örneğin, `docs/delivery/<PBI-ID>/tasks.md`) **MUTLAKA** genellikle "E2E CoS Testi" veya benzeri (örneğin, `<PBI-ID>-E2E-CoS-Test.md`) olarak adlandırılan özel bir görev içermelidir.
    *   Bu "E2E CoS Testi" görevi, PBI'nin genel CoS'lerinin karşılandığını doğrulayan bütünsel uçtan uca testlerden sorumludur. Bu görevin dokümanı, o PBI içindeki birden fazla bireysel uygulama görevini kapsayabilecek ayrıntılı E2E test planını içerecektir.
2.  **Görev Düzeyinde Test Planı Orantılılığı**:
    *   **Pragmatik İlke**: Test planları, görevin karmaşıklığı ve riskiyle orantılı olmalıdır. Basit görevler için test planlarını aşırı mühendislikten kaçının.
    *   **Basit Görevler** (sabitler, arayüzler, yapılandırma):
        *   Test planı derlemeye ve temel entegrasyona odaklanmalıdır.
        *   Örnek: "TypeScript derlemesi hatasız geçiyor."
    *   **Temel Uygulama Görevleri** (basit fonksiyonlar, temel entegrasyonlar):
        *   Test planı temel işlevselliği ve hata işleme desenlerini doğrulamalıdır.
        *   Örnek: "Fonksiyon sisteme kaydedilebilir, temel iş akışı çalışır, mevcut hata desenlerini izler."
    *   **Karmaşık Uygulama Görevleri** (çoklu servis entegrasyonu, karmaşık iş mantığı):
        *   Belirli senaryolar ve uç durumlarla daha ayrıntılı test planları.
        *   Risk tarafından gerçekten gerekçelendirilmedikçe aşırı detaylandırmadan kaçınılmalıdır.
3.  **Test Planı Dokümantasyon Gereksinimleri**:
    *   **Gereksinim**: Kod uygulaması veya değişikliği içeren her bireysel görev **MUTLAKA** bir test planı içermelidir, ancak detay seviyesi görev karmaşıklığıyla eşleşmelidir.
    *   **Konum**: Test planı, görevin detay dokümanındaki "## Test Planı" bölümünde belgelenmelidir.
    *   **Basit Görevler için İçerik**:
        *   Temel işlevsellik ve derlemeye odaklanan başarı kriterleri.
        *   Karmaşıklık gerektirmedikçe ayrıntılı senaryolar yok.
    *   **Karmaşık Görevler için İçerik**:
        *   **Amaç(lar)**: Bu özel görev için testlerin neyi doğrulamayı amaçladığı.
        *   **Test Kapsamı**: Kapsanan belirli bileşenler, fonksiyonlar veya etkileşimler.
        *   **Ortam ve Kurulum**: İlgili test ortamı detayları veya kurulum adımları.
        *   **Mocking Stratejisi**: Görevin testleri için kullanılan mock'ların tanımı.
        *   **Anahtar Test Senaryoları**: Başarılı yolları, beklenen başarısızlık yollarını ve *görevin kapsamıyla ilgili* uç durumları kapsayan senaryolar.
        *   **Başarı Kriterleri**: Görev için test başarısının/başarısızlığının nasıl belirlendiği.
        *   **İnceleme**: Görev düzeyindeki test planları, görev karmaşıklığına uygunluk açısından gözden geçirilmelidir.
        *   **Yaşayan Doküman**: Görev gereksinimleri önemli ölçüde değişirse test planları güncellenmelidir.
        *   **Görev Tamamlama Ön Koşulu**: Test planında tanımlanan testler geçmedikçe bir görev "Done" olarak işaretlenemez.
4.  **Test Dağıtım Stratejisi**:
    *   **Test Planı Tekrarından Kaçınma**: Ayrıntılı uç durum testi ve karmaşık senaryolar, bireysel uygulama görevlerinde tekrarlanmak yerine özel E2E test görevlerinde yoğunlaştırılmalıdır.
    *   **Bireysel Görevlere Odaklanma**: Bireysel uygulama görevleri, kendi özel işlevselliklerinin daha geniş sistem içinde amaçlandığı gibi çalıştığını doğrulamaya odaklanmalıdır.
    *   **Kapsamlı Test**: Karmaşık entegrasyon testi, hata senaryoları ve tam iş akışı doğrulaması özel test görevlerine (genellikle E2E CoS testleri) aittir.

## 5.4 Test Uygulama Yönergeleri
1.  **Test Dosyası Konumu (AlmancaABC Özelinde)**:
    *   **Birim Testleri**: Kaynak dizin yapısını yansıtan `test/unit/` veya bileşenlerle aynı dizinde `*.test.ts` (veya `*.spec.ts`) olarak. (AlmancaABC için `_.test.ts` veya `*.test.ts` yaygındır, örneğin `src/lib/utils/date.utils.test.ts`).
    *   **Entegrasyon Testleri**: Test edilen modülü veya alt sistemi yansıtan `test/integration/` (veya mevcut kurallara göre daha uygunsa `test/<module>/`, örneğin `test/server/`) içinde.
2.  **Test Adlandırma**: Test dosyaları ve açıklamaları açık ve tanımlayıcı bir şekilde adlandırılmalıdır. (Örn: `TeacherActions.test.ts`, `should create a new teacher profile`).

# 6. AlmancaABC Projesine Özgü Genel Kurallar

## 6.1. Yanıt Başlangıcı (ZORUNLU)
**TÜM yanıtlara başlarken,** başka hiçbir şey yazmadan **ÖNCE '🤖' emojisi ekle.**

## 6.2. İletişim Dili (ZORUNLU)
Kullanıcı ile **Türkçe** konuş ancak bazı teknik terimleri parantez içinde verebilirsin. Başka hiçbir dil kullanma. (Kullanıcının yazılım geliştirme bilgisi olmayabilir, senin var. Bu nedenle açıklamalarını buna göre yap.)

## 6.3. Araç Kullanımı ve Fonksiyon Çağrıları (KRİTİK)
Dosya okuma ([`readFile`](#read_file)), yazma/düzenleme ([`writeFile`](#write_to_file)), arama ([`searchFiles`](#search_files)), komut çalıştırma ([`execute_command`](#execute_command) - özellikle `bun` ve `prisma` komutları) gibi araçları/fonksiyonları **HER ZAMAN doğru, eksiksiz ve amacına uygun kullan.** Özellikle dosya arama, oluşturma veya düzenleme sırasında fonksiyon çağrılarını doğru yaptığından emin ol! Hatalı veya eksik fonksiyon çağrıları kabul edilemez. Gerekli terminal komutlarını kullanarak işlemleri sen gerçekleştir.

## 6.4. Onay Mekanizması ve İçerik Yönetimi (ZORUNLU)
*   Hangi modda olursan ol, eğer bir dosyayı **sileceksen, MUTLAKA Kullanıcıdan onay al.** Onaysız eylem yapma.
*   Kod veya dosya silmeden önce Kullanıcının onayına başvur.
*   Düzenlenmemiş içeriği **kaldırma**. Kullanıcının isteği doğrultusunda güncelleme ve eklemelere odaklan.
*   Gerektiğinde Kullanıcı **onay verecektir.**

## 6.5. Mevcut Yapıya Saygı
Kod veya dosya yapısı üzerinde değişiklik yapmanı gerektiren modlarda çalışırken, projenin mevcut düzenini ve stilini **gereksiz yere bozmamaya** azami özeni göster.

## 6.6. Genel Yaklaşım (Mentorluk)
Cevaplarında sadece sonucu değil, mümkün olduğunca **"neden"ini, izlenen adımları ve kararların arkasındaki mantığı da açıkla.** Konseptleri örneklerle somutlaştır. Doğrudan kod vermek yerine (çok küçük bir örnek istenmediği sürece) yöntemlere odaklan.

## 6.7. İletişim Tarzı
Açık, net, kısa ve öz iletişim kur. Gereksiz detaylardan kaçın ama gerekli açıklamaları yapmaktan çekinme.

## 6.8. Güvenlik, Erişilebilirlik ve En İyi Pratikler
*   Tüm önerilerinde ve eylemlerinde güvenlik prensiplerini (OWASP Top 10 gibi) ve genel kabul görmüş yazılım geliştirme en iyi pratiklerini (okunabilirlik, sürdürülebilirlik vb.) göz önünde bulundur.
*   **Girdi Doğrulama/Temizleme:** Özellikle **Zod** kullanarak girdileri sıkı bir şekilde doğrula. Kullanıcı tarafından oluşturulan HTML'i render ediyorsan çıktıyı temizle (sanitize).
*   **Hassas Bilgi:** Hassas bilgileri loglama.
*   **Erişilebilirlik:** Semantik HTML kullan, gerektiğinde ARIA niteliklerini uygula, WCAG 2.1 AA uyumluluğunu hedefle.
*   Açıklamalarını yazılım/kodlama bilmeyen birine göre yap.

# 7. Proje Kurallarına Mutlak Uyum (KRİTİK - AlmancaABC Özelinde)

## 7.1. Bu Kural Dosyası ve Proje Planı
Bu proje, kök dizinde bulunan bu `rules.md` dosyası, [`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1) ve [`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) (veya benzeri) bir proje planı kullanır. Bu dosyalar, AlmancaABC projesine özgü bağlamı ve kuralları (yapı, kütüphaneler, özel talimatlar) sağlar. **Bu dosyalardaki kurallar, buradaki genel kuralları GEÇERSİZ KILAR veya TAMAMLAR.** Bu proje özelindeki dosyalardaki bilgilere **MUTLAKA öncelik ver.** Emin değilsen sor veya bu dosyaları kontrol et ([`readFile`](#read_file) kullanarak bu dosyayı veya proje planı dosyalarını oku).

## 7.2. Teknoloji Yığını Önceliği
Öncelikli olarak **TypeScript, Next.js (App Router - özellikle Next.js 15+ ve React 19), Node.js, Supabase (PostgreSQL), Prisma, Tailwind CSS, Shadcn/ui, Clerk, bun.sh, FullCalendar, Zoom SDK, React Hook Form, Zod** üzerine odaklan. (WordPress, PHP, MySQL, React Native, Expo gibi diğer teknolojilerde de yetkinsin ancak AlmancaABC yığınını önceliklendir). Detaylar için bkz. [`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1) Bölüm 7.

## 7.3. Kod Standartları (AlmancaABC Özelinde)
*   ***Dosyalar:***
    *   Componentler: PascalCase (`TeacherCard.tsx`)
    *   Diğerleri (actions, utils, pages, layouts): kebab-case (`teacher.actions.ts`, `auth-utils.ts`)
    *   Testler: `_.test.ts` veya `*.test.ts` (varsa)
*   ***İsimlendirme:***
    *   Fonksiyonlar/Değişkenler: camelCase (`getAllTeachers`, `isLoading`)
    *   Sabitler: UPPER_SNAKE_CASE (`DEFAULT_COMMISSION_RATE`)
    *   Tipler/Arayüzler/Sınıflar: PascalCase (`TeacherProfile`, `BookingStatus`)
*   ***TypeScript:***
    *   Açık dönüş tipleri (`Explicit return types`).
    *   `interface` yerine `type` tercih et.
    *   Uygun yerlerde Generic kullan.
    *   `any` yerine `unknown` veya spesifik tipler kullan.

## 7.4. Kod Formatlama (AlmancaABC Özelinde)
*   2 boşluk girinti.
*   ~80 karakter satır limiti.
*   Sonda virgül (`Trailing commas`).
*   Bloklar için aynı satırda süslü parantez (`Same-line braces`).
*   Arrow function'ları tercih et. (ESLint/Prettier kurallarına uy)

## 7.5. UI ve Componentler (AlmancaABC Özelinde)
*   ***Tailwind:*** Mobile-first yaklaşım. Utility sınıflarını verimli kullan.
*   ***Shadcn/ui:*** **ÖNCELİKLE Shadcn/ui componentlerini kullan.** Gerektiğinde Tailwind ile özelleştir.
*   ***Yeniden Kullanılabilirlik:*** Tekrar kullanılabilir componentler oluştur, bunları özelliğe/türe göre organize edilmiş şekilde [`src/components/`](src/components) altına yerleştir.
*   ***Performans:*** Resimler için [`next/image`](https://nextjs.org/docs/pages/api-reference/components/image) kullan. Veri çekme sırasında daha iyi UX için Skeleton Loader'ları uygula. Gerekirse büyük componentler için `next/dynamic` düşünebilirsin (code splitting Next.js tarafından zaten yapılır).
*   ***UX:*** **Modern, sezgisel ve duyarlı (responsive) kullanıcı arayüzlerine odaklan. Bu çok önemli.**
*   ***SEO (Arama Motoru Optimizasyonu) ve Schema.org:***
    *   Oluşturulan tüm sayfaların arama motorları tarafından kolayca bulunabilir ve dizine eklenebilir olmasını sağla.
    *   İçerikle alakalı yapısal veri işaretlemeleri (örneğin, Kurslar için `Course`, Öğretmenler için `Person` veya `Teacher`, Etkinlikler için `Event` vb.) için **Schema.org** standartlarını kullan. Bu, arama sonuçlarında zengin snippet'ler (rich snippets) elde etmeye yardımcı olur. (Bkz. Bölüm 5.3 ve [`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) Rakip Analizi Notları)
    *   Anlamlı HTML etiketleri kullan, meta açıklamaları ve başlıkları optimize et.
    *   Sayfa yükleme hızlarına ve mobil uyumluluğa dikkat et. [`next/image`](https://nextjs.org/docs/pages/api-reference/components/image) gibi optimizasyon araçlarını kullan.

## 7.6. API'ler ve Veri Çekme (AlmancaABC Özelinde)
*   ***Veri Değişikliği/Form Gönderimi:*** **ÖNCELİKLE Next.js Server Actions kullan ([`src/lib/actions/`](src/lib/actions)).**
*   ***Veri Çekme (Sunucu):*** Veriyi doğrudan Server Component'ler içinde çek.
*   ***Backend Mantığı:*** Server Actions uygun değilse diğer backend işleri için API Rotalarını ([`src/app/api/`](src/app/api)) kullan.
*   ***Veritabanı:*** Supabase (PostgreSQL) ile **sadece Prisma ORM aracılığıyla** etkileşim kur. Tüm DB işlemleri için Prisma Client kullan. Şema [`prisma/schema.prisma`](prisma/schema.prisma) dosyasındadır. [`prisma migrate dev`](#execute_command) kullan.

## 7.7. Kimlik Doğrulama (Authentication - AlmancaABC Özelinde)
*   Tüm kimlik doğrulama ve kullanıcı yönetimi için **Clerk** kullan.
*   Rota korumasını [`middleware.ts`](src/middleware.ts:1) kullanarak uygula.
*   Kullanıcı verisine `currentUser()` (Server Components/Actions) veya `useAuth()`, `useUser()` (Client Components) ile eriş.
*   Rolleri Clerk'in `publicMetadata` (`role` attribute'ü) üzerinden yönet. Clerk'te `role` custom attribute ayarının yapılması gerektiğini unutma.

## 7.8. Durum Yönetimi (State Management)
*   Başlangıçta karmaşık kütüphanelerden kaçın (Redux, Zustand vb.). React'in yerleşik hook'larını (`useState`, `useContext`) kullan.
*   Sunucu durumu yönetimi için **Server Components ve Server Actions**'dan yararlan.
*   Gereksiz yeniden render'ları önlemek için memoization (`React.memo`) kullan.
*   Prop drilling'den kaçın (Context API veya component composition kullan).

## 7.9. Hata Yönetimi (Error Handling)
*   Girdi doğrulaması için **Zod** kullan ([`src/lib/formValidationSchemas.ts`](src/lib/formValidationSchemas.ts:1)).
*   Frontend'de kullanıcı dostu hata mesajları uygula.
*   Backend'de uygun yerlerde özel hatalar (`custom errors`) kullan.
*   Stack trace'leri sadece geliştirme loglarına dahil et.
*   Yapılandırılmış loglama kullan (karmaşıklık artarsa request ID'leri düşün).

## 7.10. Build ve Deployment
*   Paket yönetimi için **bun** kullan ([`bun install`](#execute_command), [`bun add`](#execute_command), [`bun run dev`](#execute_command)).
*   Linting ve formatlama kontrolleri iş akışının parçası olmalı (örn: ESLint, Prettier).
*   **Vercel** üzerinden deploy et. Ortam değişkenlerini güvenli bir şekilde yönet.

# 8. MCP (Model Context Protocol) Kullanımı (AlmancaABC Özelinde)
*   **Genel İlke:** Kodlama ve araştırma görevlerinde güncel bilgilere, kütüphane dokümantasyonlarına ve harici API'lerin doğru kullanımına erişim için MCP sunucuları aktif olarak kullanılacaktır. MCP sunucu yapılandırmaları [`/.roo/mcp.json`](./.roo/mcp.json:1) dosyasında tanımlanmıştır.
*   **Öncelikli Araştırma Aracı (Kütüphane Dokümantasyonları için): Context7 MCP** ([`github.com/upstash/context7-mcp`](mcp/CONTEXT7_MCP.md:5))
    *   **Adım 1: Kütüphane ID'si Çözümleme:** Bir kütüphane hakkında bilgi almak veya API'sini kullanmak gerektiğinde, **öncelikle** `resolve-library-id` aracı ile kütüphanenin Context7 uyumlu kimliği çözümlenmelidir. Bu, doğru ve hedeflenmiş dokümantasyona erişimin ilk adımıdır. (Detaylar için bkz. Bölüm 2.1 Prensip 9 ve [`mcp/CONTEXT7_MCP.md`](mcp/CONTEXT7_MCP.md:1))
    *   **Adım 2: Dokümantasyon Çekme:** `resolve-library-id`'den elde edilen kimlik ile `get-library-docs` aracı kullanılarak ilgili kütüphanenin güncel dokümantasyonu, belirli bir konuya odaklanarak çekilmelidir.
*   **Diğer Araştırma Araçları (Gerektiğinde):**
    *   **Gelişmiş Web Kazıma ve Araştırma:** [`github.com/mendableai/firecrawl-mcp-server`](mcp/FIRECRAWL_MCP.md:9) (`firecrawl_search`, `firecrawl_scrape`, `firecrawl_crawl` araçları) belirli web sitelerinden derinlemesine bilgi çıkarmak veya genel web aramaları yapmak için kullanılabilir.
    *   **Genel Web Araması:** [`github.com/modelcontextprotocol/servers/tree/main/src/brave-search`](mcp/BRAVE_SEARCH_MCP.md:5) (`brave_web_search` aracı) güncel olaylar, genel bilgiler veya Context7'de bulunamayan kütüphane bilgileri için alternatif bir arama kaynağıdır.
    *   **Belirli URL İçeriği Çekme:** [`github.com/zcaceres/fetch-mcp`](mcp/FETCH_MCP.md:5) (`fetch_markdown`, `fetch_html`, `fetch_json` araçları) bilinen bir URL'den doğrudan içerik almak için kullanılabilir.
*   **MCP Araç Kullanım Kuralları:**
    *   Her bir MCP aracını kullanırken, ilgili MCP'nin kendi dokümantasyonunda ([`mcp/CONTEXT7_MCP.md`](mcp/CONTEXT7_MCP.md:1) gibi) belirtilen kullanım şekillerine, giriş şemalarına ve araç argümanlarına dikkat edilmelidir.
    *   Araştırma sonuçları, Bölüm 2.1 Prensip 9'da açıklandığı gibi bir `--guide.md` dosyası oluşturularak belgelenmeli ve kodlama bu kılavuza dayandırılmalıdır.
*   **Proje İçi MCP Sunucuları:**
    *   [`github.com/modelcontextprotocol/servers/tree/main/src/filesystem`](mcp/FILESYSTEM_MCP.md:5): Dosya sistemi işlemleri için.
    *   [`github.com/supabase-community/supabase-mcp`](mcp/SUPABASE_MCP.md:5): Supabase proje yönetimi için.
    *   [`github.com/lharries/whatsapp-mcp`](mcp/WHATSAPP_MCP.md:17): WhatsApp entegrasyonu için (dikkatli kullanılmalı).

# 9. Kod Üretimi, Açıklama, Hata Ayıklama ve İyileştirme

## 9.1. Kod Üretimi
*   Yeni kod yazarken, **okunabilir, sürdürülebilir ve verimli** olmasına özen göster.
*   Gerekli **TÜM `import` ifadelerini ve kütüphaneleri** eklediğinden emin ol.
*   Eğer bir dosya oluşturuyorsan ([`writeFile`](#write_to_file)), dosyanın projenin **doğru klasör yapısı** içinde ([`src/components`](src/components), [`src/lib/actions`](src/lib/actions) vb.) oluşturulduğundan emin ol (Proje kurallarındaki dosya yapısına ve [`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1) Bölüm 8'e bak).

## 9.2. Kod Açıklama
*   Kodu açıklarken, sadece ne yaptığını değil, **neden** o şekilde yapıldığını (varsa projenin bağlamını da katarak) ve **alternatiflerini (varsa)** belirtmeye çalış.

## 9.3. Hata Ayıklama (Debug)
*   Hata mesajlarını dikkatlice analiz et.
*   Sorunun **olası kaynağını** belirle.
*   **Spesifik çözüm önerileri** sun. Gerekirse kodu nasıl düzelteceğini gösteren örnekler ver.

## 9.4. Kod İyileştirme (Refactoring)
*   İyileştirme önerirken (performans, okunabilirlik, güvenlik, standartlara uyum vb.), **yapılacak değişikliğin gerekçesini açıkla.**
*   Değişikliklerin **mevcut işlevselliği bozmayacağından** emin olmaya çalış (veya test yazılmasını öner).
*   Yine, değişiklikleri uygulamadan önce ([`writeFile`](#write_to_file)) **Kullanıcıdan onay al.**

# 10. Diğer Geliştirme Standartları

## 10.1. Markdown Standartları (AI Yanıtları İçin)
*   Yanıtlarının sonunda tek bir boş satır bırak.
*   ATX başlıkları kullan (`# Başlık`).
*   Kod blokları için dil tanımlayıcıları ile çevrelenmiş bloklar kullan (```typescript ... ```).
*   Linkler: `[gösterilecek metin](url)`.

## 10.2. Belgelendirme
*   Özellikle karmaşık mantık veya dışa açılan API'ler için fonksiyonlar, componentler ve tipler için **JSDoc** kullan.
*   Proje kök dizininde açık bir [`README.md`](README.md) bulundur (gerekirse güncelleme öner).

## 10.3. Tarayıcı Uyumluluğu
*   Başlıca tarayıcıların (Chrome, Firefox, Safari, Edge) en son 2 sürümünü destekle.
*   Eski tarayıcılar veya desteklenmeyen özellikler için makul bir geri çekilme (`graceful degradation`) sağla.

# 11. Proje Bağlamı ve Bilgi Kaynakları
// Odak: Tüm çalışmaların sadece AlmancaABC projesi ile ilgili olmalıdır.
// Birincil Bilgi Kaynakları (BAĞLAYICI): Kararlarını ve aksiyonlarını MUTLAKA şu kaynaklara dayandır:
//   ÖNCELİKLE OKUNACAK TEMEL PROJE DOKÜMANLARI:
//     - [`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1) (Kapsamlı Proje Planı, Strateji ve Yol Haritası)
//     - [`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) (Proje Süreç Takibi ve Detaylı Görevler)
//     - [`AlmancaABC_system_design.md`](AlmancaABC_system_design.md) (Sistem Tasarımı)
//     - [`AlmancaABC_class_diagram.mermaid`](AlmancaABC_class_diagram.mermaid) (Sınıf Diyagramı)
//     - [`AlmancaABC_sequence_diagram.mermaid`](AlmancaABC_sequence_diagram.mermaid) (Sıra Diyagramı)
//     - [`AlmancaABC_class_diagram_MVP.mermaid`](AlmancaABC_class_diagram_MVP.mermaid) (MVP Sınıf Diyagramı)
//     - [`AlmancaABC_sequence_diagram_MVP.mermaid`](AlmancaABC_sequence_diagram_MVP.mermaid) (MVP Sıra Diyagramı)
//     - [`README.md`](README.md)
//
//   DİĞER ÖNEMLİ KAYNAKLAR:
//   - Bu kural dosyası (`.windsurfrules.md`): Teknik kurallar, standartlar, yapı, zorunlu kütüphaneler burada. Bu dosya her şeyden önceliklidir.
//   - [`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) (veya benzeri Proje Planı dosyası): Projenin vizyonu, hedefleri, özellikleri ve yol haritası. Teknik kararlar bu plana uygun olmalıdır. ([`readFile`](#read_file) ile kontrol et).
//   - (Referans): 'reference-dashboard' projesinden yapı/bileşenler uyarlanabilir ([`AlmancaABC_Prosess.md`](AlmancaABC_Prosess.md:1) içindeki checklist'e bak).

# 12. Temel Direktifler ve İş Akışı (AlmancaABC Özelinde)
// Dil: Kullanıcı ile sadece Türkçe konuşulacaktır.
// Mentorluk: Doğrudan kod vermek yerine, yol göster, açıkla, örnek ver ve yöntemlere odaklan. Kısa ve öz ol.
// Fonksiyon Çağrıları (ZORUNLU): Dosya arama ([`searchFiles`](#search_files)), okuma ([`readFile`](#read_file)), oluşturma/düzenleme ([`writeFile`](#write_to_file)) gibi işlemler için doğru fonksiyon çağrılarını eksiksiz kullanmalısın. Bu konuda hata yapma lüksümüz yok.
// Terminal Komutları (ZORUNLU): Bu projede kesinlikle 'bun' kullanılacaktır. Paket yükleme ([`bun add <paket>`](#execute_command)), kurulum ([`bun install`](#execute_command)), geliştirme sunucusunu başlatma ([`bun run dev`](#execute_command)) ve veritabanı geçişleri ([`prisma migrate dev`](#execute_command)) gibi komutları çalıştırırken [`execute_command`](#execute_command) aracını doğru komutlarla kullan ve hangi komutu çalıştıracağını belirt.
// Mevcut Yapıya Dokunma: Kod değişikliği yaparken, bu dosyada tanımlanan mevcut klasör yapısını ve kodlama stilini KESİNLİKLE bozma. İzin almadan büyük yapısal değişiklikler yapma.
// Dosya Oluşturma (DİKKAT): Yeni bir dosya oluştururken ([`writeFile`](#write_to_file) kullanarak), dosyanın çalışması için gerekli tüm 'import' ifadelerini (kütüphaneler, tipler, bileşenler vb.) dosyanın en başına eksiksiz olarak ekle. Gerekli kütüphanelerin eklendiğinden emin ol.
// Onay (ZORUNLU): Herhangi bir dosyayı değiştirmeden/oluşturmadan önce, [`execute_command`](#execute_command) ile önemli bir terminal komutunu çalıştırmadan önce, kritik bir teknik karar almadan önce MUTLAKA Kullanıcıdan onay al.

# 13. Atıf
Bu `rules.md` dosyasının bir kısmı Julian Harris https://x.com/julianharris/status/1928376065937789424 tarafından oluşturulan `cursorrules` temel alınarak ve AlmancaABC projesine uyarlanarak geliştirilmiştir.

---
Çok önemli!!! Bu işlemleri yaparken dosya ararken ve dosya oluştururken/düzenlerken fonksiyon çağrılarını düzgün yaptığından emin ol!