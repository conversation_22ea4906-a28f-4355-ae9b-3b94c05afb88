// src/components/dashboard/UpcomingLessonsCard.tsx
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { getUpcomingLessonsForTeacher } from "@/lib/actions/teacher.actions"; // Action import edildi
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { CalendarClock } from "lucide-react";

// Action'dan dönen tipe uygun bir tip (ilişkili studentName ile)
// Prisma.Booking & { studentName: string } gibi birleştirilebilir veya elle tanımlanabilir
type UpcomingLesson = {
    id: string;
    studentId: string;
    teacherId: string;
    lessonTime: Date;
    durationMinutes: number;
    status: string;
    studentName: string; // Action'da eklenen geçici isim
}

export async function UpcomingLessonsCard() {
  // Yetkilendirme kontrolü action içinde yapılıyor varsayımı
  const upcomingLessons: UpcomingLesson[] = await getUpcomingLessonsForTeacher(5); // Sonraki 5 ders

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
            <CalendarClock className="h-5 w-5" />
            Yaklaşan Dersler
        </CardTitle>
        <CardDescription>
          Sonraki {upcomingLessons.length} dersiniz.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {upcomingLessons.length === 0 ? (
          <p className="text-sm text-muted-foreground">Yaklaşan dersiniz bulunmuyor.</p>
        ) : (
          upcomingLessons.map((lesson) => (
            <div key={lesson.id} className="flex items-center gap-4 border-b pb-3 last:border-b-0 last:pb-0">
              <Avatar className="hidden h-9 w-9 sm:flex">
                 {/* TODO: Öğrenci avatarı eklenecek (Clerk'ten veya User modelinden) */}
                <AvatarImage src={`https://avatar.vercel.sh/${lesson.studentId}.png`} alt="Öğrenci Avatar" />
                <AvatarFallback>{lesson.studentName?.[0]}</AvatarFallback>
              </Avatar>
              <div className="grid gap-1 flex-1">
                <p className="text-sm font-medium leading-none">
                  {lesson.studentName}
                </p>
                <p className="text-xs text-muted-foreground">
                    {format(new Date(lesson.lessonTime), "dd MMMM yyyy HH:mm", { locale: tr })} ({lesson.durationMinutes} dk)
                </p>
              </div>
               {/* TODO: Derse katılma linki/butonu Zoom entegrasyonu sonrası eklenecek */}
               <Button variant="outline" size="sm" asChild>
                 <Link href={`/ders/${lesson.id}`}>Derse Katıl</Link>
               </Button>
            </div>
          ))
        )}
         {upcomingLessons.length > 0 && (
             <Button asChild size="sm" className="mt-4 w-full" variant="outline">
                <Link href="/teacher/schedule">Tüm Takvimi Gör</Link>
             </Button>
         )}
      </CardContent>
    </Card>
  );
}