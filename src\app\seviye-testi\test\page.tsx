"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { questions, calculateLevel } from "@/lib/data/level-test-questions"
import { Progress } from "@/components/ui/progress"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { levelTestEmailSchema, LevelTestEmailFormValues } from "@/lib/schemas/level-test-email.schema"
import { Input } from "@/components/ui/input"

export default function LevelTestPage() {
  const router = useRouter()
  const [currentQuestion, setCurrentQuestion] = React.useState(0)
  const [answers, setAnswers] = React.useState<number[]>([])
  const [isFinished, setIsFinished] = React.useState(false)
  const [userLevel, setUserLevel] = React.useState("")
  const [userEmail, setUserEmail] = React.useState("")
  const [showEmailForm, setShowEmailForm] = React.useState(false)
  const form = useForm<LevelTestEmailFormValues>({
    resolver: zodResolver(levelTestEmailSchema),
    defaultValues: { email: "" },
  })

  React.useEffect(() => {
    // Ana sayfadan gelen kullanıcı bilgilerini kontrol et
    const email = localStorage.getItem("userEmail")
    const name = localStorage.getItem("userName")
    if (email) {
      setUserEmail(email)
    } else {
      // E-posta yoksa formu göster
      setShowEmailForm(true)
    }
  }, [])

  const handleAnswer = (answerIndex: number) => {
    const newAnswers = [...answers, answerIndex]
    setAnswers(newAnswers)

    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(prev => prev + 1)
    } else {
      // Test bitti, seviyeyi hesapla
      const correctAnswers = newAnswers.filter(
        (answer, index) => answer === questions[index].correctAnswer
      ).length
      const level = calculateLevel(correctAnswers)
      setUserLevel(level)
      setIsFinished(true)
    }
  }

  const handleFinish = () => {
    router.push(`/kurs/${userLevel.toLowerCase()}`)
  }

  const handleEmailSubmit = async (values: LevelTestEmailFormValues) => {
    localStorage.setItem("userEmail", values.email)
    setUserEmail(values.email)
    setShowEmailForm(false)
  }

  if (showEmailForm) {
    return (
      <div className="container mx-auto px-4 py-12 max-w-md">
        <Card className="bg-white/90 backdrop-blur shadow-xl">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-6">Teste Başlamak İçin E-posta Adresinizi Girin</h2>
            <form onSubmit={form.handleSubmit(handleEmailSubmit)} className="space-y-4">
              <div>
                <label>E-posta</label>
                <Input {...form.register("email")} type="email" placeholder="<EMAIL>" />
                {form.formState.errors.email && <p>{form.formState.errors.email.message}</p>}
              </div>
              <Button type="submit" className="w-full">Teste Başla</Button>
            </form>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isFinished) {
    return (
      <div className="container mx-auto px-4 py-12 max-w-2xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="bg-white/90 backdrop-blur shadow-xl">
            <CardContent className="p-6 text-center">
              <h1 className="text-3xl font-bold mb-6">Test Tamamlandı!</h1>
              <div className="mb-8">
                <p className="text-lg mb-2">
                  Almanca seviyeniz: <span className="font-bold text-primary">{userLevel}</span>
                </p>
                <p className="text-gray-600">
                  {answers.filter((answer, index) => answer === questions[index].correctAnswer).length} soruyu doğru cevapladınız.
                </p>
              </div>
              <div className="space-y-4">
                <p className="text-gray-700">
                  Size özel hazırlanmış {userLevel} seviye kursumuz ile Almanca öğrenmeye hemen başlayabilirsiniz.
                </p>
                <Button onClick={handleFinish} className="w-full bg-primary hover:bg-primary/90">
                  {userLevel} Seviye Kursunu İncele
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-12 max-w-2xl">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="bg-white/90 backdrop-blur shadow-xl">
          <CardContent className="p-6">
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">Soru {currentQuestion + 1}/12</span>
                <span className="text-sm text-gray-600">İlerleme: {Math.round(((currentQuestion) / questions.length) * 100)}%</span>
              </div>
              <Progress value={((currentQuestion) / questions.length) * 100} className="h-2" />
            </div>
            
            <h2 className="text-xl font-semibold mb-6">{questions[currentQuestion].question}</h2>
            
            <div className="space-y-3">
              {questions[currentQuestion].options.map((option, index) => (
                <Button
                  key={index}
                  onClick={() => handleAnswer(index)}
                  variant="outline"
                  className="w-full text-left justify-start h-auto py-3 px-4"
                >
                  {option}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
