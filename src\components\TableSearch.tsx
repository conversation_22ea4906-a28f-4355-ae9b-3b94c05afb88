"use client";

import { Search } from "lucide-react"; // Image yerine lucide-react ikonu import edildi
import { useRouter } from "next/navigation";

const TableSearch = () => {
  const router = useRouter();

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const value = (e.currentTarget[0] as HTMLInputElement).value;

    const params = new URLSearchParams(window.location.search);
    // Arama terimi boşsa 'search' parametresini kaldır, değilse ayarla
    if (value) {
      params.set("search", value);
    } else {
      params.delete("search");
    }
    // Arama yapıldığında genellikle ilk sayfaya dönmek mantıklıdır
    params.delete("page"); 
    router.push(`${window.location.pathname}?${params}`);
  };

  return (
    <form
      onSubmit={handleSubmit}
      // Stil güncellendi: Daha modern bir görünüm için bg-background, border vb.
      className="w-full md:w-auto flex items-center gap-2 text-sm rounded-md border border-input bg-background px-3 py-1 ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
    >
      <Search className="h-4 w-4 text-muted-foreground" /> {/* Lucide ikonu kullanıldı */}
      <input
        type="text"
        placeholder="Ara..." // Placeholder Türkçeleştirildi
        // Stil güncellendi: Daha modern bir görünüm
        className="w-full p-0 bg-transparent outline-none placeholder:text-muted-foreground disabled:Roo-not-allowed disabled:opacity-50"
        // Varsayılan arama değerini URL'den almak için defaultValue eklenebilir (opsiyonel)
        // defaultValue={new URLSearchParams(window.location.search).get("search") || ""} 
      />
    </form>
  );
};

export default TableSearch;