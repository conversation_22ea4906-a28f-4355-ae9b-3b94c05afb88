import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ChevronLeft, Users } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const metadata: Metadata = {
  title: 'Üyelik Sözleşmesi | AlmancaABC',
  description: 'AlmancaABC platformuna üye olurken kabul ettiğiniz sözleşme detayları.',
};

const UyelikSozlesmesiPage = () => {
  return (
    (<div className="container mx-auto py-8 px-4 md:px-6">
      <div className="mb-8">
        <Button variant="outline" asChild className="text-sm">
          <Link href="/yardim">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Yardım Merkezine Dön
          </Link>
        </Button>
      </div>
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center space-x-3 mb-3">
            <Users className="h-8 w-8 text-purple-600" />
            <CardTitle className="text-3xl font-bold">Üyelik Sözleşmesi</CardTitle>
          </div>
          <p className="text-muted-foreground">Son Güncelleme: 18 Mayıs 2025</p>
        </CardHeader>
        <CardContent className="prose prose-sm sm:prose lg:prose-lg xl:prose-xl dark:prose-invert max-w-none">
          <h2>1. Sözleşmenin Konusu</h2>
          <p>            İşbu Üyelik Sözleşmesi (&ldquo;Sözleşme&rdquo;), AlmancaABC platformuna (&ldquo;Platform&rdquo;) üye olan kullanıcıların
            (&ldquo;Üye&rdquo;) Platform&apos;u kullanım koşullarını ve tarafların hak ve yükümlülüklerini belirler.
          </p>
          <h2>2. Üyelik Şartları</h2>
          <p>            Platform&apos;a üye olmak için kullanıcıların en az 18 yaşında olması veya yasal vasilerinin onayı
            gerekmektedir. Üyeler, kayıt sırasında verdikleri bilgilerin doğru ve güncel olduğunu kabul ederler.
          </p>
          <h2>3. Üyenin Hak ve Yükümlülükleri</h2>
          <p>
            Üye,            Platform&apos;u yasalara ve işbu Sözleşme&apos;ye uygun olarak kullanmayı kabul eder.
            Hesap bilgilerinin gizliliğinden Üye sorumludur. Platform üzerinde diğer üyelere
            karşı saygılı ve etik kurallara uygun davranmalıdır.
          </p>
          <h2>4. Platformun Hak ve Yükümlülükleri</h2>
          <p>            AlmancaABC, Platform&apos;un kesintisiz ve güvenli bir şekilde çalışması için gerekli özeni gösterir.
            Ancak teknik aksaklıklardan dolayı yaşanabilecek sorunlardan sorumlu tutulamaz.
            AlmancaABC, Sözleşme&apos;yi ihlal eden üyelerin üyeliklerini askıya alma veya sonlandırma
            hakkını saklı tutar.
          </p>
          <h2>5. Gizlilik</h2>
          <p>            Üyelerin kişisel verileri, Platform&apos;un <Link href="/yardim/gizlilik-politikasi" className="text-blue-600 hover:underline">Gizlilik Politikası</Link>&apos;na
            uygun olarak işlenir ve korunur.
          </p>
          <h2>6. Sözleşmenin Feshi</h2>
          <p>            Üyeler, diledikleri zaman üyeliklerini sonlandırabilirler. AlmancaABC de işbu Sözleşme&apos;de
            belirtilen koşullar altında üyeliği sonlandırabilir.
          </p>
          <h2>7. Değişiklikler</h2>          <p>
            AlmancaABC, bu Üyelik Sözleşmesi&apos;ni dilediği zaman güncelleme hakkını saklı tutar.
            Güncellemeler Platform üzerinden duyurulur.
          </p>
          <p>
            {/* Buraya daha fazla detay ve yasal metin eklenecektir. */}
            Bu belge genel bir taslaktır ve yasal geçerliliği için bir hukuk danışmanına
            başvurulması önerilir.
          </p>
        </CardContent>
      </Card>
    </div>)
  );
};

export default UyelikSozlesmesiPage;
