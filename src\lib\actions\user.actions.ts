"use server";

import { createSupabaseServerClient } from '@/utils/supabase/server';
import { Role } from '@/types/user.types';
// import { prisma } from '@/lib/prisma'; // Yo<PERSON>lu kalsın veya silinsin

export async function getCurrentUserRole(): Promise<Role | null> {
  const supabase = createSupabaseServerClient();
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error || !user) {
    // console.error('Error getting current user for role:', error?.message);
    return null;
  }
  // Kullanıcının user_metadata'sında 'role' alanı yoksa varsayılan olarak USER ata.
  return user.user_metadata?.role as Role || Role.USER;
}

export async function isUserAdmin(): Promise<boolean> {
  // Bu fonksiyon sadece mevcut giriş yapmış kullanıcının admin olup olmadığını kontrol eder.
  // Başka bir kullanıcının adminliğini kontrol etmek için admin yetkileriyle (service_role)
  // Supabase client kullanılarak o kullanıcının rolü çekilmelidir.
  const role = await getCurrentUserRole();
  return role === Role.ADMIN;
}

// BU FONKSİYON ADMIN YETKİSİ (SERVICE_ROLE KEY) GEREKTİRİR VE GÜVENLİ KULLANILMALIDIR.
// Sadece admin paneli gibi güvenli yerlerden çağrılmalıdır.
export async function getUserRoleByIdForAdmin(userId: string): Promise<Role | null> {
  'use server';
  // ÖNEMLİ: Bu fonksiyonun güvenli çalışması için service_role key ile yeni bir Supabase client oluşturulmalı.
  // import { createClient } from '@supabase/supabase-js';
  // const supabaseAdmin = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
  // const { data, error } = await supabaseAdmin.auth.admin.getUserById(userId);
  // if (error) {
  //   console.error('Error getting user by ID for admin:', error.message);
  //   return null;
  // }
  // return data.user?.user_metadata?.role as Role || null;
  console.warn(`getUserRoleByIdForAdmin çağrıldı (userId: ${userId}) ancak Supabase Admin Client implementasyonu eksik. Null dönecek.`);
  // Geçici ve güvensiz fallback (sadece mevcut kullanıcı için çalışır, konsept için):
  // Bu fallback sadece mevcut kullanıcı için çalışır ve admin client'ın yerini tutmaz.
  // Gerçek implementasyonda yukarıdaki supabaseAdmin bloğu kullanılmalıdır.
  const supabase = createSupabaseServerClient();
  const { data: { user: currentUser } } = await supabase.auth.getUser();
  if (currentUser?.id === userId) {
    return currentUser.user_metadata?.role as Role || Role.USER;
  }
  return null; // Başka bir kullanıcı için rol bilgisi alınamadı.
}

// BU FONKSİYON ADMIN YETKİSİ (SERVICE_ROLE KEY) GEREKTİRİR VE GÜVENLİ KULLANILMALIDIR.
export async function setUserRoleByAdmin(userIdToUpdate: string, newRole: Role) {
  'use server';
  // ÖNEMLİ: Bu fonksiyonun güvenli çalışması için service_role key ile yeni bir Supabase client oluşturulmalı.
  // import { createClient } from '@supabase/supabase-js';
  // const supabaseAdmin = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
  // const { data, error } = await supabaseAdmin.auth.admin.updateUserById(
  //   userIdToUpdate,
  //   { user_metadata: { role: newRole /* diğer metadata korunmalı */ } }
  // );
  // if (error) {
  //   console.error('Error setting user role by admin:', error.message);
  //   throw new Error(`Failed to set user role for ${userIdToUpdate}: ${error.message}`);
  // }
  // console.log(`User ${userIdToUpdate} role updated to ${newRole} by admin.`);
  // return data.user; // data.user yerine data.user demeliyiz, Supabase SDK'ya göre değişebilir
  console.warn(`setUserRoleByAdmin çağrıldı (userId: ${userIdToUpdate}, role: ${newRole}) ancak Supabase Admin Client implementasyonu eksik. İşlem yapılmayacak.`);
  throw new Error('Admin client konfigürasyonu eksik, rol atama yapılamadı.');
}

export async function ensureUserProfileExists(userId: string, role: Role, details: { email?: string | null, fullName?: string | null }) {
  'use server';
  // import { PrismaClient } from '@prisma/client'; // Prisma client'ı import et veya oluştur
  // const prisma = new PrismaClient();
  // try {
  //   if (role === Role.TEACHER) {
  //     await prisma.teacher.upsert({
  //       where: { id: userId },
  //       update: { email: details.email, name: details.fullName }, // Gerekirse diğer alanlar
  //       create: {
  //         id: userId,
  //         email: details.email!, // Teacher için email zorunlu varsayalım
  //         name: details.fullName,
  //         // Diğer zorunlu Teacher alanları
  //       },
  //     });
  //   } else if (role === Role.STUDENT) {
  //     await prisma.student.upsert({
  //       where: { id: userId },
  //       update: { email: details.email, name: details.fullName },
  //       create: {
  //         id: userId,
  //         email: details.email!, // Student için email zorunlu varsayalım
  //         name: details.fullName,
  //          // Diğer zorunlu Student alanları
  //       },
  //     });
  //   } // Parent için de benzer bir yapı eklenebilir
  //   // console.log(`${role} profile ensured for user ${userId}`);
  // } catch (error) {
  //   console.error(`Error ensuring ${role} profile for user ${userId}:`, error);
  // } finally {
  //   await prisma.$disconnect();
  // }
  console.log(`ensureUserProfileExists çağrıldı: userId=${userId}, role=${role}, details=${JSON.stringify(details)}. Prisma entegrasyonu yorumlu/eksik.`);
}
