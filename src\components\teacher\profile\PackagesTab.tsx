"use client";

import React, { useState } from 'react';
import { Target, Users, Video, CheckCircle, CreditCard, Sparkles, Calendar, Clock, Star } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { CoursePackage, TeacherProfileData } from '../TeacherProfileClient';

interface PackagesTabProps {
  teacher: TeacherProfileData;
  onOpenCalendarModal: (pkg: CoursePackage) => void;
}

export const PackagesTab: React.FC<PackagesTabProps> = ({ teacher, onOpenCalendarModal }) => {
  const [activePackageType, setActivePackageType] = useState('oneOnOne');

  const packageTypes = [
    {
      id: 'oneOnOne',
      name: 'B<PERSON>bir Online Dersler',
      shortDesc: '<PERSON><PERSON><PERSON><PERSON> özel',
      description: 'Tamamen sizin ihtiyaçlarınıza ve öğrenme hızınıza göre tasarlanmış birebir dersler.',
      packages: teacher.oneOnOnePackages,
      icon: Target,
      gradient: "from-blue-500 to-indigo-500",
      bgGradient: "from-blue-50 to-indigo-50",
      features: [
        "Kişiselleştirilmiş ders planı",
        "Esnek ders programı",
        "Haftalık ödevler ve geri bildirim",
        "Konuşma pratiği odaklı dersler",
        "Sınırsız mesajlaşma desteği",
        "Özel ders materyalleri"
      ]
    },
    {
      id: 'group',
      name: 'Grup Dersleri',
      shortDesc: '2-4 kişilik gruplar',
      description: '2-4 kişilik küçük gruplarla etkileşimli dersler. Hem ekonomik hem de sosyal öğrenme.',
      packages: teacher.groupCourses,
      icon: Users,
      gradient: "from-emerald-500 to-green-500",
      bgGradient: "from-emerald-50 to-green-50",
      features: [
        "Seviyenize uygun grup eşleştirmesi",
        "Etkileşimli grup aktiviteleri",
        "Konuşma pratiği imkanı",
        "Haftalık ödevler",
        "Gruba özel WhatsApp grubu",
        "Dönem sonu sertifikası"
      ]
    },
    {
      id: 'video',
      name: 'Video Kursları',
      shortDesc: 'Kayıtlı dersler',
      description: 'Kendi hızınızda ilerleyebileceğiniz kapsamlı video dersler.',
      packages: teacher.videoCourses,
      icon: Video,
      gradient: "from-purple-500 to-pink-500",
      bgGradient: "from-purple-50 to-pink-50",
      features: [
        "Süresiz erişim hakkı",
        "İndirebilir çalışma materyalleri",
        "İlerleme testi ve alıştırmalar",
        "Mobil uyumlu platform",
        "Sertifika alma imkanı",
        "3 aylık e-posta desteği"
      ]
    }
  ];

  const activePackageData = packageTypes.find(type => type.id === activePackageType);

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-6 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-3xl p-8 md:p-12 border border-blue-200">
        <div className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
          <Sparkles className="w-4 h-4" />
          Ders Paketleri
        </div>
        <h2 className="text-4xl md:text-5xl font-black text-gray-900 leading-tight">
          <span className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
            Size Özel Ders Paketleri
          </span>
        </h2>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          İhtiyaçlarınıza en uygun ders paketini seçin ve Almanca öğrenme yolculuğunuza başlayın!
        </p>
      </div>

      {/* Package Type Tabs */}
      <Tabs value={activePackageType} onValueChange={setActivePackageType} className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-gray-100 p-1 rounded-xl">
          {packageTypes.map((type) => {
            const Icon = type.icon;
            return (
              <TabsTrigger 
                key={type.id} 
                value={type.id}
                className="flex items-center gap-2 py-3 px-4 text-sm font-semibold data-[state=active]:bg-white data-[state=active]:shadow-sm rounded-lg transition-all duration-300"
              >
                <Icon className="w-4 h-4" />
                <span className="hidden sm:inline">{type.name}</span>
                <span className="sm:hidden">{type.shortDesc}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {packageTypes.map((packageType) => (
          <TabsContent key={packageType.id} value={packageType.id} className="mt-8">
            {packageType.packages && packageType.packages.length > 0 ? (
              <div className="space-y-8">
                {/* Package Type Info */}
                <Card className={`bg-gradient-to-br ${packageType.bgGradient} border border-gray-200 shadow-lg`}>
                  <CardHeader>
                    <div className="flex items-center gap-4">
                      <div className={`p-3 bg-gradient-to-br ${packageType.gradient} rounded-2xl shadow-lg`}>
                        <packageType.icon className="w-7 h-7 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-2xl font-bold text-gray-900">
                          {packageType.name}
                        </CardTitle>
                        <p className="text-gray-600 font-medium text-lg">{packageType.description}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {packageType.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2 text-gray-700">
                          <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Packages Grid */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {packageType.packages.map((pkg, index) => (
                    <Card 
                      key={pkg.id} 
                      className="group relative bg-white border border-gray-200 overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02]"
                    >
                      {/* Popular Badge */}
                      {index === 0 && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                          <div className="bg-gradient-to-r from-amber-500 to-orange-500 text-white px-4 py-1 rounded-full text-sm font-bold shadow-lg">
                            🔥 En Popüler
                          </div>
                        </div>
                      )}

                      <CardContent className="p-6 space-y-4">
                        {/* Package Header */}
                        <div className="text-center space-y-2">
                          <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                            {pkg.name}
                          </h3>
                          {pkg.level && (
                            <Badge className={`bg-gradient-to-r ${packageType.gradient} text-white`}>
                              {pkg.level}
                            </Badge>
                          )}
                        </div>

                        {/* Package Details */}
                        <div className="space-y-3 text-sm text-gray-600">
                          {pkg.sessions && (
                            <div className="flex items-center gap-2">
                              <Calendar className="w-4 h-4 text-blue-500" />
                              <span>{pkg.sessions} ders</span>
                            </div>
                          )}
                          {pkg.durationMinutes && (
                            <div className="flex items-center gap-2">
                              <Clock className="w-4 h-4 text-green-500" />
                              <span>{pkg.durationMinutes} dakika/ders</span>
                            </div>
                          )}
                          {pkg.access && (
                            <div className="flex items-center gap-2">
                              <CheckCircle className="w-4 h-4 text-purple-500" />
                              <span>{pkg.access}</span>
                            </div>
                          )}
                        </div>

                        {/* Features */}
                        {pkg.features && (
                          <div className="space-y-2">
                            {pkg.features.slice(0, 3).map((feature, fIdx) => (
                              <div key={fIdx} className="flex items-center text-sm text-gray-600">
                                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                                <span>{feature}</span>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Price and Action */}
                        <div className="border-t border-gray-100 pt-4 space-y-4">
                          <div className="text-center">
                            <div className="text-3xl font-black text-gray-900">{pkg.price} ₺</div>
                            {pkg.perLesson && (
                              <div className="text-sm text-gray-500">{pkg.perLesson} ₺/ders</div>
                            )}
                          </div>
                          
                          <Button
                            className={`w-full py-3 text-base font-semibold bg-gradient-to-r ${packageType.gradient} hover:opacity-90 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]`}
                            onClick={() => onOpenCalendarModal(pkg)}
                          >
                            <CreditCard className="w-5 h-5 mr-2" />
                            Hemen Başla
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-400 text-lg">Bu kategoride henüz paket bulunmuyor.</div>
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};