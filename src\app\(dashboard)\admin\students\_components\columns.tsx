// src/app/(dashboard)/admin/students/_components/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";

// StudentData tipini page.tsx'teki ile aynı veya Prisma'dan import edilecek şekilde tanımla
type StudentData = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  proficiencyLevel: string | null;
  created_at: Date;
};

export const columns: ColumnDef<StudentData>[] = [
  {
    accessorKey: "firstName", // Ad ve Soyadı birleştirelim
    header: ({ column }) => {
       return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Ad Soyad
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
        const firstName = row.getValue("firstName") as string | null;
        const lastName = row.original.lastName;
        return <div className="font-medium">{`${firstName ?? ''} ${lastName ?? ''}`}</div>;
    }
  },
  {
    accessorKey: "email",
     header: ({ column }) => {
       return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          E-posta
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
  },
  {
    accessorKey: "proficiencyLevel",
    header: "Seviye",
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => {
       return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Kayıt Tarihi
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const date = new Date(row.getValue("created_at"));
      const formatted = date.toLocaleDateString("tr-TR");
      return <div className="text-right font-medium">{formatted}</div>;
    },
  },
   {
    id: "actions",
    cell: ({ row }) => {
      const student = row.original;

      return (
        (<DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Menüyü aç</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Eylemler</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(student.id)}
            >
              Öğrenci ID Kopyala
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href={`/admin/students/${student.id}`}>Profili Görüntüle</Link>
            </DropdownMenuItem>
             {/* TODO: Öğrenciyi düzenleme/silme eylemleri eklenecek */}
             <DropdownMenuItem>Düzenle (Eylem Eklenecek)</DropdownMenuItem>
             <DropdownMenuItem className="text-destructive">
                Sil (Eylem Eklenecek)
             </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>)
      );
    },
     enableSorting: false,
     enableHiding: false,
  },
];