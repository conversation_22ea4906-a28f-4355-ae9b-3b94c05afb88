# AlmancaABC Platform System Design

**Son Güncelleme:** 31 Mart 2025

*<PERSON><PERSON>, AlmancaABC platformunun teknik mimarisini ve tasarım kararlarını özetlemektedir. Kapsamlı proje planı, strateji ve yol haritası için l<PERSON>fen [AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md](./AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md) dosyasına başvurun.*

## 1. Implementation Approach

The AlmancaABC platform will be developed as a web application connecting German language teachers with students. The primary focus is on delivering a robust MVP quickly, leveraging the existing blog traffic, while ensuring scalability for future features like offline courses, dictionary, corporate packages, and translation services.

### 1.1. Key Technology Decisions (Based on Project Plan v. 31.03.2025)

1.  **Frontend**: Next.js 15+ (App Router), React 19, TypeScript - *Chosen for performance, SEO, DX, and Vercel integration.*
2.  **Styling**: Tailwind CSS + Shadcn/ui - *Chosen for rapid UI development and consistency.*
3.  **Backend & Database**: Supabase (PostgreSQL) - *Chosen for its BaaS features (Auth, DB, Storage, Realtime) and generous free tier.*
4.  **Authentication**: Clerk - *Chosen due to existing integration and ease of use for role management via custom attributes.*
5.  **ORM**: Prisma - *Chosen for type safety, developer experience (DX), and robust migration system.*
6.  **Video Conferencing (MVP)**: **Zoom SDK** - *Chosen based on user preference. Alternatives like VideoSDK/Dyte considered for cost-efficiency.*
7.  **Payment Processing (MVP)**: **Stripe (with Stripe Connect)** - *Chosen for MVP to handle lesson payments and commission splitting. Paypal/iyzico to be added post-MVP.*
8.  **Calendar Library**: **FullCalendar** - *Chosen for rich features and potential for Google Calendar sync.*
9.  **State Management**: **None initially** - *React's built-in capabilities (useState, Context) and server state management (e.g., Next.js cache, potentially React Query/SWR later if needed) will be used.*
10. **Form Handling**: React Hook Form + Zod - *Standard choice for robust form handling and validation.*
11. **API Layer**: Next.js API Routes (Server Actions where applicable) + Prisma Client.
12. **Internationalization (i18n)**: **next-intl** or similar library - *To be implemented for Turkish (primary) and German language support.*
13. **Hosting**: Vercel - *Chosen for seamless Next.js deployment.*
14. **Error Tracking**: Sentry - *To be integrated post-MVP.*
15. **Video Hosting (Offline Courses - Post-MVP)**: **YouTube Links** (initially), Mux/Cloudinary (later).

### 1.2. Development Approach

*   **Component-Based Architecture**: Reusable UI components (Shadcn/ui).
*   **Responsive Design**: Mobile-first.
*   **Internationalization (i18n)**: Structure for TR/DE languages.
*   **Security**: GDPR compliance, secure coding practices, dependency scanning.
*   **Performance**: Optimized asset loading, code splitting, caching (Vercel Edge).
*   **Monitoring**: Vercel Analytics (initially), Sentry (post-MVP).
*   **Version Control**: Git (GitHub).
*   **Package Manager**: bun.

### 1.3. Challenging Areas and Solutions

1.  **Real-time Availability**: Using Supabase Realtime or efficient polling/revalidation for teacher availability updates.
2.  **Video Session Reliability**: Implementing robust error handling and potentially fallback mechanisms for Zoom SDK.
3.  **User Role Management**: Implementing Clerk Custom Attributes for role assignment during sign-up and using middleware for route protection.
4.  **Scalable i18n**: Setting up `next-intl` (or similar) correctly for easy translation management.
5.  **Payment & Commission Logic**: Implementing Stripe Connect accurately for automated commission splitting and teacher payouts. Ensuring compliance with German tax regulations (requires consultation).
6.  **Prisma Migration Issues**: Currently facing issues with `prisma migrate dev` against Supabase (*Resolution pending*). May need to explore `db push` for development or manual SQL application as temporary workarounds.

## 2. Data Structures and Interfaces

The detailed data structures and relationships are defined in the following Mermaid class diagrams:

*   **MVP Focus:** [AlmancaABC_class_diagram_MVP.mermaid](./AlmancaABC_class_diagram_MVP.mermaid) - Outlines the core models required for the Minimum Viable Product.
*   **Full Scope:** [AlmancaABC_class_diagram.mermaid](./AlmancaABC_class_diagram.mermaid) - Represents the complete data structure including post-MVP features and ecosystem components.

*Database schema will be managed using Prisma migrations.*

## 3. Program Call Flow / Sequence Diagrams

Key user interaction flows and system component interactions are illustrated in the following Mermaid sequence diagrams:

*   **MVP Focus:** [AlmancaABC_sequence_diagram_MVP.mermaid](./AlmancaABC_sequence_diagram_MVP.mermaid) - Shows the core sequences for MVP features like registration, booking, joining lessons, and reviews.
*   **Full Scope:** [AlmancaABC_sequence_diagram.mermaid](./AlmancaABC_sequence_diagram.mermaid) - Includes sequences for post-MVP features like offline course enrollment, translation requests, dictionary search, etc.

## 4. Anything UNCLEAR / Open Questions (Post-Planning)

*   **Prisma Migration Resolution**: The primary technical blocker currently is resolving the `prisma migrate dev` issues with Supabase.
*   **Clerk Custom Attribute Setup**: User needs to configure the `role` attribute in the Clerk dashboard.
*   **Specific i18n Library**: Final decision on `next-intl` vs. alternatives and implementation details for easy translation management.
*   **Video SDK Choice (Final)**: Confirming Zoom SDK as the definite choice for MVP despite potential cost implications vs. VideoSDK/Dyte.
*   **Detailed Legal Texts**: AGB, Impressum, Datenschutzerklärung require legal consultation/preparation.
*   **Admin Panel Requirements**: Specific features and UI for the admin panel (teacher approval, platform settings) need definition.
*   **Notification System Details**: Specific triggers and channels (email, in-app) for notifications need planning (post-MVP).
*   **Data Migration from Blog**: Strategy for potentially migrating relevant content or users from the existing blog (if desired).