// src/components/forms/TeacherProfileForm.tsx
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useEffect } from "react";
import { useFormState } from "react-dom";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "sonner"; // sonner'dan toast import edildi
import { updateTeacher } from "@/lib/actions/teacher.actions";
import { Teacher, Prisma } from "@prisma/client";

// Form şeması
const teacherProfileSchema = z.object({
  bio: z.string().optional().nullable(),
  specializations: z.string().optional().nullable(),
  hourly_rate: z.coerce // coerce kullanarak tip zorlaması yap
    .number({ invalid_type_error: "Lütfen geçerli bir sayı girin." }) // Sayıya çevirmeye çalış
    .positive("Saatlik ücret pozitif olmalı") // Pozitif olmalı
    .optional() // İsteğe bağlı
    .nullable(), // Null olabilir
  profile_image_url: z.string().url("Geçerli bir URL girin").or(z.literal("")).optional().nullable(),
  intro_video_url: z.string().url("Geçerli bir URL girin").or(z.literal("")).optional().nullable(),
});

type TeacherProfileSchema = z.infer<typeof teacherProfileSchema>;

type FormState = { success: boolean; error: string | null };

export function TeacherProfileForm({ teacherData }: { teacherData: Teacher }) {
  // const { toast } = useToast(); // Kaldırıldı
  const router = useRouter();

  const {
    register,
    formState: { errors, isSubmitting },
    // handleSubmit ve reset kaldırıldı
  } = useForm<TeacherProfileSchema>({
    resolver: zodResolver(teacherProfileSchema),
    defaultValues: {
      bio: teacherData.bio ?? "",
      specializations: teacherData.specializations?.join(", ") ?? "",
      hourly_rate: teacherData.hourly_rate?.toNumber() ?? undefined,
      profile_image_url: teacherData.profile_image_url ?? "",
      intro_video_url: teacherData.intro_video_url ?? "",
    },
  });

  const [state, formAction] = useFormState(
     async (prevState: FormState, formData: FormData): Promise<FormState> => {
        const validatedFields = teacherProfileSchema.safeParse({
            bio: formData.get('bio'),
            specializations: formData.get('specializations'),
            hourly_rate: formData.get('hourly_rate'),
            profile_image_url: formData.get('profile_image_url'),
            intro_video_url: formData.get('intro_video_url'),
        });

        if (!validatedFields.success) {
            // console.error("Form Validation Errors:", validatedFields.error.flatten().fieldErrors); - REMOVED
            return { success: false, error: "Form verileri geçersiz." };
        }

        const specializationsArray = validatedFields.data.specializations
            ?.split(',')
            .map((s: string) => s.trim())
            .filter((s: string) => s !== '') ?? [];

        const hourlyRateDecimal = validatedFields.data.hourly_rate !== null && validatedFields.data.hourly_rate !== undefined
            ? new Prisma.Decimal(validatedFields.data.hourly_rate)
            : null;

        try {
            const updateData: Parameters<typeof updateTeacher>[1] = {
                bio: validatedFields.data.bio,
                specializations: specializationsArray,
                hourly_rate: hourlyRateDecimal,
                profile_image_url: validatedFields.data.profile_image_url,
                intro_video_url: validatedFields.data.intro_video_url,
            };

            const result = await updateTeacher(teacherData.id, updateData);

            if (result) {
                return { success: true, error: null };
            } else {
                const errorMessage = "Profil güncellenirken bir hata oluştu.";
                return { success: false, error: errorMessage };
            }
        } catch (error) {
            // console.error("Update Teacher Error:", error); - REMOVED
            return { success: false, error: "Bir sunucu hatası oluştu." };
        }
     },
     { success: false, error: null }
  );

  useEffect(() => {
    if (state.success) {
      toast.success("Profiliniz başarıyla güncellendi."); // sonner kullanımı
      router.refresh();
    }
    if (state.error) {
      toast.error(state.error); // sonner kullanımı
    }
  }, [state, router]); // toast bağımlılıktan kaldırıldı


  return (
    <form action={formAction} className="space-y-6">
       {/* Biyo Alanı */}
       <div className="space-y-2">
         <Label htmlFor="bio">Hakkımda (Biyo)</Label>
         <Textarea
           id="bio"
           {...register("bio")}
           placeholder="Kendinizi ve öğretmenlik yaklaşımınızı anlatın..."
           className={errors.bio ? "border-destructive" : ""}
         />
         {errors.bio && <p className="text-sm text-destructive">{errors.bio.message}</p>}
       </div>

       {/* Uzmanlık Alanları */}
       <div className="space-y-2">
         <Label htmlFor="specializations">Uzmanlık Alanları (Virgülle ayırın)</Label>
         <Input
           id="specializations"
           {...register("specializations")}
           placeholder="Örn: TestDaF, İş Almancası, Çocuklar için Almanca"
           className={errors.specializations ? "border-destructive" : ""}
         />
         {errors.specializations && <p className="text-sm text-destructive">{errors.specializations.message}</p>}
       </div>

       {/* Saatlik Ücret */}
       <div className="space-y-2">
         <Label htmlFor="hourly_rate">Saatlik Ücret (€)</Label>
         <Input
           id="hourly_rate"
           type="number"
           step="0.5"
           {...register("hourly_rate")}
           placeholder="Örn: 25.50"
           className={errors.hourly_rate ? "border-destructive" : ""}
         />
         {errors.hourly_rate && <p className="text-sm text-destructive">{errors.hourly_rate.message}</p>}
       </div>

        {/* Profil Resmi URL */}
       <div className="space-y-2">
         <Label htmlFor="profile_image_url">Profil Resmi URL</Label>
         <Input
           id="profile_image_url"
           {...register("profile_image_url")}
           placeholder="https://..."
           className={errors.profile_image_url ? "border-destructive" : ""}
         />
         {/* TODO: Supabase Storage ile resim yükleme bileşeni eklenecek */}
         {errors.profile_image_url && <p className="text-sm text-destructive">{errors.profile_image_url.message}</p>}
       </div>

        {/* Tanıtım Videosu URL */}
       <div className="space-y-2">
         <Label htmlFor="intro_video_url">Tanıtım Videosu URL (YouTube)</Label>
         <Input
           id="intro_video_url"
           {...register("intro_video_url")}
           placeholder="https://youtube.com/..."
           className={errors.intro_video_url ? "border-destructive" : ""}
         />
         {errors.intro_video_url && <p className="text-sm text-destructive">{errors.intro_video_url.message}</p>}
       </div>

      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? "Kaydediliyor..." : "Profili Güncelle"}
      </Button>
    </form>
  );
}