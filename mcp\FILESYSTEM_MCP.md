# Filesystem MCP Sunucu <PERSON> (AlmancaABC Projesi)

## <PERSON><PERSON>u Bilgileri

- **Tan<PERSON>mlayıcı:** `github.com/modelcontextprotocol/servers/tree/main/src/filesystem`
- **<PERSON><PERSON><PERSON><PERSON><PERSON>:** `cmd /c npx -y @modelcontextprotocol/server-filesystem C:/Users/<USER>/almancaabc C:/Users/<USER>/Desktop`
- **Açıklama:** İzin verilen dizinler içindeki yerel dosya sistemiyle etkileşim için araçlar sağlar.

## İzin Verilen Dizinler

Bu sunucu aşağıdaki dizinlerdeki dosyalara ve dizinlere erişebilir:
- `C:/Users/<USER>/almancaabc`
- `C:/Users/<USER>/Desktop`

Erişilebilir yolları doğrulamak için `list_allowed_directories` aracını kullanın.

## Kullanılabilir Araçlar

### `read_file`
- **Açıklama:** <PERSON><PERSON> bir dosyanın tüm içeriğini okur. Çeşitli metin kodlamalarını destekler.
- **<PERSON><PERSON><PERSON>eması:**
  ```json
  {
    "type": "object",
    "properties": {
      "path": { "type": "string" }
    },
    "required": ["path"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "path": "src/components/navbar.tsx"
  }
  ```

### `read_multiple_files`
- **Açıklama:** Aynı anda birden fazla dosyanın içeriğini okur. Birkaç dosyayı analiz etmek veya karşılaştırmak için daha verimlidir.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "paths": {
        "type": "array",
        "items": { "type": "string" }
      }
    },
    "required": ["paths"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "paths": ["src/components/navbar.tsx", "src/components/footer.tsx"]
  }
  ```

### `write_file`
- **Açıklama:** Yeni bir dosya oluşturur veya varolan bir dosyanın içeriğini tamamen üzerine yazar. Dikkatli kullanın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "path": { "type": "string" },
      "content": { "type": "string" }
    },
    "required": ["path", "content"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "path": "new_file.txt",
    "content": "Bu dosyanın içeriği."
  }
  ```

### `edit_file`
- **Açıklama:** Bir metin dosyasında satır tabanlı düzenlemeler yapar. Tam satır dizilerini değiştirir. Git tarzı bir fark döndürür.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "path": { "type": "string" },
      "edits": {
        "type": "array",
        "items": {
          "type": "object",
          "properties": {
            "oldText": { "type": "string", "description": "Aranacak metin - tam olarak eşleşmeli" },
            "newText": { "type": "string", "description": "Yerine konulacak metin" }
          },
          "required": ["oldText", "newText"],
          "additionalProperties": false
        }
      },
      "dryRun": { "type": "boolean", "default": false, "description": "Git tarzı fark formatını kullanarak değişiklikleri önizleyin" }
    },
    "required": ["path", "edits"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "path": "src/components/navbar.tsx",
    "edits": [
      {
        "oldText": "const Navbar = () => {",
        "newText": "const Navbar = () => {"
      }
    ]
  }
  ```

### `create_directory`
- **Açıklama:** Yeni bir dizin oluşturur veya bir dizinin var olduğundan emin olur. İç içe dizinler oluşturabilir. Dizin zaten varsa sessizce başarılı olur.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "path": { "type": "string" }
    },
    "required": ["path"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "path": "src/components/ui"
  }
  ```

### `list_directory`
- **Açıklama:** Bir yoldaki tüm dosya ve dizinlerin ayrıntılı bir listesini alır. Öğeleri `[FILE]` veya `[DIR]` ile önekler.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "path": { "type": "string" }
    },
    "required": ["path"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "path": "src/components"
  }
  ```

### `directory_tree`
- **Açıklama:** Dosyaların ve dizinlerin özyinelemeli bir ağaç görünümünü JSON olarak alır. 'name', 'type' ve 'children' içerir.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "path": { "type": "string" }
    },
    "required": ["path"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "path": "src"
  }
  ```

### `move_file`
- **Açıklama:** Dosyaları ve dizinleri taşır veya yeniden adlandırır. Hedef zaten varsa başarısız olur.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "source": { "type": "string" },
      "destination": { "type": "string" }
    },
    "required": ["source", "destination"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "source": "old_file.txt",
    "destination": "new_file.txt"
  }
  ```

### `search_files`
- **Açıklama:** Bir desene uyan dosya ve dizinleri özyinelemeli olarak arar (büyük/küçük harfe duyarsız, kısmi adlar). Tam yolları döndürür.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "path": { "type": "string" },
      "pattern": { "type": "string" },
      "excludePatterns": {
        "type": "array",
        "items": { "type": "string" },
        "default": []
      }
    },
    "required": ["path", "pattern"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "path": "src",
    "pattern": "navbar"
  }
  ```

### `get_file_info`
- **Açıklama:** Bir dosya veya dizin hakkında ayrıntılı meta verileri (boyut, zaman damgaları, izinler, tür) alır.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "path": { "type": "string" }
    },
    "required": ["path"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "path": "src/components/navbar.tsx"
  }
  ```

### `list_allowed_directories`
- **Açıklama:** Bu sunucunun erişmesine izin verilen dizinlerin listesini döndürür.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {},
    "required": []
  }
  ```
- **Örnek Kullanım:**
  ```json
  {}
