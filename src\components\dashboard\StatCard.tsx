// src/components/dashboard/StatCard.tsx
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";

interface StatCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  // Opsiyonel: De<PERSON>i<PERSON><PERSON> oranı ve trend (artış/azalış)
  change?: string;
  changeType?: "increase" | "decrease";
}

export function StatCard({ title, value, icon: Icon, change, changeType }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change && (
          <p className={`text-xs ${changeType === 'decrease' ? 'text-red-600' : 'text-green-600'}`}>
            {change} vs geçen ay {/* TODO: Zaman dilimini dinamik yap */}
          </p>
        )}
         {!change && <p className="text-xs text-muted-foreground invisible">+/-</p>} {/* Yükseklik hizalaması için */}
      </CardContent>
    </Card>
  );
}