// src/app/(dashboard)/admin/teachers/[id]/page.tsx
import { notFound } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from 'next/image';
import { getTeacherDetailsForAdmin } from '@/lib/actions/teacher.actions'; // Yeni Server Action
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

// TeacherDetailData tipini Server Action'dan dönen veriyle eşleşecek şekilde tanımla
// Prisma'dan gelen Teacher tipi + belki ilişkili veriler
type TeacherDetailData = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string; // Geçici veya Clerk'ten alınacak
  bio: string | null;
  specializations: string[];
  levels: string[]; // Prisma şemasında vardı, ekleyelim
  hourly_rate: number | null; // Prisma Decimal -> number
  profile_image_url: string | null;
  intro_video_url: string | null; // Prisma şemasında vardı
  country: string | null; // Prisma şemasında vardı
  city: string | null; // Prisma şemasında vardı
  languages: string[]; // Prisma şemasında vardı
  is_approved: boolean;
  is_verified: boolean; // Prisma şemasında vardı
  badges: string[]; // Prisma şemasında vardı
  created_at: Date;
  updated_at: Date; // Prisma şemasında vardı
  // TODO: İstatistikler, eğitim, deneyim, sertifikalar, ders paketleri (JSON alanları)
  // TODO: İlişkili veriler (Rezervasyonlar, Yorumlar, Müsaitlik)
};

// Placeholder fetchTeacherDetails fonksiyonu kaldırıldı.

export default async function AdminTeacherDetailPage({ params }: { params: { id: string } }) {
  // Gerçek Server Action'ı çağırarak öğretmen detaylarını al
  // Teacher değişkenine TeacherDetailData tipini atayalım (null kontrolü zaten yapılıyor)
  const teacherId = params.id; // ID'yi bir değişkene ata
  const teacher: TeacherDetailData | null = await getTeacherDetailsForAdmin(teacherId);

  if (!teacher) {
    notFound(); // Öğretmen bulunamazsa 404 sayfası göster
  }

  return (
    <div className="flex flex-col gap-6">
      <h1 className="text-2xl font-semibold">Öğretmen Detayları: {teacher.firstName} {teacher.lastName}</h1>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Sol Sütun: Profil Bilgileri */}
        <div className="md:col-span-1 space-y-6">
           <Card>
             <CardHeader>
               <CardTitle>Profil Resmi</CardTitle>
             </CardHeader>
             <CardContent>
               {teacher.profile_image_url ? (
                 <Image
                   src={teacher.profile_image_url}
                   alt={`${teacher.firstName} ${teacher.lastName}`}
                   width={150}
                   height={150}
                   className="rounded-full mx-auto"
                 />
               ) : (
                 <div className="w-36 h-36 rounded-full bg-muted flex items-center justify-center mx-auto text-muted-foreground">
                   Resim Yok
                 </div>
               )}
             </CardContent>
           </Card>
           <Card>
             <CardHeader>
               <CardTitle>Durum</CardTitle>
             </CardHeader>
             <CardContent className="space-y-2">
                <Badge variant={teacher.is_approved ? "default" : "destructive"}>
                    {teacher.is_approved ? "Onaylı" : "Onay Bekliyor"}
                </Badge>
                 <Badge variant={teacher.is_verified ? "default" : "secondary"}>
                    {teacher.is_verified ? "Doğrulandı" : "Doğrulanmadı"}
                </Badge>
                {/* Diğer rozetler */}
                {teacher.badges.map(badge => <Badge key={badge} variant="outline">{badge}</Badge>)}
             </CardContent>
           </Card>
            <Card>
             <CardHeader>
               <CardTitle>İletişim ve Konum</CardTitle>
             </CardHeader>
             <CardContent className="space-y-1 text-sm">
                <p><strong>E-posta:</strong> {teacher.email}</p>
                <p><strong>Ülke:</strong> {teacher.country ?? '-'}</p>
                <p><strong>Şehir:</strong> {teacher.city ?? '-'}</p>
                <p><strong>Diller:</strong> {teacher.languages.join(', ')}</p>
             </CardContent>
           </Card>
        </div>

        {/* Sağ Sütun: Detaylı Bilgiler */}
        <div className="md:col-span-2 space-y-6">
           <Card>
             <CardHeader>
               <CardTitle>Hakkında</CardTitle>
             </CardHeader>
             <CardContent>
               <p className="text-sm text-muted-foreground">{teacher.bio ?? "Biyografi eklenmemiş."}</p>
             </CardContent>
           </Card>
           <Card>
             <CardHeader>
               <CardTitle>Uzmanlık Alanları ve Seviyeler</CardTitle>
             </CardHeader>
             <CardContent className="space-y-2">
                <div>
                    <h4 className="font-medium mb-1">Uzmanlıklar:</h4>
                    <div className="flex flex-wrap gap-1">
                        {teacher.specializations.map(spec => <Badge key={spec} variant="secondary">{spec}</Badge>)}
                    </div>
                </div>
                 <div>
                    <h4 className="font-medium mb-1">Verdiği Seviyeler:</h4>
                     <div className="flex flex-wrap gap-1">
                        {teacher.levels.map(level => <Badge key={level} variant="outline">{level}</Badge>)}
                    </div>
                </div>
             </CardContent>
           </Card>
            <Card>
             <CardHeader>
               <CardTitle>Ücret ve Kayıt Bilgileri</CardTitle>
             </CardHeader>
             <CardContent className="space-y-1 text-sm">
                <p><strong>Saatlik Ücret:</strong> {teacher.hourly_rate ? `${teacher.hourly_rate} ₺` : '-'}</p>
                <p><strong>Kayıt Tarihi:</strong> {format(teacher.created_at, "dd MMMM yyyy", { locale: tr })}</p>
                <p><strong>Son Güncelleme:</strong> {format(teacher.updated_at, "dd MMMM yyyy HH:mm", { locale: tr })}</p>
             </CardContent>
           </Card>
           {/* TODO: Ders Geçmişi, İstatistikler, Müsaitlik Takvimi vb. bölümler eklenecek */}
            <Card>
             <CardHeader>
               <CardTitle>Ders Geçmişi</CardTitle>
             </CardHeader>
             <CardContent>
                <p className="text-sm text-muted-foreground">Ders geçmişi bileşeni buraya gelecek.</p>
             </CardContent>
           </Card>
        </div>
      </div>
    </div>
  );
}