'use client';
import React, { useRef } from 'react';
import TableOfContents from './TableOfContents'; // TableOfContents bileşenini import et

type ContentPageLayoutProps = {
  children: React.ReactNode;
  className?: string;
  showToc?: boolean; // İçindekiler bölümünü gösterip göstermeyeceğimizi kontrol etmek için prop
};

const ContentPageLayout = ({ children, className = '', showToc = false }: ContentPageLayoutProps) => {
  const contentRef = useRef<HTMLElement>(null);

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
      <div className="lg:flex lg:gap-0">
        {showToc && (
          <aside className="lg:w-1/3 xl:w-1/4 mb-8 lg:mb-0">
            {/*
              TableOfContents bileşeni sticky olabilir ve sayfa kaydırıld<PERSON>k<PERSON>
              görünür kalabilir. Bu, TableOfContents bileşeninin kendi içinde
              CSS ile (örneğin `sticky top-20`) ayarlanabilir.
            */}
            <TableOfContents contentRef={contentRef} />
          </aside>
        )}
        <article
          ref={contentRef}
          className={`
            prose
            prose-sm
            sm:prose
            lg:prose-lg
            xl:prose-xl
            ${showToc ? 'lg:w-1/2 xl:w-2/3 max-w-2xl ml-auto mr-auto' : 'max-w-2xl mx-auto'} {/* TOC varsa genişliği ayarla, yoksa ortala */}
            prose-p:leading-relaxed
            prose-p:text-slate-700
            dark:prose-p:text-slate-300
            prose-headings:font-bold
            prose-headings:text-slate-900
            dark:prose-headings:text-slate-50
            prose-h2:text-4xl
            prose-h2:mb-8
            prose-h2:mt-12
            prose-h3:text-2xl
            prose-h3:font-bold
            prose-h3:mb-6
            prose-h3:mt-8
            prose-h4:text-xl
            prose-h4:font-semibold
            prose-h4:mb-5
            prose-h4:mt-8
            prose-h5:text-lg
            prose-h5:font-semibold
            prose-h5:mb-4
            prose-h5:mt-6
            prose-h6:text-base
            prose-h6:font-semibold
            prose-h6:mb-3
            prose-h6:mt-5
            prose-p:mb-5 {/* Paragraf alt boşluğu artırıldı */}
            prose-strong:text-slate-900
            dark:prose-strong:text-slate-50
            prose-ul:list-disc
            prose-ul:pl-6
            prose-ul:mb-5
            prose-ol:list-decimal
            prose-ol:pl-6
            prose-ol:mb-5
            prose-li:mb-2 {/* Liste elemanı alt boşluğu artırıldı */}
            prose-a:text-sky-700
            dark:prose-a:text-sky-500
            prose-a:font-medium
            prose-a:no-underline
            hover:prose-a:underline
            hover:prose-a:text-sky-500
            dark:hover:prose-a:text-sky-300
            ${className}
          `}
        >
          {/*
            Tailwind Typography (`@tailwindcss/typography`) eklentisi burada kullanılabilir.
            Yukarıdaki `prose-X:modifier` sınıfları, eklentinin varsayılan stillerini ezer.
            Örneğin, `prose-p:leading-relaxed` tüm paragrafların satır yüksekliğini artırır.
            `prose-headings:mb-5` tüm başlıkların (h1-h6) varsayılan alt boşluğunu ayarlar,
            ardından `prose-hX:mb-Y` ile spesifik başlıklar için bu değerler ayrıca ayarlanır.
          */}
          <div className="text-15"> {/* tailwind.config.ts'de tanımlanan özel 15px boyutu */}
            {children}
          </div>
        </article>
      </div>
    </div>
  );
};

export default ContentPageLayout;

// Örnek Başlık Stilleri (Tailwind Typography kullanılmıyorsa):
// Bu stiller doğrudan bu bileşene veya global.css'e eklenebilir.
// Şimdilik, Tailwind Typography'nin temel stilleri sağlayacağını varsayıyorum.
// Gerekirse, bu yorumları kaldırıp stilleri aktif edebiliriz.
/*
.prose h1 { @apply text-4xl font-bold mb-6 mt-8; }
.prose h2 { @apply text-3xl font-semibold mb-5 mt-7; }
.prose h3 { @apply text-2xl font-semibold mb-4 mt-6; }
.prose h4 { @apply text-xl font-semibold mb-3 mt-5; }
.prose h5 { @apply text-lg font-semibold mb-2 mt-4; }
.prose h6 { @apply text-base font-semibold mb-2 mt-3; }
.prose p { @apply mb-4 leading-relaxed; } // text-15 zaten div ile uygulanıyor
.prose ul, .prose ol { @apply mb-4 ml-6; }
.prose li { @apply mb-2; }
.prose a { @apply text-blue-600 hover:underline; }
*/