import { Suspense } from "react";
import { notFound, redirect } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { getTeacherDetailsForAdmin, TeacherDetailsForAdminResult, EducationEntry, CertificateEntry } from "@/lib/actions/teacher.actions"; // TeacherDetailsForAdminResult, EducationEntry, CertificateEntry eklendi
import { formatDate } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Metadata } from "next";
// import { auth } from "@clerk/nextjs/server"; // Clerk kaldırıldı

// Dinamik metadata
export async function generateMetadata({
  params,
}: {
  params: { id: string };
}): Promise<Metadata> {
  const teacher: TeacherDetailsForAdminResult | null = await getTeacherDetailsForAdmin(params.id);
  
  if (!teacher) {
    return {
      title: "Öğretmen Bulunamadı | AlmancaABC Admin",
    };
  }
  
  return {
    title: `${teacher.firstName || ""} ${teacher.lastName || ""} | AlmancaABC Admin`,
    description: `${teacher.firstName || ""} ${teacher.lastName || ""} öğretmen profili detayları`,
  };
}

// Sayfa bileşeni
export default async function TeacherDetailPage({
  params,
}: {
  params: { id: string };
}) {
  // Admin yetkisi kontrolü
  let userId: string | null = null;

  if (process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
    userId = "test-admin-id";
  }
  
  if (!userId) {
    redirect("/");
  }
  
  const teacher: TeacherDetailsForAdminResult | null = await getTeacherDetailsForAdmin(params.id);
  
  if (!teacher) {
    notFound();
  }
  
  // Öğretmen verileri için yardımcı değişkenler
  const fullName = `${teacher.firstName || ""} ${teacher.lastName || ""}`.trim() || "İsimsiz Öğretmen";
  const hourlyRate = teacher.hourly_rate; // Bu zaten number | null tipinde geliyor
  
  // LessonPackageData arayüzü, TeacherDetailsForAdminResult içindeki lessonPackages elemanının tipiyle eşleşmeli
  // Action dosyasında lessonPackages'ın price ve pricePerLessonCalculated alanları number'a çevriliyor.
  type PageLessonPackageData = TeacherDetailsForAdminResult['lessonPackages'][number];

  // Sertifikalar için güvenli dönüşüm (Artık action içinde parse ediliyor)
  const certificatesDisplay: string[] = Array.isArray(teacher.certificates)
    ? teacher.certificates.map((cert: CertificateEntry) => cert.name || JSON.stringify(cert))
    : [];
  
  // Eğitim bilgileri için güvenli dönüşüm (Artık action içinde parse ediliyor)
  const educationDisplay: string[] = Array.isArray(teacher.education)
    ? teacher.education.map((edu: EducationEntry) =>
        edu.degree ? `${edu.degree} - ${edu.institution || ''} (${edu.year || ''})` : JSON.stringify(edu)
      )
    : [];
  
  // İstatistikler (teacher.stats zaten action'da oluşturuluyor ve tipi TeacherDetailsForAdminResult içinde tanımlı)
  const stats = teacher.stats; // teacher.stats null olamaz çünkü action'da her zaman bir obje atanıyor.
  
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{fullName} - Öğretmen Profili</h1>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/dashboard/list/teachers">
              Öğretmenler Listesine Dön
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/dashboard/admin">
              Admin Paneline Dön
            </Link>
          </Button>
        </div>
      </div>
      
      {/* Üst Bilgi Kartı */}
      <Card className="mb-6 shadow-md">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-6 items-start">
            <div className="relative w-32 h-32 rounded-lg overflow-hidden bg-gray-200 flex-shrink-0">
              <Image
                src={teacher.profile_image_url || '/placeholder-avatar.png'}
                alt={fullName}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 128px"
              />
            </div>
            
            <div className="flex-grow space-y-4">
              <div>
                <h2 className="text-2xl font-bold">{fullName}</h2>
                <p className="text-muted-foreground">{teacher.title || "Almanca Öğretmeni"}</p> {/* teacher.title alanı action'da seçilmeli */}
              </div>
              
              <div className="flex flex-wrap gap-2">
                {teacher.is_approved && (
                  <Badge className="bg-green-500 hover:bg-green-600">Onaylanmış</Badge>
                )}
                {teacher.is_verified && (
                  <Badge variant="outline" className="border-blue-500 text-blue-500">Doğrulandı</Badge>
                )}
                {/* teacher.badges alanı Teacher modelinde yok, kaldırıldı. */}
              </div>
              
              <div className="text-sm">
                <p className="mb-1">{teacher.shortBio || teacher.aboutMe?.join(" ") || "Açıklama yok"}</p> {/* teacher.bio -> teacher.shortBio veya teacher.aboutMe */}
                <p className="text-muted-foreground">
                  {teacher.country && teacher.city ? `${teacher.country}, ${teacher.city}` : teacher.country || teacher.city || "Konum belirtilmemiş"}
                </p>
              </div>
            </div>
            
            <div className="flex flex-col gap-3 md:text-right">
              <div>
                <p className="text-lg font-bold">{hourlyRate ? `${hourlyRate} € / saat` : "Ücret belirtilmemiş"}</p>
                <p className="text-sm text-muted-foreground">Ders ücreti</p>
              </div>
              
              <div className="flex flex-col gap-1">
                <p className="text-sm"><span className="font-medium">{stats.totalLessons}</span> ders verdi</p>
                <p className="text-sm"><span className="font-medium">{stats.totalStudents}</span> öğrenci</p>
                <p className="text-sm"><span className="font-medium">{stats.reviewCount}</span> değerlendirme</p>
              </div>
              
              <div className="flex gap-2 mt-2 md:justify-end">
                <Button size="sm" variant="outline" asChild>
                  <Link href={`/teachers/${teacher.id}`} target="_blank">
                    Profili Görüntüle
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Kişisel Bilgiler */}
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Kişisel Bilgiler</CardTitle>
            <CardDescription>Öğretmenin temel bilgileri</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Ad Soyad</h3>
              <p className="text-base">{fullName}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Ülke</h3>
              <p className="text-base">{teacher.country || "Belirtilmemiş"}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Şehir</h3>
              <p className="text-base">{teacher.city || "Belirtilmemiş"}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Konuştuğu Diller</h3>
              <div className="flex flex-wrap gap-2 mt-1">
                {Array.isArray(teacher.spokenLanguages) && teacher.spokenLanguages.length > 0 ?
                  teacher.spokenLanguages.map((lang: string, i: number) => (
                    <Badge key={i} variant="outline">
                      {lang}
                    </Badge>
                  ))
                 : (
                  <p className="text-sm text-muted-foreground">Belirtilmemiş</p>
                )}
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Kayıt Tarihi</h3>
              <p className="text-base">{formatDate(teacher.created_at)}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Son Güncelleme</h3>
              <p className="text-base">{formatDate(teacher.updated_at)}</p>
            </div>
          </CardContent>
        </Card>
        
        {/* Eğitim ve Sertifikalar */}
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Eğitim ve Sertifikalar</CardTitle>
            <CardDescription>Öğretmenin eğitim geçmişi ve sertifikaları</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Eğitim</h3>
              {educationDisplay.length > 0 ? (
                <ul className="mt-1 space-y-1">
                  {educationDisplay.map((edu, i) => (
                    <li key={i} className="text-sm">• {edu}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-muted-foreground">Eğitim bilgisi belirtilmemiş</p>
              )}
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Sertifikalar</h3>
              {certificatesDisplay.length > 0 ? (
                <ul className="mt-1 space-y-1">
                  {certificatesDisplay.map((cert, i) => (
                    <li key={i} className="text-sm">• {cert}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-muted-foreground">Sertifika belirtilmemiş</p>
              )}
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Uzmanlık Alanları</h3>
              <div className="flex flex-wrap gap-2 mt-1">
                {Array.isArray(teacher.specializations) && teacher.specializations.length > 0 ?
                  teacher.specializations.map((spec: string, i: number) => (
                    <Badge key={i} variant="outline">
                      {spec}
                    </Badge>
                  ))
                 : (
                  <p className="text-sm text-muted-foreground">Belirtilmemiş</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Öğretim Bilgileri */}
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Öğretim Bilgileri</CardTitle>
            <CardDescription>Öğretmenin ders ve öğretim bilgileri</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Ders Seviyeleri</h3>
              <div className="flex flex-wrap gap-2 mt-1">
                {Array.isArray(teacher.levels) && teacher.levels.length > 0 ?
                  teacher.levels.map((level: string, i: number) => (
                    <Badge key={i} variant="outline">
                      {level}
                    </Badge>
                  ))
                 : (
                  <p className="text-sm text-muted-foreground">Belirtilmemiş</p>
                )}
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Ders Paketleri</h3>
              {teacher.lessonPackages && teacher.lessonPackages.length > 0 ? (
                <div className="mt-2 space-y-2">
                  {teacher.lessonPackages.map((pkg: PageLessonPackageData, i: number) => (
                    <div key={pkg.id || i} className="p-2 bg-gray-50 rounded-md">
                      <p className="text-sm font-medium">{pkg.name || `${pkg.lessonsInPackage || 'Bilinmeyen'} Ders Paketi`}</p>
                      <p className="text-xs text-muted-foreground">
                        Toplam Fiyat: {pkg.price !== undefined ? `${pkg.price} €` : "N/A"}
                        {pkg.pricePerLessonCalculated !== null && pkg.pricePerLessonCalculated !== undefined && ` (Ders Başı: ${pkg.pricePerLessonCalculated} €)`}
                        {pkg.discountPercentage !== undefined && pkg.discountPercentage !== null && pkg.discountPercentage > 0 && ` (${pkg.discountPercentage}% indirimli)`}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">Ders paketi belirtilmemiş</p>
              )}
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Tanıtım Videosu</h3>
              {teacher.intro_video_url ? (
                <div className="mt-2">
                  <Button size="sm" variant="outline" asChild>
                    <Link href={teacher.intro_video_url} target="_blank">
                      Videoyu İzle
                    </Link>
                  </Button>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">Tanıtım videosu yok</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Alt Bilgi Kartı */}
      <Card className="mt-6 shadow-md">
        <CardHeader>
          <CardTitle>Hesap Durumu</CardTitle>
          <CardDescription>Öğretmen hesabının mevcut durumu</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div className="flex items-center">
              <Badge variant={teacher.is_approved ? "default" : "secondary"} className={teacher.is_approved ? "bg-green-500 hover:bg-green-600" : ""}>
                {teacher.is_approved ? "Onaylandı" : "Onaylanmadı"}
              </Badge>
              <span className="ml-2 text-sm text-muted-foreground">
                {teacher.is_approved 
                  ? `${formatDate(teacher.updated_at)} tarihinde onaylandı` 
                  : "Henüz onaylanmadı"}
              </span>
            </div>
            
            <div className="flex items-center">
              <Badge variant={teacher.is_verified ? "default" : "secondary"} className={teacher.is_verified ? "bg-blue-500 hover:bg-blue-600" : ""}>
                {teacher.is_verified ? "Doğrulandı" : "Doğrulanmadı"}
              </Badge>
              <span className="ml-2 text-sm text-muted-foreground">
                {teacher.is_verified 
                  ? "Kimlik ve belgeler doğrulandı" 
                  : "Kimlik ve belgeler henüz doğrulanmadı"}
              </span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end space-x-4">
          <Button variant="outline" asChild>
            <Link href={`/dashboard/list/teacher-applications/${teacher.id}`}>
              Başvuru Detaylarını Görüntüle
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

// Yükleme durumu için iskelet bileşeni
function TeacherDetailSkeleton() {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-10 w-40" />
      </div>
      
      <Skeleton className="h-40 w-full mb-6" />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="border rounded-lg p-6">
            <Skeleton className="h-6 w-40 mb-2" />
            <Skeleton className="h-4 w-full mb-4" />
            
            <div className="space-y-4">
              {[1, 2, 3, 4].map((j) => (
                <div key={j}>
                  <Skeleton className="h-4 w-24 mb-1" />
                  <Skeleton className="h-5 w-full" />
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
