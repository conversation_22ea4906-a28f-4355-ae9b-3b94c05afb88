# AlmancaABC Proje Süreç Takibi

## Proje Genel Bakış
AlmancaABC, Almanca öğretmenleri ve öğrencileri buluşturan bir online eğitim platformudur.
Detaylı plan ve strateji için bkz: [AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md](./AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md)

**Teknoloji:** Next.js, React, TypeScript, Tailwind CSS, Supabase, Clerk, Zoom SDK, Stripe, Prisma, FullCalendar, Vercel, bun

## <PERSON><PERSON><PERSON><PERSON> (Genel Aşamalar)

*   ✅ **Aşama 0:** Hazırlık ve <PERSON> (Kısmen - Teknik olmayan adımlar hariç)
*   ✅ **Aşama 1:** Planlama ve Temel Kurulum
*   ✅ **Aşama 2:** Temel Özelliklerin Geliştirilmesi (MVP Odaklı - Devam Ediyor) # Dashboard temeli, listeleme ve detay sayfaları oluşturuldu, veri entegrasyonu başladı
*   ☐ **Aşama 3:** Test, Yayınlama ve Geri <PERSON> (MVP)
*   ☐ **Aşama 4:** Platformu Genişletme ve Büyütme (MVP Sonrası)
*   ☐ **Aşama 5:** Sürdürülebilirlik ve Güvenlik (Uzun Vadeli)

## Tamamlanan Görevler (Detaylı)

### Aşama 1: Planlama ve Temel Kurulum
*   ✅ Platform Özellikleri Netleştirildi ([Proje Planı](./AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md))
*   ✅ Teknoloji Yığını Seçildi ([Proje Planı Bölüm 7](./AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md#7-teknoloji-yığını-kararlaştırılan-ve-seçim-bekleyenler))
*   ✅ Geliştirme Ortamı Kuruldu (Node.js, bun, Git)
*   ✅ Repo Oluşturuldu (GitHub)
*   ✅ Proje Başlangıç Ayarları Yapıldı (Next.js, Supabase, Clerk)
*   ✅ Supabase MCP Bağlantısı Kuruldu
*   ✅ Prisma Kurulumu Yapıldı (`prisma init`)
*   ✅ `.env` dosyası Supabase URL'leri ile yapılandırıldı (`DATABASE_URL`, `DIRECT_URL`)
*   ✅ `prisma/schema.prisma` dosyası oluşturuldu ve `Teacher` modeli eklendi.
*   ✅ Temel Frontend Bileşenleri Oluşturuldu (Navbar, Footer, TeacherList vb.)
*   ✅ Sistem Tasarım Dokümanları Oluşturuldu/Güncellendi (PRD, System Design, Diagrams, Proje Planı)
*   ✅ Veritabanı Geçişi: `Teacher` tablosu manuel SQL ile oluşturuldu ve `prisma migrate resolve` ile işaretlendi.
*   ✅ Prisma Client oluşturuldu (`bunx prisma generate`)
*   ✅ Supabase Client (`src/utils/supabase/server.ts`) Clerk entegrasyonu ile güncellendi.

### Aşama 2: Temel Özelliklerin Geliştirilmesi (MVP Odaklı)
*   **2.1. Kullanıcı Kimlik Doğrulama ve Rol Yönetimi**
    *   ✅ Clerk Entegrasyonu Tamamlandı
    *   ✅ Middleware (`src/middleware.ts`) ile Temel Rota Koruması Eklendi (/teacher/**, /student/**)
    *   ✅ `/unauthorized` Sayfası Oluşturuldu
    *   ✅ Temel Profil Sayfaları Oluşturuldu (`/teacher/profile`, `/student/profile`)
    *   ✅ Clerk Rol Yönetimi Tamamlandı
        *   ✅ `src/middleware.ts` güncellendi - Rol bazlı erişim kontrolü eklendi
        *   ✅ `src/lib/actions/user.actions.ts` oluşturuldu - Kullanıcı rol atama fonksiyonları
        *   ✅ `src/lib/actions/auth.actions.ts` oluşturuldu - Rol bazlı erişim kontrol fonksiyonları
        *   ✅ `src/app/api/user/set-role/route.ts` oluşturuldu - Admin rol atama API endpoint'i
*   **2.2. Öğretmen Profili ve Yönetimi**
    *   ✅ Veritabanı: `teachers` tablosu için temel CRUD işlemleri (`src/lib/actions/teacher.actions.ts`)
    *   ✅ Veritabanı Bağlantısı: Prisma ve Supabase bağlantısı test edildi ve çalışır durumda
*   **Dashboard Yapısı (Referans Projeden Uyarlama)**
    *   ✅ `src/app/(dashboard)/layout.tsx` (Ana Dashboard Layout) oluşturuldu.
    *   ✅ `src/components/navbar.tsx` (Üst Navigasyon Barı) mevcut dosya kullanıldı ve import düzeltildi.
    *   ✅ `src/components/Menu.tsx` (Sol Kenar Çubuğu Menüsü) oluşturuldu ve rollere göre uyarlandı.
    *   ✅ `src/app/(dashboard)/page.tsx` (Ana Dashboard Yönlendirme Sayfası) oluşturuldu.
    *   ✅ `src/components/forms/TeacherProfileForm.tsx` (Öğretmen profil düzenleme formu) oluşturuldu ve uyarlandı.
    *   ✅ `src/components/forms/StudentProfileForm.tsx`: Öğrenci profil formu (oluşturuldu - Bkz: 2.3).
    *   ✅ `src/lib/actions/student.actions.ts`: Öğrenci CRUD işlemleri (oluşturuldu - Bkz: MVP Sonrası).

## Referans Proje Yapısı ve Uyarlama Checklist (`reference-dashboard`)

*Bu bölüm, `reference-dashboard` projesinden alınacak veya uyarlanacak ana yapıları ve bileşenleri listeler. Yapılacaklar listesindeki ilgili görevlerle eşleştirilmiştir.*

*   **Dashboard Temeli:**
    *   ✅ `src/app/(dashboard)/layout.tsx`: Ana layout (Sidebar + Main Content).
    *   ✅ `src/components/Navbar.tsx`: Üst navigasyon barı.
    *   ✅ `src/components/Menu.tsx`: Rol bazlı sol menü.
    *   ✅ `src/app/(dashboard)/page.tsx`: Rol bazlı ana sayfa yönlendirmesi.
*   **Listeleme Sayfaları Yapısı:**
    *   ⚙️ `src/app/(dashboard)/list/[entity]/page.tsx`: Genel listeleme sayfası şablonu (Öğretmenler, Öğrenciler vb. için uyarlanacak - Bkz: 2.3).
    *   ⚙️ `src/app/(dashboard)/list/[entity]/[id]/page.tsx`: Detay sayfası şablonu (Öğretmen, Öğrenci detayları - Bkz: 2.2, 2.3).
    *   ✅ `src/components/dashboard/DataTable.tsx`: Genel tablo bileşeni (Listeleme sayfaları için - Bkz: 2.3).
    *   ✅ `src/components/dashboard/Pagination.tsx`: Sayfalama bileşeni (Listeleme sayfaları için - Bkz: 2.3).
    *   ⚙️ `src/components/TableSearch.tsx` (veya gelişmiş filtreleme) bileşeni oluşturulacak/düzenlenecek.
*   **Form Bileşenleri:**
    *   ✅ `src/components/forms/TeacherProfileForm.tsx`: Öğretmen profil formu (oluşturuldu - Bkz: 2.2).
    *   ✅ `src/components/forms/StudentProfileForm.tsx`: Öğrenci profil formu (oluşturuldu - Bkz: 2.3).
    *   ⚙️ `BookingForm.tsx`: Rezervasyon formu (oluşturulacak - Bkz: 2.4).
    *   ⚙️ Diğer formlar (ihtiyaca göre - örn: `ParentProfileForm.tsx` - Bkz: 2.7).
*   **Backend Mantığı:**
    *   ✅ `src/lib/actions/teacher.actions.ts`: Öğretmen CRUD işlemleri (uyarlandı).
    *   ✅ `src/lib/actions/student.actions.ts`: Öğrenci CRUD işlemleri (oluşturuldu - Bkz: MVP Sonrası).
    *   ⚙️ `src/lib/actions/booking.actions.ts`: Rezervasyon işlemleri (oluşturulacak - Bkz: 2.4).
    *   ⚙️ `src/lib/actions/payment.actions.ts`: Ödeme işlemleri (oluşturulacak - Bkz: 2.4).
    *   ⚙️ `src/lib/actions/availability.actions.ts`: Müsaitlik işlemleri (oluşturulacak - Bkz: 2.2).
    *   ⚙️ `src/lib/actions/zoom.actions.ts`: Zoom toplantı işlemleri (oluşturulacak - Bkz: 2.5).
*   **Middleware ve Yetkilendirme:**
    *   ✅ `src/middleware.ts`: Rota koruması ve yönlendirmeler (uyarlandı).
    *   ⚙️ `src/lib/utils/auth.ts`: Yetkilendirme yardımcı fonksiyonları (oluşturulacak - Bkz: 2.1).

### 2.1. Kullanıcı Kimlik Doğrulama ve Rol Yönetimi
*   ⚙️ Rol Atama Mekanizması (Kayıt sırasında, Clerk Custom Attributes ile - *Clerk ayarı bekleniyor*)

### 2.2. Öğretmen Profili ve Yönetimi
*   ⚙️ Öğretmen Başvuru ve Onay Süreci
    *   ☐ `TeacherApplicationForm.tsx` oluşturulacak.
    *   ☐ Başvuruları listeleyecek Admin arayüzü (Basit liste).
    *   ☐ Başvuru onay/reddetme action'ları oluşturulacak (`approveTeacherApplication`, `rejectTeacherApplication`).
*   ⚙️ Öğretmen Müsaitlik Ayarları
    *   ☐ `AvailabilityCalendar.tsx` bileşeni oluşturulacak (FullCalendar kullanarak).
    *   ☐ Müsaitlik ekleme/silme action'ları oluşturulacak (`addAvailabilitySlot`, `removeAvailabilitySlot`).
    *   ☐ Veritabanında `AvailabilitySlot` tablosu oluşturulacak.

### 2.3. Öğrenci ve Öğretmen Listeleme/Arama
*   ⚙️ Öğretmen Listeleme Sayfası (`/ogretmenler`)
    *   ✅ Temel liste görünümü oluşturuldu.
    *   ✅ `DataTable.tsx` ve `Pagination.tsx` bileşenleri oluşturuldu/kullanıldı.
    *   ⚙️ Temel filtreleme (uzmanlık alanı, fiyat aralığı?) eklenecek.
*   ⚙️ Öğretmen Detay Sayfası (`/ogretmenler/[slug]` veya `/[id]`)
    *   ☐ `getTeacherById` (veya slug ile) action'ı oluşturulacak.
    *   ☐ Öğretmen bilgileri, biyografi, uzmanlık alanları gösterilecek.
    *   ☐ Müsaitlik takvimi gösterilecek (FullCalendar - salt okunur).
    *   ☐ Ders rezervasyon butonu/formu eklenecek.

### 2.4. Ders Rezervasyonu ve Ödeme
*   ⚙️ Ders Seçimi ve Rezervasyon
    *   ☐ Öğretmen detay sayfasında müsait zaman dilimlerini gösteren takvim (FullCalendar).
    *   ☐ Zaman dilimi seçme ve rezervasyon formu (`BookingForm.tsx`).
    *   ☐ Rezervasyon oluşturma action'ı (`createBooking`).
*   ⚙️ Ödeme Entegrasyonu (Stripe)
    *   ☐ Stripe hesabı oluşturulacak ve API anahtarları alınacak.
    *   ☐ Stripe Elements kütüphanesi projeye eklenecek.
    *   ☐ Ders satın alma için `createPaymentIntent` server action'ı oluşturulacak.
    *   ☐ Frontend'de Stripe Elements ile ödeme formu entegrasyonu yapılacak.
*   ⚙️ Komisyon Hesaplama ve Aktarım (Stripe Connect)
    *   ☐ Stripe Connect hesabı ayarlanacak (Platform hesabı).
    *   ☐ Öğretmenler için Stripe hesapları oluşturma/bağlama akışı (MVP sonrası olabilir).
    *   ☐ Ödeme sırasında komisyon ayrıştırma mantığı eklenecek (`transfer_data` parametresi).
*   ⚙️ Rezervasyon Onayı
    *   ☐ Ödeme başarılı olduktan sonra `Booking` durumu güncellenecek (`createBooking` action?).
    *   ☐ Temel e-posta bildirimi (Öğrenci ve Öğretmene - Resend/nodemailer?).
*   ⚙️ Veritabanı Modelleme
    *   ☐ `Booking`, `Payment` modelleri `prisma/schema.prisma`'ya eklenecek.
    *   ☐ İlişkiler (User, Teacher, AvailabilitySlot) tanımlanacak.
    *   *(Not: Veli rolü ve çocuk eşleştirmesi Clerk ayarı sonrası netleşecek)*.

### 2.5. Canlı Ders Entegrasyonu
*   ⚙️ Video SDK Entegrasyonu (Zoom SDK)
    *   ☐ Zoom Developer hesabı oluşturulacak ve SDK anahtarları alınacak.
    *   ☐ Zoom SDK Web kütüphanesi projeye eklenecek.
*   ⚙️ Otomatik Ders Odası Oluşturma
    *   ☐ Rezervasyon onaylandıktan sonra Zoom API kullanılarak toplantı oluşturma action'ı (`createZoomMeeting`?).
    *   ☐ Toplantı ID ve katılım linkleri `VideoSession` tablosuna kaydedilecek (Bu tablo eklenecek).
*   ⚙️ Temel Ders Odası Arayüzü (`/ders/[bookingId]`)
    *   ☐ Zoom SDK kullanılarak video/ses bağlantısı kurulacak.
    *   ☐ Temel kontroller (mikrofon/kamera aç/kapa), ekran paylaşımı butonu eklenecek.

### 2.6. Temel UI ve Stil
*   ⚙️ Modern Takvim Kütüphanesi Entegrasyonu (FullCalendar) - *Öğretmen Müsaitlik (2.2) ve Ders Seçimi (2.4) altında detaylandırıldı.*
*   ⚙️ İskelet Yükleyiciler (Skeleton Loaders)
    *   ☐ Listeleme sayfaları ve dashboard bileşenleri için Shadcn/ui Skeleton kullanılacak.
*   ⚙️ Temel Hata Yönetimi
    *   ☐ Form gönderimlerinde (Server Actions) try-catch blokları ve kullanıcı dostu hata mesajları (react-toastify ile?).
    *   ☐ API çağrılarında (varsa) hata durumları ele alınacak.
*   ⚙️ Shadcn/ui ve Tailwind CSS ile Stil İyileştirmeleri
    *   ☐ Mevcut bileşenler (Navbar, Menu, Kartlar vb.) Shadcn/ui prensiplerine göre gözden geçirilecek/iyileştirilecek.
    *   ☐ Yeni oluşturulacak bileşenlerde (Table, Pagination, FormModal vb.) Shadcn/ui kullanılacak.

### 2.7. Veli Arayüzü (Temel MVP)
*   ☐ Veli Dashboard'u Oluşturma (`/parent/dashboard`)
    *   ☐ Çocuğun/Çocukların ders programı gösterilecek (BigCalendar?).
    *   ☐ Yaklaşan sınavlar/ödevler listelenecek.
*   ☐ Veli Profil Sayfası Oluşturma (`/parent/profile`)
    *   ☐ Temel veli bilgileri formu (`ParentProfileForm.tsx`?).
*   ☐ Veritabanı ve Backend
    *   ☐ `Parent` modeli `prisma/schema.prisma`'ya eklenecek (Student ile ilişki).
    *   ☐ `parent.actions.ts` içinde temel CRUD işlemleri oluşturulacak.
    *   *(Not: Veli rolü ve çocuk eşleştirmesi Clerk ayarı sonrası netleşecek)*.

## Dashboard Yapısı İyileştirme (reference-dashboard Uyarlama)

*Bu bölüm, reference-dashboard projesindeki dashboard yapısını AlmancaABC'ye uyarlamak için yapılacaklar listesini içerir.*

### 1. Dashboard Layout ve Temel Bileşenler
*   ☐ **Dashboard Layout Düzenleme**
    *   ☐ `src/app/(dashboard)/layout.tsx` dosyasını reference-dashboard projesindeki gibi düzenle (sol menü + ana içerik yapısı)
    *   ☐ Responsive tasarımı iyileştir (mobil, tablet ve masaüstü görünümleri)
*   ☐ **Menü Bileşeni İyileştirme**
    *   ☐ `src/components/Menu.tsx` bileşenini reference-dashboard projesindeki gibi düzenle
    *   ☐ Rol bazlı menü öğeleri ekle (admin, öğretmen, öğrenci)
    *   ☐ İkonlar ve görsel iyileştirmeler yap
*   ☐ **Navbar Bileşeni İyileştirme**
    *   ☐ `src/components/navbar.tsx` bileşenini reference-dashboard projesindeki gibi düzenle
    *   ☐ Arama kutusu, bildirimler ve kullanıcı profil menüsü ekle
    *   ☐ Clerk UserButton entegrasyonu yap

### 2. Dashboard Ana Sayfası
*   ☐ **Dashboard Ana Sayfa İyileştirme**
    *   ☐ `src/app/(dashboard)/page.tsx` dosyasını reference-dashboard projesindeki gibi düzenle
    *   ☐ Rol bazlı içerik gösterimi ekle (admin, öğretmen, öğrenci)
    *   ☐ İstatistik kartları ve özet bilgiler ekle
*   ☐ **Admin Dashboard Sayfası**
    *   ✅ `src/app/(dashboard)/admin/page.tsx` dosyasını oluştur/düzenle (Temel yapı)
    *   ⚙️ İstatistik kartları (dinamik veri eklenecek)
    *   ⚙️ Bekleyen öğretmen başvuruları özeti (bileşen eklenecek)
    *   ⚙️ Son aktiviteler listesi (bileşen eklenecek)

### 3. Öğretmen ve Öğrenci Dashboard Sayfaları
*   ☐ **Öğretmen Dashboard Sayfası**
    *   ✅ `src/app/(dashboard)/teacher/page.tsx` dosyasını oluştur/düzenle (Temel yapı)
    *   ⚙️ Yaklaşan dersler listesi (dinamik veri eklenecek)
    *   ⚙️ Ders takvimi özeti (bileşen eklenecek)
    *   ⚙️ Öğrenci istatistikleri (bileşen eklenecek)
*   ✅ **Öğrenci Dashboard Sayfası**
    *   ✅ `src/app/(dashboard)/student/page.tsx` dosyasını oluştur/düzenle (Temel yapı)
    *   ⚙️ Yaklaşan dersler listesi (dinamik veri eklenecek)
    *   ⚙️ Öğretmen listesi (bileşen eklenecek)
    *   ⚙️ Ders geçmişi (bileşen eklenecek)

### 4. Liste Sayfaları
*   ✅ **Öğretmenler Liste Sayfası**
    *   ✅ `src/app/(dashboard)/admin/teachers/page.tsx` dosyasını oluştur/düzenle
    *   ✅ Tablo bileşeni (`DataTable`) ile öğretmen listesi (dinamik veri çekme eklendi)
    *   ✅ Temel arama (Input) ve sütun gizleme özellikleri eklendi.
    *   ✅ Sayfalama (`Pagination`) eklendi.
*   ✅ **Öğrenciler Liste Sayfası**
    *   ✅ `src/app/(dashboard)/admin/students/page.tsx` dosyasını oluştur/düzenle
    *   ✅ Tablo bileşeni (`DataTable`) ile öğrenci listesi (örnek veri ile, dinamik veri eklenecek)
    *   ✅ Temel arama (Input) ve sütun gizleme özellikleri eklendi.
    *   ✅ Sayfalama (`Pagination`) eklendi.
*   ✅ **Dersler Liste Sayfası**
    *   ✅ `src/app/(dashboard)/admin/bookings/page.tsx` dosyasını oluştur/düzenle
    *   ✅ Tablo bileşeni (`DataTable`) ile ders listesi (örnek veri ile, dinamik veri eklenecek)
    *   ✅ Temel arama (Input) ve sütun gizleme özellikleri eklendi.
    *   ✅ Sayfalama (`Pagination`) eklendi.

### 5. Detay Sayfaları
*   ✅ **Öğretmen Detay Sayfası**
    *   ✅ `src/app/(dashboard)/admin/teachers/[id]/page.tsx` dosyasını oluştur/düzenle
    *   ✅ Öğretmen profil bilgileri (dinamik veri çekme eklendi)
    *   ⚙️ Ders geçmişi bölümünü ekle (ilgili action ve tablo ile).
    *   ⚙️ İstatistikler bölümünü ekle (ilgili action ve grafiklerle).
    *   ⚙️ Müsaitlik takvimi bölümünü ekle (FullCalendar entegrasyonu sonrası).
*   ☐ **Öğrenci Detay Sayfası**
    *   ☐ `src/app/(dashboard)/list/students/[id]/page.tsx` dosyasını oluştur/düzenle
    *   ☐ Öğrenci profil bilgilerini, ders geçmişini ve istatistikleri göster.
*   ☐ **Rezervasyon Detay Sayfası**
    *   ☐ `src/app/(dashboard)/list/bookings/[id]/page.tsx` dosyasını oluştur/düzenle
    *   ☐ Rezervasyon detaylarını, öğrenci/öğretmen bilgilerini göster.

### 6. Ortak Bileşenler
*   ✅ **Tablo Bileşeni**
    *   ✅ `src/components/dashboard/DataTable.tsx` bileşenini oluştur/düzenle
    *   ✅ Sıralama, temel filtreleme (arama) ve sütun gizleme özellikleri eklendi.
    *   ⚙️ Responsive tasarım iyileştirilebilir.
*   ☐ **Arama Bileşeni**
    *   ⚙️ `src/components/TableSearch.tsx` (veya gelişmiş filtreleme) bileşeni oluşturulacak/düzenlenecek.
    *   ✅ `DataTable` içinde temel anlık arama özelliği eklendi.
*   ✅ **Sayfalama Bileşeni**
    *   ✅ `src/components/dashboard/Pagination.tsx` bileşenini oluştur/düzenle
    *   ✅ Sayfa numaraları, sayfa boyutu seçimi ve ileri/geri butonları eklendi.

### 7. Middleware ve Yetkilendirme
*   ☐ **Middleware İyileştirme**
    *   ☐ `src/middleware.ts` dosyasını düzenle
    *   ☐ Rol bazlı erişim kontrolü
    *   ☐ Yönlendirme mantığını iyileştir

## TODO & İyileştirme Notları (Navbar ve Genel UI)

- [ ] **Karanlık mod (Dark Mode) desteği eklenmeli.** Kullanıcı sağ üstte tema değiştirebilmeli.
- [ ] **Arama kutusu gerçek bir arama fonksiyonuna bağlanmalı.** (Server Action veya API ile)
- [ ] **Bildirimler Supabase veya benzeri ile dinamik ve gerçek zamanlı yapılmalı.**
- [ ] **Menüde rol bazlı özelleştirme yapılmalı.** (öğrenci/öğretmen/admin için farklı linkler)
- [ ] **Mobil menüde arama ve bildirimler de gösterilmeli.**
- [ ] **Erişilebilirlik (a11y) ve SEO için gerekli aria-label ve odak iyileştirmeleri yapılmalı.**

## Referans Dashboard Entegrasyon Planı (safak/next-dashboard-ui)

*Bu bölüm, `reference-dashboard` klasörüne klonlanan `safak/next-dashboard-ui` projesinin yapısını ve bileşenlerini kendi projemize adım adım entegre etme planını içerir.*

### Adım 1: Sidebar/Menü (`Menu.tsx`) Karşılaştırması ve İyileştirme
*   ✅ Referans projedeki menü bileşeninin veri yapısını incele.
*   ✅ Kendi `src/components/Menu.tsx` bileşenimizdeki veri yapısı, ikonlar, aktif durum gösterimi ve rol filtrelemesi referansa göre güncellendi (Lucide ikonları ve Shadcn Button kullanılarak).
*   ⚙️ Daraltma/genişletme gibi ek işlevler (MVP sonrası).

### Adım 2: Navbar (`navbar.tsx`) Karşılaştırması ve İyileştirme
*   ⚙️ Referans projedeki navbar bileşeninin yapısını incele (Dosya bulunamadı, genel pratiklere göre ilerleniyor).
*   ✅ Kendi `src/components/navbar.tsx` bileşenimizin dashboard görünümü (arama, bildirimler, kullanıcı) temel olarak oluşturuldu.
*   ✅ Arama kutusuna temel state ve konsol log işlevselliği eklendi (Gerçek arama mantığı ⚙️).
*   ✅ Bildirimler için DropdownMenu oluşturuldu (Statik içerik, dinamik veri ⚙️).
*   ✅ Clerk `UserButton` entegrasyonu mevcut ve çalışıyor (Stil iyileştirmesi ⚙️).

### Adım 3: Dashboard Widget'ları ve Kartları
*   ✅ Referans projedeki genel kart bileşenini (`app/ui/dashboard/cards.tsx`?) incele ve kendi projemize uyarlayarak `src/components/dashboard/StatCard.tsx` gibi bir bileşen oluştur.
*   ✅ **Admin Dashboard (`/admin/page.tsx`):**
    *   ✅ Öğretmen, öğrenci, ders sayısı için istatistik kartlarını (`StatCard`) oluştur ve dinamik verilerle doldur (ilgili `count` action'ları oluşturuldu - `admin.actions.ts`).
    *   ✅ Bekleyen öğretmen başvuruları için bir özet bileşeni (`PendingApplicationsCard`) oluşturuldu ve action (`getPendingTeacherApplicationsSummary`) eklendi.
    *   ✅ Son aktiviteler için bir liste bileşeni (`RecentActivitiesCard`) oluşturuldu ve action (`getRecentActivities`) eklendi (ActivityLog modeli de eklendi).
*   ☐ **Teacher Dashboard (`/teacher/page.tsx`):**
    *   ✅ Yaklaşan dersler listesi bileşeni (`UpcomingLessonsCard`) oluşturuldu ve action (`getUpcomingLessonsForTeacher`) eklendi.
    *   ✅ Ders takvimi özeti bileşeni oluştur (FullCalendar veya BigCalendar entegrasyonu sonrası).
    *   ✅ Öğrenci istatistikleri kartları oluştur (ilgili action'ları oluştur).
  *   ☐ **Student Dashboard (`/student/page.tsx`):**
    *   ✅ Yaklaşan dersler listesi bileşeni (`StudentUpcomingLessonsCard`) oluşturuldu ve action (`getUpcomingLessonsForStudent`) eklendi.
    *   ✅ Öğretmen listesi bileşeni oluştur (ilgili action'ı oluştur).
    *   ✅ Ders geçmişi özeti bileşeni oluştur (ilgili action'ı oluştur).
  *   ☐ **Parent Dashboard (`/parent/page.tsx`):**
    *   ✅ Çocukların yaklaşan dersleri bileşeni (`ChildrenScheduleCard`) oluşturuldu ve action (`getUpcomingLessonsForChildren`) eklendi (Parent modeli ve ilişki de eklendi). (Takvim görünümü ⚙️)
    *   ⚙️ Yaklaşan sınavlar/ödevler listesi bileşeni oluştur (ilgili action'ları oluştur).

### Adım 4: Liste Sayfaları İyileştirmeleri
*   ☐ **Gelişmiş Arama/Filtreleme:**
    *   ☐ Referans projedeki arama/filtreleme bileşenini (`app/ui/search.tsx`?) incele.
    *   ☐ `src/components/dashboard/TableSearch.tsx` (veya benzeri) bir bileşen oluştur.
    *   ☐ Öğretmen, öğrenci, rezervasyon listelerine bu bileşeni entegre et ve ilgili Server Action'ları filtreleme parametrelerini alacak şekilde güncelle.
*   ☐ **Yönetim Eylemleri (CRUD):**
    *   ✅ Öğretmen Onaylama (`approveTeacher`) eklendi ve tabloya entegre edildi.
    *   ☐ Öğretmen Reddetme (`rejectTeacher`) action'ı ve tablo entegrasyonu.
    *   ☐ Öğretmen Silme (`deleteTeacher`) action'ı ve tablo entegrasyonu (Confirm dialog ile).
    *   ☐ Öğretmen Düzenleme butonu/linki (Profil formuna yönlendirme).
    *   ☐ Öğrenci Silme/Düzenleme action'ları ve tablo entegrasyonu.
    *   ☐ Rezervasyon İptal/Düzenleme action'ları ve tablo entegrasyonu.
*   ☐ **Dinamik Veri Bağlantısı:**
    *   ☐ Öğrenci listesi için `getStudentsForAdmin` action'ı oluştur ve sayfaya bağla.
    *   ☐ Rezervasyon listesi için `getBookingsForAdmin` action'ı oluştur ve sayfaya bağla.

### Adım 5: Detay Sayfaları İyileştirmeleri
*   ☐ **Öğretmen Detay (`/admin/teachers/[id]`):**
    *   ☐ Ders geçmişi bölümünü ekle (ilgili action ve tablo ile).
    *   ☐ İstatistikler bölümünü ekle (ilgili action ve grafiklerle).
    *   ☐ Müsaitlik takvimi bölümünü ekle (FullCalendar entegrasyonu sonrası).
*   ☐ **Öğrenci Detay (`/admin/students/[id]`):**
    *   ☐ Sayfayı oluştur (`getStudentDetailsForAdmin` action'ı ile).
    *   ☐ Öğrenci profil bilgilerini, ders geçmişini ve istatistikleri göster.
*   ☐ **Rezervasyon Detay (`/admin/bookings/[id]`):**
    *   ☐ Sayfayı oluştur (`getBookingDetailsForAdmin` action'ı ile).
    *   ☐ Rezervasyon detaylarını, öğrenci/öğretmen bilgilerini göster.

### Adım 6: Form Yönetimi
*   ☐ Referans projedeki form yapısını (`app/ui/(teachers|students)/create-form.tsx` vb.) incele.
*   ☐ `src/components/dashboard/FormModal.tsx` (veya Shadcn Dialog'u genişleten) bir modal bileşeni oluştur.
*   ☐ Tüm CRUD işlemleri için gerekli formları (`StudentForm`, `BookingForm`, `SubjectForm` vb.) React Hook Form ve Zod kullanarak oluştur ve `FormModal` içine entegre et.
*   ☐ İlgili Server Action'ları (create, update) bu formlarla entegre et.

### Adım 7: Takvim Entegrasyonları
*   ☐ `react-calendar` kütüphanesini Admin dashboard'daki etkinlikler için entegre et.
*   ☐ `react-big-calendar` (veya FullCalendar) kütüphanesini Öğretmen/Öğrenci/Veli ders programları için entegre et.

### Adım 8: Diğer İyileştirmeler
*   ☐ Hata Yönetimi (`error.tsx`, `global-error.tsx`, toast bildirimleri) iyileştir.
*   ☐ Yüklenme Durumları (`loading.tsx`, Skeleton bileşenleri) ekle.
*   ☐ Genel Stil ve Tema (`globals.css`, `tailwind.config.ts`) referans projeyle uyumlu hale getir.
*   ☐ Responsive tasarımı tüm sayfa ve bileşenlerde kontrol et/iyileştir.
*   ☐ Yetkilendirme kontrollerini (`middleware.ts`, Server Actions) gözden geçir ve güçlendir.

## Notlar
*   Proje başlangıç aşamasında, MVP odaklı ilerleniyor.
*   Detaylı plan için `AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md` dosyasına bakınız.
*   **Mevcut Engeller/Bekleyenler:**
    *   ⚙️ Clerk rol atama ayarı ("Custom user attributes") beklenmektedir.
    *   ✅ Prisma migration sorunu çözüldü ve veritabanı bağlantısı test edildi.
    *   ✅ Öğretmen detay sayfasındaki 404 hatası çözüldü. "teacher-1" formatındaki ID'ler artık doğru şekilde işleniyor.
    *   Tarayıcı ve Brave Search MCP sunucuları ile bağlantı kurulamadı (Şu an için kritik değil).
    *   ⚠️ WhatsApp MCP sunucusunun başlatılması ve QR kod okutma işlemi ilk seferde biraz uzun sürebilir (yaklaşık 1-2 dakika). Sonraki kullanımlarda daha hızlı olacaktır.
    *   *Not: Prisma migration ve Clerk rol atama engelleri çözülene kadar ilgili görevlerde geçici çözümler (manuel SQL, varsayılan rollerle test) kullanılabilir.*
    *   Rakip analizi için `rakipler` klasörü oluşturuldu. Preply'den alınan ilk veriler bu klasöre taşındı. Detaylı analiz devam edecek.

## Kullanıcı Arayüzü İyileştirmeleri

### Menü ve Dashboard Modernizasyonu (10 Mayıs 2025)

#### Yapılan İyileştirmeler

1.  **Menü Bileşeni Modernizasyonu (`src/components/Menu.tsx`):**
    *   Menü grupları daha anlamlı ve rol odaklı kategorilere ayrıldı (ANA MENÜ, YÖNETİM, ÖĞRETMEN, ÖĞRENCİ, VELİ, İLETİŞİM, HESAP)
    *   Menü daraltma/genişletme özelliği eklendi (kullanıcı tercihine göre sidebar genişliği ayarlanabilir)
    *   İkonlar daha belirgin ve anlamlı hale getirildi, boyutları artırıldı
    *   Kullanıcının rolüne göre dinamik olarak ilgili menü grupları gösteriliyor
    *   Aktif menü öğesi vurgusu iyileştirildi (sol kenarlık ve renk ile)
    *   Mobil görünüm iyileştirildi
    *   Kaydırma çubuğu eklenerek uzun menülerde gezinme kolaylaştırıldı

2.  **Dashboard Layout İyileştirmesi (`src/app/(dashboard)/layout.tsx`):**
    *   Responsive tasarım güçlendirildi (mobil, tablet ve masaüstü için farklı davranışlar)
    *   Menü daraltıldığında layout otomatik olarak uyum sağlıyor
    *   Sayfa yüklendiğinde cihaz tipine göre otomatik menü durumu (mobil cihazlarda kapalı başlıyor)
    *   Animasyonlar ve geçişler eklendi (menü açılıp kapanırken daha akıcı deneyim)
    *   Mobil overlay arka planı iyileştirildi (blur efekti eklendi)
    *   İçerik alanı için maksimum genişlik ve container eklendi (daha iyi okunabilirlik)
    *   Alt bilgi bölümü eklendi (telif hakkı ve sürüm bilgisi)

3.  **Yeni Hook Eklendi (`src/hooks/useMediaQuery.ts`):**
    *   Responsive tasarımı desteklemek için medya sorgusu hook'u oluşturuldu
    *   Ekran boyutuna göre dinamik UI ayarlamaları için kullanılıyor

#### Karşılaşılan Zorluklar ve Çözümler

*   **Menü Daraltma/Genişletme:** Menü daraltıldığında içerik ve logo görünürlüğünün doğru şekilde ayarlanması için transition ve opacity özellikleri kullanıldı.
*   **Responsive Davranış:** Farklı ekran boyutlarında tutarlı bir deneyim sağlamak için `useMediaQuery` hook'u oluşturuldu ve kullanıldı.
*   **Menü Filtreleme:** Kullanıcı rolüne göre ilgisiz menü gruplarının tamamen gizlenmesi için filtreleme mantığı eklendi.

#### Sonuç

Yapılan iyileştirmeler sonucunda dashboard arayüzü daha modern, kullanıcı dostu ve profesyonel bir görünüme kavuştu. Kullanıcılar artık kendi tercihlerine göre menüyü daraltıp genişletebiliyor, sadece kendi rollerine uygun menü öğelerini görüyorlar. Mobil cihazlarda da daha iyi bir deneyim sunuluyor.

## Kod Kalitesi ve TypeScript İyileştirmeleri (1 Haziran 2025)

### Console Temizleme İşlemleri
*   ✅ **Console Statement Temizliği Tamamlandı**
    *   ✅ `src/components` dizinindeki tüm console.log, console.error ve console.warn ifadeleri temizlendi
    *   ✅ Temizlenen dosyalar:
        *   `src/components/forms/TeacherProfileForm.tsx` - 2 console.error ifadesi kaldırıldı
        *   `src/components/forms/TeacherApplicationForm.tsx` - 1 console.log ifadesi kaldırıldı
        *   `src/components/dashboard/TeacherCalendarSummary.tsx` - 1 console.error ifadesi kaldırıldı
        *   `src/components/admin/TeacherApplicationsTable.tsx` - 2 console.error ifadesi kaldırıldı
        *   `src/components/footer.tsx` - 1 console.error ifadesi kaldırıldı
        *   `src/components/TeacherProfileClient.tsx` - 1 console.error ifadesi kaldırıldı
        *   `src/components/TeacherCard.tsx` - 2 console.log ifadesi kaldırıldı
        *   `src/components/TeacherRecommendations.tsx` - 1 console.error ifadesi kaldırıldı
    *   ✅ Toplam 11 console ifadesi temizlendi
    *   ✅ Mevcut hata yönetimi mekanizmaları (toast bildirimleri, alert'ler) korundu
    *   ✅ Action dosyalarındaki console ifadeleri zaten önceden temizlenmiş/yorumlanmış durumda

### TypeScript Any Type Düzeltmeleri
*   ✅ **TypeScript Any Type Temizliği Tamamlandı**
    *   ✅ Proje genelinde 10 adet `any` type kullanımı tespit edildi ve düzeltildi
    *   ✅ Yeni tip tanımları oluşturuldu:
        *   `src/types/teacher.types.ts` - Öğretmen ile ilgili tip tanımları
        *   `src/types/form.types.ts` - Form ile ilgili tip tanımları
    *   ✅ Düzeltilen dosyalar ve tipler:
        *   `src/components/TeacherRecommendations.tsx`:
            - `teachers` prop için `SerializedTeacher[]` tipi
            - `parseStats` fonksiyonu için `TeacherStats` tipi
            - Map fonksiyonunda `teacher` parametresi için `SerializedTeacher` tipi
        *   `src/lib/actions/contact.actions.ts`:
            - `prevState` parametresi için `ContactFormState` tipi
        *   `src/components/calendar/TeacherAvailabilityCalendar.tsx`:
            - Event handler için uygun tip tanımı
        *   `src/app/(dashboard)/list/teachers/[id]/page.tsx`:
            - Certificate ve education data için `CertificateData` ve `EducationData` tipleri
        *   `src/app/(dashboard)/list/teacher-applications/[id]/page.tsx`:
            - Certificate ve education data için tip tanımları
        *   `src/components/Table.tsx`:
            - Generic tip parametresi eklendi
    *   ✅ Faydalar:
        *   Tip güvenliği artırıldı
        *   IDE otomatik tamamlama desteği iyileştirildi
        *   Hata tespiti geliştirme aşamasında yapılabilir hale geldi
        *   Kod okunabilirliği artırıldı
        *   Refactoring güvenliği sağlandı

### Prisma Schema Eksik Modeller Planlaması
*   ⚙️ **Sonraki Adım: Prisma Schema Tamamlama**
    *   ☐ `Review` modelinde eksik alanlar eklenmeli
        * helpful_count - Yorumun kaç kişi tarafından faydalı bulunduğu
        * reported_count - Şikayet sayısı
        * is_verified - Doğrulanmış yorum mu
        * lesson_date - Dersin yapıldığı tarih
    *   ☐ `Payment` modelinde eksik alanlar eklenmeli
        * stripe_payment_intent_id
        * refund_amount
        * refund_reason
        * currency (varsayılan TRY)
    *   ☐ `Booking` modelinde eksik durumlar eklenmeli
        * RESCHEDULED - Ertelenmiş
        * NO_SHOW_TEACHER - Öğretmen gelmedi
        * NO_SHOW_STUDENT - Öğrenci gelmedi
        * TECHNICAL_ISSUE - Teknik sorun
    *   ☐ `TeacherApplication` modelinde eksik durumlar eklenmeli
        * INTERVIEW_SCHEDULED - Mülakat planlandı
        * INTERVIEW_COMPLETED - Mülakat tamamlandı
        * DOCUMENTS_REQUESTED - Belge talep edildi
        * BACKGROUND_CHECK - Geçmiş kontrolü
    *   ☐ `Notification` modeli oluşturulmalı (kritik öncelik)
        * id - Bildirim ID'si
        * user_id - Bildirimin gönderildiği kullanıcı
        * type - Bildirim tipi (BOOKING, MESSAGE, PAYMENT, SYSTEM)
        * title - Bildirim başlığı
        * content - Bildirim içeriği
        * is_read - Okundu mu
        * created_at - Oluşturulma zamanı
        * action_url - Tıklandığında yönlendirilecek URL
    *   ☐ `Message/Chat` modeli oluşturulmalı (kritik öncelik)
        * Conversation modeli
          * id - Konuşma ID'si
          * participants - Katılımcılar (User[] ilişkisi)
          * created_at - Oluşturulma zamanı
          * updated_at - Son mesaj zamanı
          * is_active - Aktif mi
        * Message modeli
          * id - Mesaj ID'si
          * conversation_id - Bağlı olduğu konuşma
          * sender_id - Gönderen kullanıcı
          * content - Mesaj içeriği
          * is_read - Okundu mu
          * created_at - Gönderilme zamanı
          * attachments - Ekler (opsiyonel)
    *   **Öncelik Sıralaması:**
        * Yüksek Öncelik:
            1. Notification Modeli - Kullanıcı deneyimi için kritik
            2. Message/Chat Modeli - Öğretmen-öğrenci iletişimi için
            3. Review Modeli Güncellemeleri - Güvenilirlik için
        * Orta Öncelik:
            4. Booking Durumları - Ders yönetimi için
            5. Payment Güncellemeleri - Finansal işlemler için
        * Düşük Öncelik:
            6. TeacherApplication Durumları - Admin süreçleri için
    *   **Uygulama Planı:**
        1. Notification Modeli Oluşturma - Bildirim sistemi altyapısı
        2. Message/Chat Modeli Oluşturma - Mesajlaşma sistemi
        3. Mevcut Modelleri Güncelleme - Eksik alanları ekleme
        4. Migration Dosyaları Oluşturma - Veritabanı güncellemeleri
        5. İlgili TypeScript Tiplerini Güncelleme - Tip güvenliği

## Güncelleme Tarihi
---
## Öğretmenler Sayfası Yeniden Tasarımı (Devam Ediyor - 18 Mayıs 2025)

*Bu bölüm, `/ogretmenler` sayfasının Preply ve diğer rakip sitelerden (Özel Ders Alanı, Superprof, Armut, ÖzelDers.com, Goethe Enstitüsü) ilham alarak modern bir tasarımla yeniden yapılandırılması görevlerini içerir.*

### 1. Rakip Analizi ve İlham Alma
*   ✅ Preply, Özel Ders Alanı ve Armut siteleri incelendi. Detaylı karşılaştırmalı analiz ve AlmancaABC için çıkarımlar [`rakipler/rakip_site_karsilastirmali_analiz.md`](./rakipler/rakip_site_karsilastirmali_analiz.md) dosyasına eklendi.
*   ☐ Superprof ([`https://www.superprof.com.tr/ders/almanca/turkiye/`](https://www.superprof.com.tr/ders/almanca/turkiye/)) incelenecek.
*   ☐ ÖzelDers.com ([`https://www.ozelders.com/ders-verenler/yabanci-dil/almanca?t=1764`](https://www.ozelders.com/ders-verenler/yabanci-dil/almanca?t=1764)) incelenecek.
*   ☐ Goethe Enstitüsü ([`https://www.goethe.de/ins/de/tr/kur/ang/ind.html`](https://www.goethe.de/ins/de/tr/kur/ang/ind.html)) incelenecek.
*   ✅ Ana sayfadaki öğretmen kartı tasarımı (ekran görüntüsü ile sağlandı) incelendi ve ızgara görünümü için temel alınacak.

### 2. Temel Sayfa Yapısı ve İçeriği (`src/app/teachers/page.tsx`)
*   ✅ Mevcut `page.tsx.backup` dosyasından yeni `page.tsx` oluşturuldu.
*   ✅ Ana başlık "Online Almanca Kursu: Almanca Özel Ders Öğretmeni Seç" olarak güncellendi.
*   ✅ Preply'den ilham alarak kısa bir tanıtım metni eklendi.
*   ✅ `AppLayoutWrapper` import hatası düzeltildi.
*   ✅ Mock öğretmen verileri (`mockTeachers`, `AIRecommendations`) güncellenmiş `Teacher` tipine uygun hale getirildi.
*   ✅ `cn` fonksiyonu importu eklendi.
*   ✅ Kullanılmayan `Loader2` ikonu ve `isLoading`, `setIsLoading` state'leri kaldırıldı.

### 3. Tip Güncellemeleri
*   ✅ `src/types/teacher.ts` (`Teacher` tipi):
    *   ✅ `activeStudents` eklendi.
    *   ✅ `totalLessons` eklendi.
    *   ✅ `isSuperTeacher` eklendi.
    *   ✅ `spokenLanguages` (dil ve seviye içeren obje dizisi) eklendi.
*   ✅ `src/types/filters.ts` (`TeacherFilters` tipi):
    *   ✅ `spokenLanguages` (string dizisi) eklendi.
    *   ✅ `isNativeSpeaker` (boolean | null) eklendi.
    *   ✅ `sortBy` ('relevance' | 'price_asc' | 'price_desc' | 'rating_desc' | 'newest') eklendi.

### 4. Filtreleme Bileşeni (`src/components/FilterBar.tsx`)
*   ✅ Yeni filtre seçenekleri eklendi:
    *   ✅ Konuştuğu Diller (Checkbox listesi).
    *   ✅ Ana Dili (Checkbox: "Sadece Ana Dili Konuşanlar").
    *   ✅ Sıralama (Select: İlgiye Göre, Fiyat Artan/Azalan, Puan Azalan, En Yeni).
*   ✅ Filtre butonları ve menülerinin görünümü Preply'ye benzetildi.
*   ✅ `handleFilterChange` fonksiyonu yeni filtre tiplerini işleyecek şekilde güncellendi.
*   ✅ Aktif filtre sayısı hesaplaması güncellendi.
*   ✅ TypeScript hataları düzeltildi.
*   ✅ Sıralama (`sortBy`) `Select` bileşeni filtre çubuğuna eklendi.
*   ✅ Filtre çubuğunun mobil cihazlarda yatay kaydırılabilir olması sağlandı.

### 5. Öğretmen Kartı Bileşeni (`src/components/TeacherCard.tsx`)
*   ✅ **Tasarım Entegrasyonu (Izgara Görünümü):** Ana sayfadaki kart tasarımı temel alınarak ızgara görünümü büyük ölçüde entegre edildi.
    *   ✅ `VerifiedBadge` kullanılıyor.
    *   ✅ Favorilere ekleme (kalp ikonu) işlevselliği mevcut.
    *   ✅ Müsaitlik durumu gösteriliyor ve "Bugün müsait" için canlı yeşil renk ayarlandı.
    *   ✅ "Süper Öğretmen" rozeti gösteriliyor.
    *   ✅ Avatar kullanılıyor.
    *   ✅ İsim, başlık, ülke bayrağı, puan, yorum sayısı, ücret, uzmanlıklar, bio, konuşulan diller, aktif öğrenci, toplam ders gibi bilgiler gösteriliyor.
    *   ✅ "Mesaj Gönder" (beyaz) ve "Ders Al" (siyah) butonları stilize edildi.
*   ✅ **Liste Görünümü İyileştirmeleri:**
    *   ✅ Sağlanan örnek koda göre modern ve detaylı bir liste görünümü tasarlandı.
    *   ✅ Butonlar (Yeşil "Deneme Dersi Al", Siyah "Ders Planla", Beyaz "Mesaj Gönder") ve Favori butonu eklendi.
    *   ✅ Mavi tik, Süper Öğretmen, AI Önerisi rozetleri entegre edildi.
    *   ✅ Dikey daraltma yapılarak kart yüksekliği optimize edildi.
    *   ✅ Avatar boyutu kullanıcı isteğine göre ayarlandı.
*   ☐ **Eklenebilecekler (Rakip Analizinden):**
    *   ☐ Kartlara "Deneyim Yılı" bilgisi eklenebilir.
    *   ☐ Kartlara (özellikle liste görünümüne) "Mezun Olduğu Üniversite" bilgisi eklenebilir.
    *   ☐ Öğretmenlerin kısa video tanıtımları için bir ikon/link eklenebilir.

### 6. Listeleme Görünümleri (`src/app/teachers/page.tsx`)
*   ✅ Yatay (liste) ve Izgara (grid) olmak üzere iki farklı öğretmen listeleme görünümü için state (`viewMode`) eklendi.
*   ✅ Kullanıcının bu iki görünüm arasında geçiş yapabilmesi için butonlar (`LayoutGrid`, `List` ikonları ile) eklendi.
*   ✅ Öğretmen listesi, seçili `viewMode`'a göre koşullu olarak render ediliyor.
*   ✅ Liste görünümü varsayılan olarak ayarlandı.
*   ✅ Izgara görünümünde kartların sola yaslanması ve aralarındaki boşluk ayarlandı.
*   ✅ Izgara görünümünde sütun sayıları farklı ekran boyutları için optimize edildi (üst üste binme sorunu giderildi).

### 7. Sayfalama (`src/app/teachers/page.tsx` ve `src/components/Pagination.tsx`)
*   ✅ Mevcut "Daha Fazla Yükle" butonu yerine `Pagination` bileşeni kullanılarak sayfa numaralandırması eklendi.
*   ✅ URL'de sayfa numarası (`?sayfa=`) gösteriliyor ve okunuyor.
*   ✅ `ITEM_PER_PAGE` sabiti kullanılıyor.

### 8. Ek Özellikler ve İyileştirmeler
*   ☐ "Size Özel Öneriler" ve "Tüm Öğretmenler" bölümleri korunacak ve yeni tasarıma uyarlanacak.
*   ✅ Filtreleme sisteminin mevcut işlevselliği korunuyor ve yeni filtrelerle entegre edildi.
*   ✅ "Sonuç Bulunamadı" mesajına "Filtreleri Temizle" butonu eklendi.
*   ✅ Sayfa arka plan rengi `#f2f4f7` olarak değiştirildi.
*   ✅ Öğretmen kartlarına `randomuser.me` üzerinden gerçekçi fotoğraflar atandı.
*   ☐ **Eklenebilecekler (Rakip Analizinden):**
    *   ☐ "Neden AlmancaABC?" bölümü eklenebilir.
    *   ☐ Platform istatistikleri (toplam öğretmen, ders vb.) gösterilebilir.
    *   ☐ Öğretmenler sayfasına SSS bölümü eklenebilir.
*   ☐ Genel CSS ve stil iyileştirmeleri yapılacak.
*   ✅ Responsive tasarım (filtre çubuğu, kartlar) büyük ölçüde iyileştirildi, kontroller devam etmeli.
## Öğretmenler Sayfası Yeniden Tasarımı (Devam Ediyor - 18 Mayıs 2025)

*Bu bölüm, `/ogretmenler` sayfasının Preply ve diğer rakip sitelerden (Özel Ders Alanı, Superprof, Armut, ÖzelDers.com, Goethe Enstitüsü) ilham alarak modern bir tasarımla yeniden yapılandırılması görevlerini içerir.*

### 1. Rakip Analizi ve İlham Alma
*   ☐ Özel Ders Alanı ([`https://www.ozeldersalani.com/almanca`](https://www.ozeldersalani.com/almanca)) incelenecek.
*   ☐ Superprof ([`https://www.superprof.com.tr/ders/almanca/turkiye/`](https://www.superprof.com.tr/ders/almanca/turkiye/)) incelenecek.
*   ☐ Armut ([`https://armut.com/almanca-ozel-ders`](https://armut.com/almanca-ozel-ders)) incelenecek.
*   ☐ ÖzelDers.com ([`https://www.ozelders.com/ders-verenler/yabanci-dil/almanca?t=1764`](https://www.ozelders.com/ders-verenler/yabanci-dil/almanca?t=1764)) incelenecek.
*   ☐ Goethe Enstitüsü ([`https://www.goethe.de/ins/de/tr/kur/ang/ind.html`](https://www.goethe.de/ins/de/tr/kur/ang/ind.html)) incelenecek.
*   ✅ Preply ([`https://preply.com/tr/online/almanca-e%C4%9Fitimi`](https://preply.com/tr/online/almanca-e%C4%9Fitimi)) incelendi.

### 2. Temel Sayfa Yapısı ve İçeriği (`src/app/teachers/page.tsx`)
*   ✅ Mevcut `page.tsx.backup` dosyasından yeni `page.tsx` oluşturuldu.
*   ✅ Ana başlık "Online Almanca Kursu: Almanca Özel Ders Öğretmeni Seç" olarak güncellendi.
*   ✅ Preply'den ilham alarak kısa bir tanıtım metni eklendi.
*   ✅ `AppLayoutWrapper` import hatası düzeltildi.
*   ✅ Mock öğretmen verileri (`mockTeachers`, `AIRecommendations`) güncellenmiş `Teacher` tipine uygun hale getirildi.

### 3. Tip Güncellemeleri
*   ✅ `src/types/teacher.ts` (`Teacher` tipi):
    *   ✅ `activeStudents` eklendi.
    *   ✅ `totalLessons` eklendi.
    *   ✅ `isSuperTeacher` eklendi.
    *   ✅ `spokenLanguages` (dil ve seviye içeren obje dizisi) eklendi.
*   ✅ `src/types/filters.ts` (`TeacherFilters` tipi):
    *   ✅ `spokenLanguages` (string dizisi) eklendi.
    *   ✅ `isNativeSpeaker` (boolean | null) eklendi.
    *   ✅ `sortBy` ('relevance' | 'price_asc' | 'price_desc' | 'rating_desc' | 'newest') eklendi.

### 4. Filtreleme Bileşeni (`src/components/FilterBar.tsx`)
*   ✅ Yeni filtre seçenekleri eklendi:
    *   ✅ Konuştuğu Diller (Checkbox listesi).
    *   ✅ Ana Dili (Checkbox: "Sadece Ana Dili Konuşanlar").
    *   ✅ Sıralama (Select: İlgiye Göre, Fiyat Artan/Azalan, Puan Azalan, En Yeni).
*   ✅ Filtre butonları ve menülerinin görünümü Preply'ye benzetildi.
*   ✅ `handleFilterChange` fonksiyonu yeni filtre tiplerini işleyecek şekilde güncellendi.
*   ✅ Aktif filtre sayısı hesaplaması güncellendi.
*   ✅ TypeScript hataları düzeltildi.

### 5. Öğretmen Kartı Bileşeni (`src/components/TeacherCard.tsx`)
*   ☐ Genel yerleşim ve stil Preply ve sağlanan görsellere göre modernleştirilecek.
*   ☐ Avatar ve online durumu göstergesi (şimdilik görsel yer tutucu).
*   ☐ Öğretmen bilgileri alanı güncellenecek: İsim, başlık, **ülke bayrağı**, "Süper Öğretmen" rozeti.
*   ☐ Puan, değerlendirme sayısı, saatlik ücret ve ders süresi belirginleştirilecek.
*   ☐ İstatistikler alanı eklenecek: Aktif öğrenci, toplam ders.
*   ☐ Konuşulan diller (seviyeleriyle birlikte) listelenecek.
*   ☐ Uzmanlık alanları (badge'ler) düzenlenecek.
*   ☐ Öğretmen açıklaması (snippet) eklenecek.
*   ☐ "Deneme Dersi Ayırt" ve "Mesaj Gönder" butonları yeni tasarıma uygun hale getirilecek.
*   ☐ Liste görünümü için de kart tasarımı iyileştirilecek ve yeni alanlar eklenecek.

### 6. Listeleme Görünümleri
*   ☐ Yatay (liste) ve Izgara (grid) olmak üzere iki farklı öğretmen listeleme görünümü eklenecek.
*   ☐ Kullanıcının bu iki görünüm arasında geçiş yapabilmesi için bir buton/ikon eklenecek.
*   ☐ `/kurslar` sayfasındaki benzer yapı referans alınabilir.

### 7. Sayfalama
*   ☐ Mevcut "Daha Fazla Yükle" butonu yerine sayfa numaralandırması kullanılacak.
*   ☐ URL'de sayfa numarası gösterilecek (örn: `/ogretmenler?sayfa=2`).
*   ☐ Google'ın sayfa bazlı indexlemesi için bu yapı tercih edilecek.

### 8. Ek Özellikler ve İyileştirmeler
*   ☐ "Müsaitlik durumu" (örn: "Bugün müsait", "Yarın müsait") kartlarda gösterilecek.
*   ☐ "Önerilen sıralama" varsayılan olacak.
*   ☐ Genel CSS ve stil iyileştirmeleri yapılacak.
*   ☐ Responsive tasarım tüm ekran boyutları için kontrol edilecek ve iyileştirilecek.
18 Mayıs 2025 // Son güncelleme: Menü ve dashboard arayüzü modernize edildi, kullanıcı deneyimi iyileştirildi.

---
## Rakip Analizi ve Öğretmenler Sayfası Geliştirme Notları (20 Mayıs 2025)

Aşağıda belirtilen rakip siteler incelenmiş ve `/ogretmenler` sayfamızın geliştirilmesi için potansiyel iyileştirme alanları ve ilham alınabilecek noktalar belirlenmiştir.

**İncelenen Rakipler:**

*   Preply ([`https://preply.com/tr/online/almanca-e%C4%9Fitimi`](https://preply.com/tr/online/almanca-e%C4%9Fitimi))
*   Goethe Enstitüsü ([`https://www.goethe.de/ins/tr/tr/spr/kur/fer.html`](https://www.goethe.de/ins/tr/tr/spr/kur/fer.html))
*   Superprof ([`https://www.superprof.com.tr/ders/almanca/webcam/`](https://www.superprof.com.tr/ders/almanca/webcam/))
*   Özel Ders Alanı ([`https://www.ozeldersalani.com/almanca/online`](https://www.ozeldersalani.com/almanca/online))
*   Armut ([`https://armut.com/online-almanca-ozel-ders`](https://armut.com/online-almanca-ozel-ders))

**Genel Geliştirme Alanları ve Fikirler:**

**1. Kullanıcı Deneyimi (UX) ve Arayüz (UI):**

*   **Kullanıcı Odaklı Hızlı Filtreleme/Yönlendirme (Preply, Superprof):**
    *   "Sana Uygun Öğretmeni Bul" gibi bir sihirbaz (wizard) veya kullanıcıyı adım adım yönlendiren bir soru-cevap mekanizması eklenebilir.
*   **Öğretmen Kartı Tasarımı ve İçeriği:**
    *   **Video Tanıtım (Preply):** Öğretmenlerin kısa video tanıtımlarını yükleyebileceği ve kartlarda bunun gösterileceği bir alan.
    *   **Anında Müsaitlik / Takvim Entegrasyonu (Preply):** Kart üzerinde öğretmenin yakın zamandaki müsaitliğini gösteren bir işaret veya "Programın tamamını gör" gibi bir takvim linki. (FullCalendar entegrasyonu ile geliştirilebilir).
    *   **Detaylı İstatistikler (Özel Ders Alanı, Preply):**
        *   Deneyim yılı.
        *   Ortalama cevap süresi.
        *   Profil görüntülenme sayısı.
        *   Aktif öğrenci sayısı / Toplam verilen ders sayısı (bizde var, sunumu iyileştirilebilir).
    *   **Mezuniyet Bilgisi (Özel Ders Alanı):** Öğretmenlerin mezun olduğu üniversite ve fakülte bilgisi.
    *   **Öğretmenin Kendine Ait Sloganı/Başlığı (Superprof):** Kartlarda öğretmenin dikkat çekici bir sloganı/başlığı.
    *   **"İlk Ders Ücretsiz" Vurgusu (Superprof, Preply):** Bu seçeneği sunan öğretmenler için belirgin bir işaret.
    *   **"Hızlı Talep Oluştur" Butonu (Özel Ders Alanı):** Profile gitmeden hızlıca ders talebi oluşturma.
*   **Filtreleme ve Sıralama Çeşitliliği:**
    *   **Detaylı Seviye/Hedef Filtresi (Özel Ders Alanı, Goethe):** "Aile Birleşimi", "Goethe Sınavı", "İş Almancası" gibi çok spesifik hedeflere göre filtreleme.
    *   **Öğretmen Yanıt Süresi Filtresi (Özel Ders Alanı):** Hızlı yanıt veren öğretmenleri filtreleme.
    *   **Çeşitli Sıralama Seçenekleri (Özel Ders Alanı, Superprof):** "En Çok Yorum Alan", "En Çok Görüntüleme Alan", "Son Kayıt Olan" gibi.
*   **Platformun Çalışma Prensibini Anlatma (Superprof, Armut):** Basit adımlarla platformun nasıl kullanılacağını anlatan bir bölüm.

**2. SEO ve İçerik Stratejisi:**

*   **Sayfa Başlıkları (`title`), Ana Başlıklar (`H1`) ve Meta Açıklamaları:** Rakip kullanımlarını inceleyerek kendi başlık ve açıklamalarımızı optimize etmek. (Mevcut yapımız iyi, ancak anahtar kelime stratejisi gözden geçirilebilir).
*   **Yapılandırılmış Veri (Schema.org) Stratejisi:**
    *   **Genel Bulgular (Preply & Superprof HTML Analizinden):**
        *   Tüm büyük platformlar Schema.org'u (JSON-LD veya Microdata) yoğun bir şekilde kullanıyor.
        *   **Sık Kullanılan Schema Tipleri:**
            *   `WebPage`, `BreadcrumbList`: Sayfa yapısı ve navigasyon için temel.
            *   `FAQPage`: SSS bölümleri için standart.
            *   `ItemList` ve `ListItem`: Öğretmen listeleri ve kartları için. Her `ListItem` içinde öğretmenin profil sayfasına `url` itemprop'i ile link vermek önemli.
            *   `Person` (veya `Teacher` alt tipi, `EducationalOccupationalCredential` ile genişletilmiş `Person`): Bireysel öğretmen profilleri için. Önemli `itemprop`'lar: `name`, `image`, `description`, `url`, `knowsAbout` (uzmanlık), `knowsLanguage`, `priceRange` (veya `makesOffer` ile `Offer` şeması), `aggregateRating`, `review`.
            *   `LocalBusiness` (veya `EducationalOrganization`, `OnlineBusiness`): Platformun kendisini tanımlamak için.
            *   `Course`: Eğer yapılandırılmış kurslar sunuluyorsa.
            *   `Review` ve `AggregateRating`: Öğretmen ve platform değerlendirmeleri için.
            *   `Table`: İstatistiksel verilerin sunumu için.
        *   **Meta Etiketler:** Kapsamlı `og:*` (Open Graph) ve `twitter:*` (Twitter Card) etiketleri, dinamik `title` ve `description`'lar, `canonical` URL'ler ve çok dilli siteler için `hreflang` etiketleri yaygın.
    *   **AlmancaABC İçin Aksiyonlar:**
        *   `/ogretmenler` sayfamız için `ItemList` şemasını entegre et.
        *   [`TeacherCard.tsx`](src/components/TeacherCard.tsx:1) bileşenini `ListItem` ve içinde `Person` (veya `EducationalOccupationalCredential` ile genişletilmiş `Person`) şeması ile işaretle. Öğretmenin adı, resmi, açıklaması, URL'si, uzmanlık alanları, konuştuğu diller, fiyatı/teklifleri, genel puanı ve yorumları için uygun `itemprop`'ları kullan.
        *   Öğrenci yorumları için `Review` ve `AggregateRating` şemalarını öğretmen profillerine ve kartlarına entegre et.
        *   Mevcut `FAQPage` şemamızı `/sss` ve diğer ilgili sayfalarda zenginleştir.
        *   Tüm önemli sayfalar için dinamik ve açıklayıcı `og:*` ve `twitter:*` meta etiketleri oluştur.
        *   Semantik HTML5 etiketlerinin (`<main>`, `<article>`, `<section>`) ve başlık hiyerarşisinin (`<h1>`-`<h6>`) doğru kullanımını sağla.
        *   Erişilebilirlik (a11y) standartlarına (anlamlı `alt` metinleri, form `label`'ları, ARIA nitelikleri) dikkat et.
*   **Kapsamlı SSS Bölümleri (Tüm Rakipler):** Online dersler, öğretmen seçimi, fiyatlandırma, teknik konular gibi birçok başlıkta detaylı SSS. (Bizdeki SSS zenginleştirilebilir).
*   **SEO Odaklı Ek İçerikler (Preply, Superprof, Armut, Özel Ders Alanı):** Öğretmen listeleme sayfasının altına, "Online Almanca Öğrenmenin Faydaları", "Doğru Almanca Öğretmeni Nasıl Seçilir?" gibi konularda blog tarzı veya bilgilendirici uzun metinler eklemek.

**3. Güven ve İkna Unsurları:**

*   **Net Değer Önerisi ve İstatistikler (Armut, Preply, Superprof):** Platformdaki öğretmen sayısı, ortalama puan, toplam yorum/ders sayısı gibi istatistikleri ana sayfada veya öğretmenler sayfasında belirgin şekilde göstermek.
*   **Platform Garantileri (Armut):** "AlmancaABC Memnuniyet Garantisi" gibi bir güvence sunmak (örneğin, ilk dersten memnun kalmazsanız farklı öğretmenle ücretsiz deneme dersi gibi).
*   **Onaylı Yorumlar ve Rozetler (Tüm Rakipler):** Yorumların gerçek kullanıcılardan geldiğini vurgulamak, "Uzman Öğretmen", "Popüler Öğretmen", "Doğrulanmış Profil" gibi rozetler kullanmak.

**4. Ek Özellikler ve Fonksiyonlar:**

*   **Yapılandırılmış Kurs Paketleri (Goethe):** Bireysel derslerin yanı sıra, belirli hedeflere yönelik (örn: A1 Yoğun Kurs) paket programlar sunma fikri değerlendirilebilir.
*   **Öğretmenler İçin Bilgilendirme (Özel Ders Alanı):** Platforma nasıl öğretmen olarak katılabileceklerine dair net bilgiler.

**Öncelikli Yapılacaklar (Bu analiz sonrası ilk adımlar):**

1.  **Detaylı HTML Analizi ve Schema.org Stratejisi (Tamamlandı - 20 Mayıs 2025):** Yukarıdaki analizler ışığında Schema.org stratejimiz netleşmiştir.
2.  **Mevcut SEO Öğelerini Gözden Geçirme:** Kendi `/ogretmenler` sayfamızdaki `title`, `H1`, meta açıklama ve Schema.org (`FAQPage` ve `Teacher`) implementasyonunu yukarıdaki strateji ve rakip analizleri ışığında iyileştirmek.
3.  **Filtreleme ve Sıralama Seçeneklerini Zenginleştirme:** Kullanıcı geri bildirimleri ve rakip analizlerine göre en çok talep görebilecek yeni filtre (örn: "Ana Dili Konuşan", "Sınav Türü", "Deneyim Yılı", "Yanıt Süresi") ve sıralama seçeneklerini planlamak.
4.  **Öğretmen Kartı İçeriğini Geliştirme:** Kartlara "Deneyim Yılı", "Video Tanıtım İkonu/Linki", "Ortalama Cevap Süresi", "Mezuniyet Bilgisi" gibi bilgileri eklemeyi değerlendirmek.
---
## Rakip Analizi Notları (20 Mayıs 2025 Devamı)

### 1. Goethe Enstitüsü (goethe.de/ins/tr/tr/spr/kur/fer.html)

**Genel Gözlemler:**

*   **Kurumsal ve Resmi İmaj:** Site, bir kültür enstitüsünün resmiyetini ve ciddiyetini yansıtıyor. AlmancaABC'nin daha kişisel ve kullanıcı dostu yaklaşımından farklı bir konumlandırma.
*   **Geniş Kapsam:** Sadece dil kursları değil, kültürel etkinlikler, kütüphane hizmetleri, Almanya hakkında bilgiler gibi çok geniş bir yelpazede içerik sunuyorlar.
*   **Fiziksel Merkezler:** Online kursların yanı sıra Ankara, İstanbul, İzmir gibi şehirlerde fiziksel enstitüleri ve kursları bulunuyor.

**Kurs Yapısı ve Çeşitliliği:**

*   **Online Kurslar:** A1-C2 seviyelerinde, genellikle öğretmen rehberliğinde, sanal sınıflarda grup dersleri şeklinde sunuluyor.
*   **Hedefe Yönelik Kurslar:**
    *   "Aile Birleşimi Kursu - Start Deutsch 1"
    *   "Sınava Hazırlık Kursu" (A1-B2 seviyeleri için)
    *   "Konuşma Kursu" (A1.1 - B1.4 seviyeleri için)
*   **Kurs Formatları ve Süreleri:**
    *   Farklı yoğunluklarda (örn: 4 haftada 64 ders birimi (DB), 8 haftada 64 DB, 8 haftada 128 DB).
    *   Haftalık canlı ders saati ve otonom çalışma süreleri belirtilmiş.
    *   Katılımcı sayıları genellikle maksimum 16-18 kişi.
*   **Fiyatlandırma:** Kurs ücretleri TL cinsinden net bir şekilde belirtilmiş (örn: Aile Birleşimi 19.800 TL, Konuşma Kursu 1.050 TL, Yoğun Online Kurs 64DB 9.900 TL).

**Öne Çıkan Özellikler ve Teklifler:**

*   **Seviye Tespit Sınavı:** Kullanıcıların Almanca seviyelerini belirleyip uygun kursu bulmalarına yardımcı olan online bir araç.
*   **Öğretmen Rehberliği:** Online kurslarda uzman Goethe Enstitüsü öğretmenlerinin rehberlik ettiği vurgulanıyor.
*   **Profesyonel ve Çeşitlendirilmiş Materyaller:** Kaliteli öğrenme materyalleri sundukları belirtiliyor.
*   **Esnek Öğrenme Saatleri:** Online eğitimin temel avantajlarından biri olarak sunuluyor.
*   **Öğrenme Platformu Erişimi:** Kurs süresince ve sonrasında belirli bir süre (örn: +3 ay) öğrenme platformuna erişim imkanı.

**Site Yapısı ve Kullanıcı Deneyimi:**

*   **Detaylı Navigasyon:** Çok katmanlı ve kapsamlı bir menü yapısı var.
*   **Bilgilendirici İçerik:** Kurs detayları, seviyeler, sınavlar hakkında ayrıntılı bilgi sunuluyor.
*   **İletişim ve Destek:** Sorular için iletişim formu ve farklı şehirlerdeki enstitülerin iletişim bilgileri mevcut.

**AlmancaABC İçin Çıkarımlar ve İyileştirme Fikirleri:**

*   **Hedefe Yönelik Özel Ders Paketleri:**
    *   Goethe'nin "Aile Birleşimi", "Sınav Hazırlığı" gibi kurslarından ilham alarak, AlmancaABC'de de benzer hedeflere yönelik özel ders paketleri (örn: "Goethe A1 Sınavına Hazırlık Paketi", "TestDaF Yoğun Hazırlık Programı", "Almanya'da Üniversite için Almanca") oluşturulabilir. Bu paketler belirli sayıda ders saati, odaklanılacak konular ve belki ek materyaller içerebilir.
*   **Online Seviye Tespit Aracı:**
    *   Kullanıcıların kendi Almanca seviyelerini hızlıca belirleyebilecekleri ve buna göre öğretmen/ders paketi önerileri alabilecekleri basit, interaktif bir online seviye tespit testi (quiz) AlmancaABC'ye eklenebilir. Bu, kullanıcıların doğru başlangıç noktasını bulmalarına yardımcı olur.
*   **Ders Paketi Çeşitliliği ve Esneklik:**
    *   Farklı yoğunluk ve sürelerde (örn: haftada 2 saatlik rahat tempo, haftada 5 saatlik yoğun program) ve farklı toplam ders saati içeren (örn: 10 derslik başlangıç paketi, 30 derslik kapsamlı paket) özel ders paketleri sunularak farklı öğrenci ihtiyaçlarına ve bütçelerine hitap edilebilir.
*   **"Neden AlmancaABC?" Vurgusu:**
    *   Goethe Enstitüsü'nün kurumsal yapısına karşın, AlmancaABC'nin **birebir özel ders** odaklı, **kişiye özel program** esnekliği, **öğretmen seçme özgürlüğü** ve daha **samimi/yakın öğrenme ortamı** gibi avantajları net bir şekilde vurgulanmalıdır.
*   **Destekleyici SEO İçerikleri:**
    *   "Almanca Seviyeleri Nelerdir?", "Goethe Sınavlarına Nasıl Hazırlanılır?", "Online Almanca Öğrenmenin Avantajları" gibi bilgilendirici blog yazıları veya SSS bölümleri hem SEO'ya katkı sağlar hem de kullanıcılara değer sunar.
*   **Şeffaf Fiyatlandırma ve Paket İçerikleri:**
    *   Goethe'deki gibi, sunulan her ders paketinin veya hizmetin fiyatı ve içeriği (kaç ders, ders süresi, ek materyal olup olmadığı vb.) net bir şekilde belirtilmelidir.
*   **Öğretmen Kalitesi ve Uzmanlığı Vurgusu:**
    *   Goethe'nin "uzman öğretmen" vurgusuna benzer şekilde, AlmancaABC'deki öğretmenlerin deneyimleri, uzmanlık alanları (sınav hazırlığı, konuşma pratiği vb.) ve sertifikaları profillerinde net bir şekilde belirtilmelidir.

---
### 2. Preply (preply.com/tr/online/almanca-e%C4%9Fitimi)

**Genel Gözlemler:**

*   **Kullanıcı Odaklı Arayüz:** Site, kullanıcıyı öğretmen bulma sürecinde aktif olarak yönlendiriyor ("Öğretmen bul", "Kişiselleştirilmiş öğretmen seçkisi").
*   **Geniş Öğretmen Havuzu:** Çok sayıda (1.930+) Almanca öğretmeni listeleniyor.
*   **Esnek Fiyatlandırma:** Ders ücretleri geniş bir aralıkta ($3 - $40+) ve genellikle 50 dakikalık dersler için belirtilmiş.
*   **Uluslararası Platform:** Birçok dil ve para birimi seçeneği sunuyor.
*   **Memnuniyet Garantisi:** "%100 memnuniyet sağlayana kadar ücretsiz öğretmen değişikliği" gibi bir güvence sunuyorlar.

**Öne Çıkan Özellikler ve Teklifler:**

*   **Kişiselleştirilmiş Öğretmen Eşleştirme:** "Hızlıca birkaç soru cevaplayarak kişiselleştirilmiş bir öğretmen seçkisine eriş" gibi bir sihirbaz (wizard) benzeri araçları var.
*   **Detaylı Filtreleme Seçenekleri:**
    *   Ders ücreti
    *   Öğretmenin doğduğu ülke
    *   Öğrenciye uygun saatler
    *   Uzmanlık alanları (örn: Konuşma, İş Almancası, Çocuklar için Almanca, TestDaF hazırlık)
    *   Öğretmenin konuştuğu diğer diller
    *   Ana dili Almanca olanlar
    *   "Süper Öğretmen" rozeti (deneyim, yorum gibi kriterlere göre)
*   **Kapsamlı Öğretmen Profilleri/Kartları:**
    *   Video tanıtım imkanı.
    *   Fotoğraf, isim, geldiği ülke (bayrakla).
    *   Puan ve değerlendirme sayısı.
    *   Saatlik ders ücreti ve ders süresi.
    *   Aktif öğrenci sayısı ve toplam verdiği ders sayısı.
    *   Konuştuğu diller ve seviyeleri.
    *   Kısa ve detaylı tanıtım metinleri (çeviri seçeneğiyle).
    *   Öğrenci yorumları (isim, tarih ve yorum metni).
    *   "Deneme dersi ayırt" ve "Mesaj gönder" butonları.
    *   Öğretmenin takvimine ("Programın tamamını gör") erişim.
*   **SEO ve İçerik Stratejisi:**
    *   Anahtar kelime odaklı sayfa başlıkları ve açıklamalar.
    *   Platformun nasıl çalıştığını anlatan basit adımlar.
    *   "Neden Preply?" gibi platform avantajlarını vurgulayan bölümler.
    *   İstatistiksel verilerle desteklenmiş iddialar (ortalama öğretmen puanı, yanıt süresi vb.).
    *   Kapsamlı SSS bölümü.
    *   Çok sayıda iç linkleme (popüler kurslar, diğer diller, şehir bazlı kurslar vb.).
    *   "Öğretmenin ülkesine göre Almanca dersi ücretleri" gibi bilgilendirici tablolar.
*   **Ek Hizmetler:**
    *   Kurumsal dil eğitimi.
    *   Öğretmenler için platforma katılım imkanı.
    *   Mobil uygulamalar (iOS, Android).

**AlmancaABC İçin Çıkarımlar ve İyileştirme Fikirleri:**

*   **Kullanıcı Dostu Öğretmen Eşleştirme Aracı:**
    *   AlmancaABC'ye, kullanıcının hedeflerini (sınav hazırlığı, konuşma pratiği, iş Almancası vb.), mevcut seviyesini, bütçesini ve tercih ettiği öğrenme stilini soran kısa bir anket/sihirbaz ekleyerek ona en uygun öğretmenleri önerebiliriz. Bu, özellikle çok sayıda öğretmen arasından seçim yapmayı kolaylaştırır.
*   **Filtreleme ve Sıralama Yeteneklerini Geliştirme:**
    *   Mevcut filtrelerimize ek olarak "Öğretmenin Doğduğu Ülke" (ana dilini konuşanları ayırt etmek için), "Süper Öğretmen" (belirli başarı kriterlerini karşılayan öğretmenler için bir rozet sistemi), "Öğrenci Yorum Sayısı" gibi seçenekler eklenebilir.
    *   Sıralama seçenekleri çeşitlendirilebilir: "En Yüksek Puanlı", "En Çok Ders Veren", "En Yeni Katılanlar", "Fiyata Göre (Artan/Azalan)".
*   **Zenginleştirilmiş Öğretmen Kartları ve Profilleri:**
    *   [`TeacherCard.tsx`](src/components/TeacherCard.tsx:1) bileşenine "Aktif Öğrenci Sayısı", "Toplam Verilen Ders Sayısı" gibi istatistiksel bilgiler eklenebilir.
    *   Öğretmenlerin kısa (1-2 dakikalık) video tanıtımları yükleyebileceği bir özellik geliştirilebilir ve kartlarda/profillerde gösterilebilir. Bu, öğrencilerin öğretmenle daha kişisel bir bağ kurmasına yardımcı olur.
    *   Öğretmenlerin müsaitlik takvimlerine doğrudan kart üzerinden veya profil sayfasından kolayca erişilebilmeli ([`TeacherAvailabilityCalendar.tsx`](src/components/TeacherAvailabilityCalendar.tsx:1) ve [`TeacherSchedule.tsx`](src/components/TeacherSchedule.tsx:1) bileşenleri bu amaçla geliştirilebilir).
    *   Öğrenci yorumları daha görünür hale getirilebilir ve belki filtrelenebilir (örn: en yeni yorumlar).
*   **SEO ve İçerik Pazarlaması:**
    *   `/ogretmenler` ([`src/app/ogretmenler/page.tsx`](src/app/ogretmenler/page.tsx:1)) sayfamızın başlığı, meta açıklaması ve H1 etiketi, "online Almanca özel ders", "Almanca öğretmeni bul", "kişiye özel Almanca kursu" gibi anahtar kelimeleri içerecek şekilde optimize edilmeli.
    *   "Neden AlmancaABC ile Almanca Öğrenmelisiniz?" gibi bir bölüm oluşturularak platformumuzun benzersiz avantajları (kaliteli öğretmenler, esnek programlar, kişiye özel ders planları, modern arayüz vb.) vurgulanmalı.
    *   "Online Almanca Derslerinin Faydaları", "Almanca Öğrenmeye Nereden Başlamalı?", "TestDaF Sınavına Nasıl Hazırlanılır?" gibi konularda kapsamlı blog yazıları veya SSS içerikleri oluşturulmalı. Bu içerikler hem SEO'ya katkı sağlar hem de potansiyel öğrencilere değer sunar.
    *   Schema.org işaretlemeleri (özellikle `Person` veya `Teacher`, `EducationalOccupationalCredential`, `ItemList`, `ListItem`, `Review`, `AggregateRating`) öğretmen listeleme ve profil sayfalarına entegre edilmeli.
*   **Güven ve İkna Edicilik:**
    *   "İlk Dersten Memnun Kalmazsanız Ücret İadesi" veya "Farklı Bir Öğretmenle Ücretsiz Deneme Dersi" gibi bir memnuniyet garantisi sunmayı değerlendirebiliriz.
    *   Belirli kriterleri (yüksek puan, çok sayıda ders, olumlu yorumlar) karşılayan öğretmenlere "Popüler Öğretmen", "Deneyimli Eğitmen" gibi rozetler verilebilir.
*   **Ders Paketleri ve Deneme Dersi:**
    *   Öğrencilere farklı ihtiyaçlara yönelik ders paketleri (örn: 5 derslik başlangıç paketi, 10 derslik konuşma pratiği paketi, 20 derslik sınav hazırlık paketi) sunulabilir.
    *   Yeni öğrenciler için (belki indirimli veya daha kısa süreli) bir "Tanışma Dersi" veya "Deneme Dersi" seçeneği sunulabilir. Bu, öğrencinin öğretmenle uyumunu test etmesine olanak tanır.
*   **Teknik Altyapı ve Kullanılabilirlik:**
    *   Öğretmen profillerinde veya mesajlaşma sisteminde, farklı dillerde yazılmış içerikler için otomatik çeviri özelliği (eğer öğretmen veya öğrenci farklı ana dillere sahipse) düşünülebilir.
    *   Platformun mobil uyumluluğu ve hızı sürekli olarak test edilmeli ve iyileştirilmelidir.

---
### 3. Superprof (superprof.com.tr/ders/almanca/webcam/)

**Genel Gözlemler:**

*   **Geniş Konu Yelpazesi:** Sadece dil dersleri değil, birçok farklı alanda özel ders imkanı sunuyor.
*   **Öğretmen Odaklı Platform:** Öğretmenlerin kendi profillerini oluşturup derslerini listelediği bir yapıya sahip.
*   **Fiyat Esnekliği:** Öğretmenler kendi saatlik ücretlerini belirliyor (Örn: 285TL'den başlayan ücretler, ortalama 285TL/saat).
*   **İlk Ders Ücretsiz Seçeneği:** Birçok öğretmen ilk dersi ücretsiz sunuyor (%99 oranında).
*   **Hızlı Yanıt Süresi:** Öğretmenlerin genellikle 5 saat içinde cevap verdiği belirtiliyor.
*   **Yorum ve Puanlama Sistemi:** Öğrenciler öğretmenleri değerlendirebiliyor (Örn: Ortalama 5 üzerinden 5 puan, 948 yorum).

**Öne Çıkan Özellikler ve Teklifler:**

*   **Detaylı Öğretmen Profilleri/Kartları:**
    *   Öğretmen fotoğrafı.
    *   İsim, ders verdiği şehir (online/yüz yüze bilgisi).
    *   Puan ve yorum sayısı.
    *   "Lider" veya "Süper Öğretmen" gibi rozetler.
    *   Öğretmenin kendi ağzından dikkat çekici bir başlık/slogan.
    *   Saatlik ücret ve ilk dersin ücretsiz olup olmadığı bilgisi.
*   **Filtreleme ve Arama:**
    *   Konu (Ne öğrenmek istersiniz?).
    *   Konum (Yakınlarımda ara / Online).
*   **Platformun Çalışma Şekli Açıklaması:**
    *   "Öğrenmek hiç bu kadar kolay olmadı" başlığı altında 3 adımda süreç anlatılıyor:
        1.  En iyi öğretmeni bul (Profilleri incele, kriterlere göre seç).
        2.  Dersleri planla (Öğretmenle iletişim kur, zaman belirle, güvenli öde).
        3.  Yeni deneyimler edin (Öğrenci Pass ile 1 yıl sınırsız erişim).
*   **Öğrenci Yorumları:** Gerçek öğrenci yorumları ve puanlamaları öğretmen profillerinde ve genel değerlendirme bölümlerinde öne çıkarılıyor.
*   **SSS (Sıkça Sorulan Sorular) Bölümü:**
    *   Online ders nasıl alınır?
    *   Uzaktan ders verenler nasıl seçilir? (Kimlik, diploma kontrolü vurgusu)
    *   Kaç öğretmen müsait?
    *   Online ders ücreti ortalaması nedir?
    *   Öğretmenlere verilen ortalama not nedir?
    *   Neden webcam üzerinden online ders almalıyım? (Popülerlik, kolaylık vurgusu)
*   **Çoklu İletişim Seçenekleri:** Skype, Hangout, Zoom, Whatsapp, Telefon gibi araçlarla ders işleme imkanı.

**AlmancaABC İçin Çıkarımlar ve İyileştirme Fikirleri:**

*   **"Süper Öğretmen" veya "Lider Eğitmen" Rozet Sistemi:**
    *   AlmancaABC'de de belirli kriterleri (yüksek öğrenci memnuniyeti, tamamlanan ders sayısı, hızlı yanıt süresi, ek sertifikalar vb.) karşılayan öğretmenlere benzer rozetler vererek öne çıkmalarını sağlayabiliriz. Bu, öğrencilerin güvenini artırır ve kaliteli öğretmenleri ayırt etmelerine yardımcı olur.
*   **Öğretmenlerin Kendilerini İfade Edebileceği Alanlar:**
    *   Öğretmen profillerinde, Superprof'taki gibi öğretmenlerin kendi öğretim tarzlarını, deneyimlerini ve öğrencilere ne katabileceklerini anlatan dikkat çekici bir başlık veya kısa bir "Hakkımda" bölümü eklenebilir. Bu, öğretmenlerin kişiliğini yansıtmasına olanak tanır.
*   **"İlk Ders Ücretsiz" veya "Tanışma Dersi" Seçeneği:**
    *   Öğretmenlere isteğe bağlı olarak "İlk Ders Ücretsiz" veya indirimli/kısa süreli bir "Tanışma Dersi" sunma seçeneği verilebilir. Bu, öğrencilerin öğretmeni ve ders işleyişini risk almadan deneyimlemesini sağlar.
*   **Platformun Kullanım Kolaylığını Vurgulama:**
    *   Superprof'un "Öğrenmek hiç bu kadar kolay olmadı" bölümü gibi, AlmancaABC'nin de nasıl çalıştığını (öğretmen bulma, ders planlama, ödeme yapma, derse katılma adımları) basit ve anlaşılır bir şekilde anlatan bir bölüm eklenebilir.
*   **Detaylı ve Şeffaf SSS Bölümü:**
    *   Online derslerin avantajları, öğretmen seçimi, teknik gereksinimler, ödeme yöntemleri, ders iptal politikaları gibi konularda kapsamlı bir SSS ([`src/app/sss/page.tsx`](src/app/sss/page.tsx:1)) bölümü oluşturulmalı ve sürekli güncellenmelidir.
*   **Çeşitli İletişim Araçları Entegrasyonu (MVP Sonrası):**
    *   Derslerin Zoom SDK üzerinden yapılması planlanmakla birlikte, öğretmen ve öğrencinin ders öncesi/sonrası iletişim için platform içi mesajlaşma dışında (onaylı) farklı araçları (örn: Superprof'taki gibi Skype, Whatsapp vb. üzerinden iletişim kurma opsiyonu) kullanabilme esnekliği, kullanıcıların tercihine bırakılabilir. Ancak güvenlik ve takip açısından platform içi mesajlaşma öncelikli olmalıdır.
*   **SEO İçin Alt Başlıklar ve İç Linkler:**
    *   Superprof'un "Alana göre" (Almanca kelime bilgisi, Almanca konuşma becerileri vb.) ve "Popüler şehirler" gibi alt başlıklarla oluşturduğu iç linkleme yapısı, AlmancaABC için de benzer şekilde uygulanabilir. Örneğin, "Goethe Sınavı Hazırlık Dersleri", "İş Almancası Özel Ders", "Çocuklar için Online Almanca" gibi alt kategoriler oluşturularak hem kullanıcı deneyimi iyileştirilebilir hem de SEO'ya katkı sağlanabilir.
*   **Öğretmen Yanıt Süresi Bilgisi:**
    *   Öğretmen profillerinde veya kartlarında ortalama yanıt sürelerinin gösterilmesi, öğrencilerin hızlı iletişim kurabilecekleri öğretmenleri tercih etmelerine yardımcı olabilir.

---
### 4. Özel Ders Alanı (ozeldersalani.com/almanca/online)

**Genel Gözlemler:**

*   **Geniş Öğretmen Ağı:** Çok sayıda (1.935) online Almanca öğretmeni listeleniyor.
*   **Yerel Odak:** Türkiye merkezli bir platform olduğu anlaşılıyor, fiyatlar TL cinsinden.
*   **Filtreleme Çeşitliliği:** Kullanıcılara ders yeri, ücret, seviye, cinsiyet, cevaplama süresi, bölge ve sıralama gibi birçok filtre seçeneği sunuluyor.
*   **Detaylı Öğretmen Profilleri:** Öğretmenlerin deneyim yılı, ortalama cevap süresi, profil görüntülenme sayısı, aktif öğrenci sayısı, toplam verdiği ders sayısı gibi istatistiksel bilgiler sunuluyor.
*   **"Uzman Öğretmen" ve "Popüler" Rozetleri:** Belirli kriterlere göre öğretmenler öne çıkarılıyor.

**Öne Çıkan Özellikler ve Teklifler:**

*   **Kapsamlı Filtreleme:**
    *   **Ders Yeri:** Online, İstanbul, Ankara, İzmir vb. (AlmancaABC için "Online" ana odak olacak).
    *   **Ders Ücreti:** Belirli aralıklarda (örn: 550-700 TL, 700-800 TL).
    *   **Seviye Seçimi:** A1-C2, İlkokul, LGS Hazırlık, YKS Hazırlık, Aile Birleşimi Sınavı, Goethe, TELC gibi çok detaylı seviye ve hedef seçenekleri.
    *   **Cinsiyet Seçimi:** Öğretmen cinsiyeti.
    *   **Cevaplama Süresi:** "Sadece aynı gün cevap veren öğretmenleri gör."
    *   **Bölge Seçimi:** (Online dersler için daha az relevant olabilir).
    *   **Sıralama Ölçütü:** Önerilen, Puana Göre, Fiyata Göre, Son Kayıt Olana Göre, En Çok Yorum Alan, En Çok Görüntüleme Alan, Sadece Mezun Eğitmenler.
*   **Detaylı Öğretmen Kartları/Profilleri:**
    *   Öğretmen fotoğrafı, ismi, uzmanlık alanı ("Almanca Öğretmeni", "Almanca Ve İngilizce...").
    *   Konum (örn: İstanbul/Şişli).
    *   Deneyim yılı.
    *   Ortalama cevap süresi.
    *   Profil görüntülenme sayısı.
    *   Puan ve yorum sayısı.
    *   Mezun olduğu üniversite ve fakülte.
    *   Verdiği dersler (birden fazla ders verebiliyorlar).
    *   Kısa tanıtım metni.
    *   Aktif öğrenci sayısı ve toplam verdiği ders sayısı.
    *   Saatlik ücret (₺/SAAT).
    *   "HIZLI TALEP OLUŞTUR" butonu.
*   **SEO ve İçerik:**
    *   Sayfa başında "Online Almanca Kursu : 1.935 Uzman Öğretmen ile Online Almanca Dersleri" gibi net ve anahtar kelime odaklı başlıklar.
    *   "Her Bütçeye Uygun Almanca Kursları (Fiyat aralığı 500₺ - 2500₺)" gibi kullanıcıya yönelik bilgilendirme.
    *   Breadcrumb navigasyonu.
    *   "En verimli Online Almanca dersi almak için ne yapmalıyım?" gibi kullanıcıya yol gösteren bölümler ve adımlar (Öğretmen profillerini incele, bütçene uygun seç, keyfini çıkar).
    *   Öğrenci yorumları ve öğretmenler hakkında genel istatistikler (toplam öğretmen, 5 yıldızlı yorum sayısı).
    *   Kapsamlı SSS bölümü (Online Almanca dersi fiyatları, eğitmenlere nasıl ulaşılır, neden online kurs alınmalı, online ders nasıl verilir vb.).
    *   Sayfa sonunda Almanca öğrenmenin önemi, online eğitimin avantajları gibi konularda uzun SEO metinleri.
*   **Ek Özellikler:**
    *   "Ders Talepleri" bölümü (öğrenciler genel talep oluşturup öğretmenlerin ulaşmasını sağlayabilir).
    *   "Online Akademi" (muhtemelen grup dersleri veya yapılandırılmış kurslar için).
    *   "Kurumsal Eğitim" seçeneği.

**AlmancaABC İçin Çıkarımlar ve İyileştirme Fikirleri:**

*   **Çok Detaylı Filtreleme Seçenekleri Sunmak:**
    *   Özel Ders Alanı'ndaki gibi, AlmancaABC'de de öğrencilerin **öğrenme hedeflerine** (Goethe sınavı A1, B2, TestDaF, iş Almancası, günlük konuşma, aile birleşimi vb.), **mevcut seviyelerine** (A1.1, B1.2 gibi alt seviyeler dahil), **bütçelerine** ve hatta **öğretmenin uzmanlık alanlarına** (örn: "Çocuklara Almanca öğretmede deneyimli", "Tıp Almancası uzmanı") göre çok detaylı filtreleme yapabilmesi sağlanmalı. Bu, doğru öğretmeni bulma sürecini kişiselleştirir.
*   **Öğretmen Profillerinde Daha Fazla İstatistik ve Bilgi:**
    *   Öğretmen kartlarına ve profillerine "Ortalama Yanıt Süresi", "Profil Görüntülenme Sayısı" (popülerliği gösterir), "Toplam Verilen Ders Saati" gibi metrikler eklenebilir.
    *   Öğretmenlerin mezun olduğu üniversite ve bölüm bilgisi (eğer varsa ve paylaşmak isterlerse) eklenebilir. Bu, bazı öğrenciler için güven artırıcı bir faktör olabilir.
*   **"Hızlı Ders Talebi Oluştur" Benzeri Bir Özellik:**
    *   Öğrencilerin beğendikleri bir öğretmene doğrudan profilinden hızlıca ders talebi gönderebileceği bir sistem (mevcut "Deneme Dersi Ayırt" veya "Mesaj Gönder" butonlarına ek olarak veya bunları birleştirerek) düşünülebilir.
*   **SEO Odaklı Açıklayıcı Metinler ve SSS:**
    *   Özel Ders Alanı'nın sayfa altındaki uzun SEO metinleri ve kapsamlı SSS bölümü örnek alınarak, AlmancaABC'nin de ilgili sayfalarında (özellikle `/ogretmenler` ([`src/app/ogretmenler/page.tsx`](src/app/ogretmenler/page.tsx:1)) ve `/sss` ([`src/app/sss/page.tsx`](src/app/sss/page.tsx:1))) Almanca öğrenimi, online derslerin faydaları, platformun kullanımı gibi konularda detaylı, anahtar kelime açısından zengin içerikler oluşturulmalı.
*   **Farklı Sıralama Kriterleri:**
    *   Öğretmen listeleme sayfasında "En Çok Yorum Alan", "En Yüksek Puanlı", "En Çok Ders Veren", "Fiyata Göre (Artan/Azalan)" gibi farklı sıralama seçenekleri sunulmalı.
*   **"Ders Talebi Oluştur" (Genel Havuz):**
    *   Öğrencilerin belirli bir öğretmeni seçmek yerine genel bir ders talebi oluşturup (örn: "Haftada 2 gün akşamları A2 seviyesinde konuşma pratiği yapmak istiyorum"), uygun öğretmenlerin bu talebe yanıt vermesini sağlayan bir sistem (Özel Ders Alanı'ndaki "Ders Talepleri" gibi) MVP sonrası için değerlendirilebilir.
*   **Öğretmen Onay ve Rozet Sistemi:**
    *   "Uzman Öğretmen" veya "Doğrulanmış Profil" gibi rozetlerle öğretmenlerin güvenilirliği ve kalitesi vurgulanabilir. (Clerk `publicMetadata` ile öğretmenlerin ek bilgilerini (uzmanlık, onay durumu vb.) tutabiliriz).
*   **Bütçe Dostu Seçenekler Vurgusu:**
    *   Farklı bütçelere uygun öğretmenlerin olduğu ve fiyat filtreleme seçeneğinin bulunduğu net bir şekilde belirtilmeli.

---
### 5. Armut (armut.com/online-almanca-ozel-ders)

**Genel Gözlemler:**

*   **Hizmet Odaklı Platform:** Armut, sadece özel ders değil, birçok farklı hizmet için teklif alma ve karşılaştırma platformudur. Online Almanca özel ders de bu hizmetlerden biri.
*   **Talep Oluşturma Sistemi:** Kullanıcılar ihtiyaçlarını belirterek talep oluşturuyor ve platformdaki öğretmenlerden fiyat teklifleri alıyorlar.
*   **Geniş Öğretmen Ağı:** "1.467 Online Almanca Özel Ders Öğretmeni hizmet vermeye hazır" gibi ifadelerle geniş bir ağa sahip olduklarını vurguluyorlar.
*   **Güven Unsurları:**
    *   Ortalama puan (4,9) ve gerçek/onaylı yorum sayısı (3.077) gibi metrikler öne çıkarılıyor.
    *   "Armut Garantisi" ile hizmetleri koruma altına aldıklarını belirtiyorlar.
*   **Fiyat Aralığı Belirtme:** "Her Bütçeye Uygun Almanca Kursları (Fiyat aralığı 500₺ - 2500₺)" gibi genel bir fiyat aralığı sunuyorlar, ancak asıl fiyatlandırma teklif usulüyle belirleniyor.

**Öne Çıkan Özellikler ve Teklifler:**

*   **Teklif Alma Süreci:**
    *   Kullanıcılar "BAŞLA" butonu ile ihtiyaçlarını (seviye, amaç, sıklık vb.) belirten bir form dolduruyor.
    *   Platform, bu talebi uygun öğretmenlere iletiyor.
    *   Öğretmenler fiyat tekliflerini sunuyor.
    *   Kullanıcılar gelen teklifleri karşılaştırıp, öğretmen profillerini (yorum, puan vb.) inceleyerek seçim yapıyor.
*   **Öğretmen Profilleri (Listelenen Örnekler Üzerinden):**
    *   Öğretmen fotoğrafı, ismi.
    *   Verdiği hizmet ve lokasyon (örn: Online Almanca Özel Ders · Ataşehir, İstanbul).
    *   Katılım tarihi.
    *   Öğretmenin kendi ağzından detaylı bir tanıtım yazısı (deneyimleri, uzmanlık alanları, ders işleyiş şekli vb.).
    *   Bazı profillerde "İlk ders ücretsiz" ibaresi görülebiliyor.
*   **SEO ve İçerik:**
    *   Sayfa başlığı net ve anahtar kelime odaklı: "4 Online Almanca Özel Ders Fiyat Teklifi Al, Karşılaştır."
    *   "Online Almanca Özel Ders için Neden Armut Tercih Edilmeli?" gibi kullanıcıyı ikna etmeye yönelik başlıklar ve maddeler.
    *   Popüler online Almanca özel ders bölgeleri ve bu bölgelerdeki fiyatlar gibi bilgilendirici linkler.
    *   Kapsamlı SSS bölümü (talep oluşturma ücretli mi, teklifler ne kadar zamanda gelir, Armut Garantisi nedir, yorumlar gerçek mi, fiyatlar ne kadar?).
*   **Armut Garantisi:** Platform üzerinden seçilen işlerin belirli bir tutara kadar koruma altında olması.

**AlmancaABC İçin Çıkarımlar ve İyileştirme Fikirleri:**

*   **"Neden AlmancaABC?" Bölümünü Güçlendirme:**
    *   Armut'un "Neden Armut Tercih Edilmeli?" bölümünden ilham alarak, AlmancaABC'nin sunduğu benzersiz değerleri (özenle seçilmiş ve doğrulanmış öğretmenler, kişiye özel ders programları, modern ve kullanıcı dostu platform, esnek ders saatleri, güvenli ödeme, belki bir "AlmancaABC Memnuniyet Politikası" vb.) net ve maddeler halinde vurgulayabiliriz.
*   **Öğretmen Profillerinde Detaylı Tanıtım Alanları:**
    *   Öğretmenlerin sadece yapılandırılmış bilgiler (deneyim, uzmanlık vb.) değil, aynı zamanda kendi öğretim felsefelerini, ders işleyiş tarzlarını ve öğrencilere nasıl yardımcı olabileceklerini anlatan daha serbest metin alanlarına sahip olmaları, öğrencilerin öğretmenle daha iyi bir bağ kurmasını sağlayabilir.
*   **Gerçek Öğrenci Yorumlarının Önemi:**
    *   Armut'un "gerçek ve onaylı yorum" vurgusu önemli. AlmancaABC'de de tamamlanan dersler sonrası öğrencilerin öğretmenleri değerlendirebileceği ve bu yorumların şeffaf bir şekilde yayınlanacağı bir sistem olmalı. Bu, yeni öğrencilerin karar verme sürecini kolaylaştırır.
*   **İstatistiksel Verilerle Güven Oluşturma:**
    *   Platformdaki toplam öğretmen sayısı, ortalama öğretmen puanı, tamamlanan ders sayısı gibi genel istatistikler (eğer yeterli veri birikirse) ana sayfada veya öğretmen listeleme sayfasında paylaşılabilir.
*   **"Armut Garantisi" Benzeri Bir Güvence Sistemi (MVP Sonrası):**
    *   Öğrencilerin platform üzerinden aldıkları derslerde belirli bir memnuniyet seviyesini garanti altına alan (örn: ilk dersten memnun kalmama durumunda farklı bir öğretmenle ücretsiz deneme veya kısmi iade gibi) bir güvence sistemi, platforma olan güveni artırabilir.
*   **Hedefe Yönelik Açıklamalar:**
    *   Öğretmen profillerinde, Armut'taki gibi, öğretmenin hangi sınavlara (Goethe, TELC, TestDaF, Aile Birleşimi vb.) veya hangi seviyelere (A1-C2) yönelik ders verdiğinin net bir şekilde belirtilmesi önemlidir.
*   **SEO İçin SSS ve Bilgilendirici İçerikler:**
    *   Armut'un SSS bölümü oldukça kapsamlı. AlmancaABC için de online Almanca dersleri, öğretmen seçimi, fiyatlandırma, teknik gereksinimler, platformun kullanımı gibi konularda detaylı bir SSS ([`src/app/sss/page.tsx`](src/app/sss/page.tsx:1)) sayfası oluşturulmalı ve sürekli güncellenmelidir.
    *   "Online Almanca Özel Ders Fiyatları Nasıl Belirlenir?", "En İyi Online Almanca Öğretmeni Nasıl Seçilir?" gibi blog yazıları veya rehber içerikler oluşturulabilir.

---
---
## Rakip Analizi Sonrası Kapsamlı Eylem Planı ve Öncelikler (20 Mayıs 2025)

Bu bölüm, Goethe Enstitüsü, Preply, Superprof, Özel Ders Alanı ve Armut sitelerinden elde edilen analizler ve mevcut proje hedefleri doğrultusunda AlmancaABC platformunu geliştirmek için atılacak adımları ve öncelikleri detaylandırmaktadır.

### I. Öğretmen Listeleme Sayfası ([`/ogretmenler`](src/app/ogretmenler/page.tsx:1)) ve Öğretmen Kartları ([`TeacherCard.tsx`](src/components/TeacherCard.tsx:1)) Geliştirmeleri

**A. Kullanıcı Deneyimi (UX) ve Arayüz (UI) İyileştirmeleri:**

1.  **"Sana Uygun Öğretmeni Bul" Sihirbazı (Wizard) (Preply, Superprof'tan İlhamla):**
    *   **Aksiyon:** Kullanıcının hedeflerini (sınav hazırlığı, konuşma pratiği vb.), mevcut seviyesini, bütçesini ve öğrenme stilini soran kısa, interaktif bir anket/sihirbaz geliştirilecek.
    *   **Amaç:** Kullanıcıya kişiselleştirilmiş öğretmen önerileri sunarak seçim sürecini kolaylaştırmak.
    *   **Durum:** ☐ Planlanacak.
2.  **Platformun Çalışma Prensibini Anlatan Bölüm (Superprof, Armut'tan İlhamla):**
    *   **Aksiyon:** `/ogretmenler` ([`src/app/ogretmenler/page.tsx`](src/app/ogretmenler/page.tsx:1)) sayfasına veya ana sayfaya, AlmancaABC'nin nasıl çalıştığını (öğretmen bulma, ders planlama, ödeme, derse katılma) basit adımlarla anlatan bir bölüm eklenecek.
    *   **Amaç:** Yeni kullanıcıların platformu kolayca anlamasını sağlamak.
    *   **Durum:** ☐ Planlanacak.
3.  **Görsel ve Tasarımsal İyileştirmeler:**
    *   **Aksiyon:** Mevcut `/ogretmenler` ([`src/app/ogretmenler/page.tsx`](src/app/ogretmenler/page.tsx:1)) sayfası ve [`TeacherCard.tsx`](src/components/TeacherCard.tsx:1) bileşeni, rakip analizlerindeki modern ve kullanıcı dostu tasarımlar (özellikle Preply ve Özel Ders Alanı) göz önünde bulundurularak yeniden değerlendirilecek.
    *   **Amaç:** Daha çekici, profesyonel ve sezgisel bir kullanıcı arayüzü sunmak.
    *   **Durum:** ⚙️ Mevcut "Öğretmenler Sayfası Yeniden Tasarımı" görevi altında devam ediyor.

**B. Filtreleme ([`FilterBar.tsx`](src/components/FilterBar.tsx:1)) ve Sıralama Yeteneklerinin Artırılması:**

1.  **Detaylı Seviye/Hedef Filtresi (Özel Ders Alanı, Goethe'den İlhamla):**
    *   **Aksiyon:** Mevcut seviye filtrelerine ek olarak "Aile Birleşimi Sınavı", "Goethe Sınavı (A1, B2 vb.)", "TestDaF Hazırlık", "İş Almancası", "Çocuklar için Almanca" gibi spesifik hedeflere yönelik filtre seçenekleri eklenecek.
    *   **Amaç:** Kullanıcıların çok özel ihtiyaçlarına göre öğretmen bulabilmesini sağlamak.
    *   **Durum:** ☐ Planlanacak.
2.  **Öğretmen Özelliklerine Göre Ek Filtreler (Preply, Özel Ders Alanı'ndan İlhamla):**
    *   **Aksiyon:** "Öğretmenin Doğduğu Ülke" (ana dilini konuşanları ayırt etmek için), "Deneyim Yılı" (örn: 1-3 yıl, 3-5 yıl, 5+ yıl), "Ortalama Yanıt Süresi" gibi filtreler eklenecek.
    *   **Amaç:** Daha rafine arama sonuçları sunmak.
    *   **Durum:** ☐ Planlanacak (Deneyim yılı ve yanıt süresi için `Teacher` tipine ve veritabanına ek alanlar gerekebilir).
3.  **Çeşitli Sıralama Seçenekleri (Özel Ders Alanı, Superprof'tan İlhamla):**
    *   **Aksiyon:** Mevcut sıralama seçeneklerine (`sortBy` in [`TeacherFilters`](src/types/filters.ts:1)) "En Çok Yorum Alan", "En Yüksek Puanlı", "En Çok Ders Veren", "En Yeni Katılanlar" gibi kriterler eklenecek.
    *   **Amaç:** Kullanıcılara farklı önceliklere göre öğretmenleri sıralama imkanı sunmak.
    *   **Durum:** ☐ Planlanacak.

**C. Öğretmen Kartı ([`TeacherCard.tsx`](src/components/TeacherCard.tsx:1)) İçeriğinin Zenginleştirilmesi:**

1.  **Video Tanıtım (Preply'den İlhamla):**
    *   **Aksiyon:** Öğretmenlerin kısa (1-2 dk) video tanıtımlarını yükleyebileceği ve bu videoların kartlarda/profillerde gösterileceği bir özellik geliştirilecek.
    *   **Amaç:** Öğrencilerin öğretmenle daha kişisel bir bağ kurmasını sağlamak.
    *   **Durum:** ☐ MVP sonrası değerlendirilecek.
2.  **Detaylı İstatistikler (Özel Ders Alanı, Preply'den İlhamla):**
    *   **Aksiyon:** Mevcut `activeStudents` ve `totalLessons` alanlarına ek olarak, "Ortalama Yanıt Süresi", "Profil Görüntülenme Sayısı" (popülerlik göstergesi), "Deneyim Yılı" gibi bilgiler kartlarda daha belirgin gösterilecek.
    *   **Amaç:** Öğretmen hakkında daha fazla nicel veri sunmak.
    *   **Durum:** ⚙️ Mevcut "Öğretmenler Sayfası Yeniden Tasarımı" görevi altında devam ediyor. (Yanıt süresi ve görüntülenme için ek altyapı gerekebilir).
3.  **Mezuniyet Bilgisi (Özel Ders Alanı'ndan İlhamla):**
    *   **Aksiyon:** Öğretmenlerin mezun olduğu üniversite ve bölüm bilgisini (isteğe bağlı olarak) profillerine ekleyebilmesi ve kartlarda gösterilmesi sağlanacak.
    *   **Amaç:** Bazı öğrenciler için güven artırıcı bir faktör sunmak.
    *   **Durum:** ☐ Planlanacak (`Teacher` tipine eklenebilir).
4.  **Öğretmenin Kendine Ait Sloganı/Başlığı (Superprof'tan İlhamla):**
    *   **Aksiyon:** Öğretmenlerin profillerine ve kartlarına dikkat çekici bir slogan veya kısa başlık ekleyebileceği bir alan oluşturulacak.
    *   **Amaç:** Öğretmenlerin kendilerini daha iyi ifade etmelerini sağlamak.
    *   **Durum:** ☐ Planlanacak.
5.  **Anında Müsaitlik / Takvim Entegrasyonu (Preply'den İlhamla):**
    *   **Aksiyon:** Öğretmen kartlarında, öğretmenin yakın zamandaki genel müsaitliğini (örn: "Bu hafta müsait", "Akşamları müsait") gösteren bir işaret veya doğrudan takvimine giden bir link/buton eklenecek. FullCalendar entegrasyonu ([`TeacherAvailabilityCalendar.tsx`](src/components/calendar/TeacherAvailabilityCalendar.tsx:1)) bu amaçla kullanılabilir.
    *   **Amaç:** Öğrencilerin hızlıca müsait öğretmen bulmasını kolaylaştırmak.
    *   **Durum:** ⚙️ Mevcut "Öğretmen Müsaitlik Ayarları" (Madde 98) ve "Ders Seçimi ve Rezervasyon" (Madde 116) görevleriyle entegre edilecek.

### II. SEO ve İçerik Stratejisi Geliştirmeleri

**A. Yapılandırılmış Veri (Schema.org) Entegrasyonunun Detaylandırılması (Tüm Rakiplerden İlhamla):**

1.  **Öğretmen Listeleme Sayfası ([`/ogretmenler`](src/app/ogretmenler/page.tsx:1)):**
    *   **Aksiyon:** Sayfa `ItemList` şeması ile işaretlenecek. Her bir öğretmen kartı ([`TeacherCard.tsx`](src/components/TeacherCard.tsx:1)) `ListItem` olarak işaretlenecek ve `ListItem` içinde öğretmenin profil sayfasına `url` itemprop'i ile link verilecek.
    *   **Amaç:** Arama motorlarının öğretmen listesini daha iyi anlamasını sağlamak.
    *   **Durum:** ☐ Planlanacak (Mevcut "Mevcut SEO Öğelerini Gözden Geçirme" (Madde 633) ile birleştirilebilir).
2.  **Öğretmen Kartları ([`TeacherCard.tsx`](src/components/TeacherCard.tsx:1)) ve Profil Sayfaları ([`/ogretmenler/[id]`](src/app/ogretmenler/[id]/page.tsx:1)):**
    *   **Aksiyon:** `Person` (veya Schema.org'un `Teacher` alt tipi varsa o, yoksa `EducationalOccupationalCredential` ile genişletilmiş `Person`) şeması kullanılacak. Önemli `itemprop`'lar: `name`, `image` (öğretmen fotoğrafı), `description` (kısa bio), `url` (profil sayfası linki), `knowsAbout` (uzmanlık alanları), `knowsLanguage` (konuştuğu diller ve seviyeleri), `priceRange` (veya `makesOffer` ile `Offer` şeması), `aggregateRating`, `review`.
    *   **Amaç:** Öğretmen bilgilerinin arama motorları tarafından zengin snippet olarak gösterilme olasılığını artırmak.
    *   **Durum:** ☐ Planlanacak (Mevcut "Mevcut SEO Öğelerini Gözden Geçirme" (Madde 633) ile birleştirilebilir).
3.  **Öğrenci Yorumları ve Değerlendirmeleri:**
    *   **Aksiyon:** Öğrenci yorumları için `Review` ve genel öğretmen puanı için `AggregateRating` şemaları öğretmen profillerine ve kartlarına entegre edilecek.
    *   **Amaç:** Yorum ve puanların arama sonuçlarında görünürlüğünü artırmak.
    *   **Durum:** ☐ Planlanacak.
4.  **SSS Sayfası ([`/sss`](src/app/sss/page.tsx:1)):**
    *   **Aksiyon:** Mevcut `FAQPage` şeması zenginleştirilecek, daha fazla soru ve cevap eklenecek.
    *   **Amaç:** SSS bölümünün arama motorları tarafından daha iyi anlaşılmasını sağlamak.
    *   **Durum:** ⚙️ Mevcut "Kapsamlı SSS Bölümleri" (Madde 616) ve "SEO İçin SSS ve Bilgilendirici İçerikler" (Madde 937) görevleriyle birleştirilecek.
5.  **Meta Etiketler (Open Graph, Twitter Cards):**
    *   **Aksiyon:** Tüm önemli sayfalar (ana sayfa, öğretmen listeleme, öğretmen profilleri, kurs sayfaları, blog yazıları) için dinamik ve açıklayıcı `og:*` ve `twitter:*` meta etiketleri oluşturulacak. [`next-sitemap.config.js`](next-sitemap.config.js:1) ve sayfa `head.tsx` dosyaları (örn: [`src/app/ogretmenler/head.tsx`](src/app/ogretmenler/head.tsx:1)) bu amaçla kullanılabilir.
    *   **Amaç:** Sosyal medya paylaşımlarında ve arama sonuçlarında daha iyi görünüm sağlamak.
    *   **Durum:** ☐ Planlanacak.

**B. Sayfa Başlıkları, Meta Açıklamaları ve H Etiketlerinin Optimizasyonu:**

1.  **Aksiyon:** Tüm sayfalardaki `<title>`, `meta description` ve başlık hiyerarşisi (`<h1>`-`<h6>`) anahtar kelime araştırması yapılarak ve rakip analizlerindeki başarılı örnekler incelenerek optimize edilecek.
2.  **Amaç:** Arama motoru sıralamalarını iyileştirmek ve tıklama oranlarını artırmak.
3.  **Durum:** ⚙️ Mevcut "Mevcut SEO Öğelerini Gözden Geçirme" (Madde 633) görevi altında devam ediyor.

**C. Bilgilendirici İçerik ve SSS Bölümlerinin Genişletilmesi (Preply, Superprof, Armut, Özel Ders Alanı'ndan İlhamla):**

1.  **Aksiyon:** Öğretmen listeleme sayfasının ([`/ogretmenler`](src/app/ogretmenler/page.tsx:1)) altına veya ayrı bir blog/rehber bölümüne "Online Almanca Öğrenmenin Faydaları", "Doğru Almanca Öğretmeni Nasıl Seçilir?", "Almanca Seviyeleri ve Anlamları", "Goethe/TestDaF Sınavlarına Hazırlık İpuçları" gibi konularda kapsamlı, SEO odaklı içerikler eklenecek.
2.  **Amaç:** Organik trafik çekmek, kullanıcıları bilgilendirmek ve platformun otoritesini artırmak.
3.  **Durum:** ☐ Planlanacak (Mevcut "SEO Odaklı Ek İçerikler" (Madde 617) göreviyle birleştirilebilir).

### III. Güven ve İkna Edicilik Unsurlarının Artırılması

**A. Öğrenci Yorumları ve Değerlendirme Sisteminin İyileştirilmesi (Tüm Rakiplerden İlhamla):**

1.  **Aksiyon:** Tamamlanan dersler sonrası öğrencilerin öğretmenleri detaylı (örn: iletişim, ders materyali, pedagojik yaklaşım gibi kriterlerle) değerlendirebileceği ve yorum bırakabileceği bir sistem geliştirilecek. Yorumların "gerçek ve onaylı" olduğu vurgulanacak.
2.  **Amaç:** Şeffaflığı artırmak ve potansiyel öğrencilere karar verme sürecinde yardımcı olmak.
3.  **Durum:** ☐ Planlanacak.

**B. Öğretmen Doğrulama ve Rozet Sisteminin Geliştirilmesi (Superprof, Özel Ders Alanı, Preply'den İlhamla):**

1.  **Aksiyon:** Öğretmenlerin kimlik, diploma, sertifika gibi belgelerini yükleyebileceği ve AlmancaABC ekibi tarafından onaylandıktan sonra profillerinde "Doğrulanmış Öğretmen", "Uzman Eğitmen", "Süper Öğretmen" (belirli başarı kriterlerini karşılayanlar için) gibi rozetler alabileceği bir sistem geliştirilecek.
2.  **Amaç:** Öğretmen kalitesini ve platform güvenilirliğini artırmak.
3.  **Durum:** ☐ Planlanacak (Clerk `publicMetadata` kullanılabilir).

**C. "Neden AlmancaABC?" Vurgusunun Güçlendirilmesi ve Garanti Politikaları (Armut, Preply'den İlhamla):**

1.  **Aksiyon:** Ana sayfada veya `/hakkimizda` ([`src/app/hakkimizda/page.tsx`](src/app/hakkimizda/page.tsx:1)) sayfasında, AlmancaABC'nin benzersiz değer önerilerini (özenle seçilmiş öğretmenler, kişiye özel programlar, modern arayüz, esneklik, güvenli ödeme vb.) net bir şekilde anlatan bir bölüm oluşturulacak.
2.  **Aksiyon:** "AlmancaABC Memnuniyet Garantisi" gibi (örn: ilk dersten memnun kalmama durumunda farklı öğretmenle ücretsiz deneme veya kısmi iade) bir politika geliştirilip duyurulacak.
3.  **Amaç:** Kullanıcıların platforma olan güvenini ve bağlılığını artırmak.
4.  **Durum:** ☐ Planlanacak.

### IV. Ek Özellikler ve Fonksiyonel Geliştirmeler (MVP Sonrası Değerlendirilecekler)

**A. Online Seviye Tespit Sınavı (Goethe'den İlhamla):**

1.  **Aksiyon:** Kullanıcıların Almanca seviyelerini hızlıca belirleyip uygun öğretmen/ders paketi önerileri alabilecekleri interaktif bir online seviye tespit testi geliştirilecek.
2.  **Amaç:** Kullanıcıların doğru başlangıç noktasını bulmalarına yardımcı olmak.
3.  **Durum:** ☐ MVP sonrası planlanacak.

**B. Ders Paketleri ve "İlk Ders Ücretsiz/İndirimli" Seçenekleri (Goethe, Superprof, Preply'den İlhamla):**

1.  **Aksiyon:** Farklı ihtiyaçlara (sınav hazırlığı, konuşma pratiği) ve sürelere (10 ders, 20 ders vb.) yönelik çeşitli ders paketleri oluşturulacak. Öğretmenlere isteğe bağlı olarak "İlk Ders Ücretsiz" veya indirimli bir "Tanışma Dersi" sunma seçeneği verilecek.
2.  **Amaç:** Öğrencilere esneklik sunmak ve yeni kullanıcıların platformu denemesini teşvik etmek.
3.  **Durum:** ☐ MVP sonrası planlanacak.

**C. Öğretmenler İçin Bilgilendirme Sayfası (Özel Ders Alanı'ndan İlhamla):**

1.  **Aksiyon:** Platforma nasıl öğretmen olarak katılabileceklerini, beklentileri, kazanç modellerini vb. açıklayan detaylı bir sayfa oluşturulacak.
2.  **Amaç:** Kaliteli öğretmenlerin platforma katılımını teşvik etmek.
3.  **Durum:** ☐ MVP sonrası planlanacak.

Bu eylem planı, rakip analizlerinden elde edilen bilgilerle mevcut geliştirme süreçlerimizi birleştirerek AlmancaABC platformunu daha rekabetçi, kullanıcı dostu ve başarılı bir hale getirmeyi amaçlamaktadır. Önceliklendirme, MVP (Minimum Viable Product - Minimum Uygulanabilir Ürün) hedefleri ve kaynaklara göre yapılacaktır.