"use client"

import React from "react"
import { motion } from "framer-motion"
// import { useLanguage } from "@/lib/i18n/LanguageContext"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Clock, Users, BookOpen, Star, ArrowRight } from "lucide-react" // ArrowRight eklendi
import Image from "next/image"
import Link from "next/link"
import CourseSchema from "./CourseSchema"

// Örnek kurs verileri
const courses = [
  {
    id: 1,
    title: "A1 Seviye Almanca Kursu",
    image: "https://images.unsplash.com/photo-1546410531-bb4caa6b424d?q=80&w=500&auto=format&fit=crop",
    level: "Başlangıç",
    duration: "8 Hafta",
    students: 245,
    lessons: 32,
    rating: 4.8,
    price: 1200,
    description: "Almanca öğrenmeye sıfırdan başlayın. <PERSON><PERSON> kelimeler, günlük konuşmalar ve basit gramer yapıları.",
  },
  {
    id: 2,
    title: "<PERSON>ş Almancası Kursu",
    image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?q=80&w=500&auto=format&fit=crop",
    level: "B1-B2",
    duration: "6 Hafta",
    students: 178,
    lessons: 24,
    rating: 4.9,
    price: 1500,
    description: "İş hayatında kullanılan Almanca terimler, toplantı ve sunum teknikleri, iş yazışmaları.",
  },
  {
    id: 3,
    title: "Almanca Konuşma Pratiği",
    image: "https://images.unsplash.com/photo-1543269865-cbf427effbad?q=80&w=500&auto=format&fit=crop",
    level: "A2-C1",
    duration: "4 Hafta",
    students: 312,
    lessons: 16,
    rating: 4.7,
    price: 900,
    description: "Günlük konuşma, akıcılık kazanma, telaffuz geliştirme ve özgüvenli konuşma teknikleri.",
  },
  {
    id: 4,
    title: "TestDaF Sınav Hazırlık",
    image: "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?q=80&w=500&auto=format&fit=crop",
    level: "B2-C1",
    duration: "10 Hafta",
    students: 156,
    lessons: 40,
    rating: 4.9,
    price: 1800,
    description: "TestDaF sınavına hazırlık, örnek sorular, sınav stratejileri ve pratik çalışmalar.",
  },
]

export default function CourseList() {
  // const { t } = useLanguage()

  return (
    (<section className="py-6 sm:py-8 bg-white">
      <CourseSchema courses={courses} />
      <div className="container mx-auto px-4">
        {/* Başlık ve "Tümünü Gör" Butonu */}
        <div className="flex justify-between items-center mb-4">
          <motion.h2
            initial={{ opacity: 0, y: -10 }} // Adjusted animation
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl font-bold" // Removed mb-4
          >
            Popüler Almanca Kursları
          </motion.h2>
          <Button asChild variant="outline">
            <Link href="/kurslar" passHref>
              Tüm Kurslar
              <ArrowRight className="ml-2 h-4 w-4" /> {/* Icon eklendi */}
            </Link>
          </Button>
        </div>
        {/* Açıklama paragrafı kaldırıldı (veya yorum satırı yapıldı)
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-gray-600 max-w-2xl mx-auto mb-12" // Added mb-12 if kept
          >
            Seviyenize ve hedeflerinize uygun kurslarımızla Almanca öğrenin. Canlı dersler, interaktif içerikler ve
            sertifika imkanı.
          </motion.p>
        */}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {courses.map((course, index) => (
            <motion.div
              key={course.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow overflow-hidden">
                <div className="relative h-48 w-full">
                  <Image
                    src={course.image}
                    alt={course.title}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    className="object-cover"
                  />
                  <div className="absolute top-2 right-2 bg-primary text-white text-xs px-2 py-1 rounded">
                    {course.level}
                  </div>
                </div>
                <CardContent className="p-3">
                  <h3 className="font-semibold text-lg mb-2">{course.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{course.description}</p>
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    <div className="flex items-center text-sm">
                      <Clock className="h-4 w-4 mr-1 text-gray-500" />
                      <span>{course.duration}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <BookOpen className="h-4 w-4 mr-1 text-gray-500" />
                      <span>{course.lessons} Ders</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Users className="h-4 w-4 mr-1 text-gray-500" />
                      <span>{course.students} Öğrenci</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Star className="h-4 w-4 mr-1 text-yellow-500 fill-yellow-500" />
                      <span>{course.rating}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-bold text-primary">{course.price} ₺</span>
                    <Button asChild size="sm">
                      <Link href={`/kurslar/${course.id}`}>Kursa Katıl</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
        {/* Eski buton kaldırıldı */}
      </div>
    </section>)
  );
}