// src/hooks/useMediaQuery.ts
"use client";

import { useState, useEffect } from 'react';

/**
 * Medya sorgusu için özel hook
 * @param query - CSS medya sorgusu (örn. '(max-width: 768px)')
 * @returns Medya sorgusunun eşleşip eşleşmediği
 */
export function useMediaQuery(query: string): boolean {
  // Varsayılan olarak false döndür (SSR için)
  const [matches, setMatches] = useState(false);
  
  // Yalnızca istemci tarafında çalıştır
  useEffect(() => {
    // Tarayıcı medya sorgusu desteğini kontrol et
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia(query);
      
      // İlk eşleşme durumunu ayarla
      setMatches(mediaQuery.matches);
      
      // Medya sorgusu değişikliklerini dinle
      const listener = (event: MediaQueryListEvent) => {
        setMatches(event.matches);
      };
      
      // <PERSON><PERSON> dinleyicisini ekle
      mediaQuery.addEventListener('change', listener);
      
      // Temizleme fonksiyonu
      return () => {
        mediaQuery.removeEventListener('change', listener);
      };
    }
    
    // SSR veya eski tarayıcılar için varsayılan değeri döndür
    return undefined;
  }, [query]);
  
  return matches;
}
