// src/app/sign-up/page.tsx
"use client";

import { signUpWithEmailAndPassword } from "@/lib/actions/auth.actions";
import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";

export default function SignUpPage() {
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setMessage(null);
    const formData = new FormData(event.currentTarget);

    startTransition(async () => {
      try {
        const result = await signUpWithEmailAndPassword(formData);
        if (result.error) {
          setError(result.error);
        } else if (result.success) {
          setMessage(result.message || "<PERSON><PERSON>t başarılı!");
          // E-posta onayı gerekiyorsa kullanıcıyı bilgilendir,
          // session hemen oluşuyorsa (result.session var) giriş sayfasına veya dashboard'a yönlendir.
          if (result.session) {
            // Belki direkt dashboard'a yönlendirmek daha iyi olur
            // router.push('/(dashboard)');
            router.push('/sign-in?message=signup_successful_please_signin');
          } else if (result.message?.includes("onaylayın")) {
            // E-posta onayı mesajı zaten message state'inde gösteriliyor.
            // Kullanıcıyı burada bırakabiliriz veya giriş sayfasına yönlendirebiliriz.
             router.push('/sign-in?message=signup_successful_check_email');
          }
        }
      } catch (e: any) {
        setError(e.message || "Bir hata oluştu.");
      }
    });
  };

  return (
    <div style={{ maxWidth: '400px', margin: '50px auto', padding: '20px', border: '1px solid #ccc', borderRadius: '8px' }}>
      <h1>Kayıt Ol</h1>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="fullName">Ad Soyad (İsteğe Bağlı):</label>
          <input type="text" id="fullName" name="fullName" style={{ width: '100%', padding: '8px', boxSizing: 'border-box' }} />
        </div>
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="email">E-posta:</label>
          <input type="email" id="email" name="email" required style={{ width: '100%', padding: '8px', boxSizing: 'border-box' }} />
        </div>
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="password">Şifre:</label>
          <input type="password" id="password" name="password" required minLength={6} style={{ width: '100%', padding: '8px', boxSizing: 'border-box' }} />
        </div>
        {error && <p style={{ color: 'red' }}>{error}</p>}
        {message && <p style={{ color: 'green' }}>{message}</p>}
        <button type="submit" disabled={isPending} style={{ padding: '10px 20px', cursor: 'pointer' }}>
          {isPending ? "Kaydediliyor..." : "Kayıt Ol"}
        </button>
      </form>
      <p style={{marginTop: '10px'}}>
        Zaten bir hesabınız var mı? <a href="/sign-in">Giriş Yapın</a>
      </p>
    </div>
  );
}
