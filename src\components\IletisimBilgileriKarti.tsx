'use client';

import React from 'react';
import { Phone, MessageCircle, Mail, MapPin } from 'lucide-react';

export const IletisimBilgileriKarti = () => {
  return (
    <div className="lg:col-span-2 bg-slate-700 dark:bg-slate-600 text-white p-6 md:p-8 relative h-full"> {/* h-full eklendi */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-slate-600 dark:bg-slate-500 rounded-bl-full opacity-40"></div>
      <div className="absolute bottom-0 left-0 w-14 h-14 bg-slate-600 dark:bg-slate-500 rounded-tr-full opacity-40"></div>
      
      <h2 className="text-2xl font-bold mb-6 relative z-10">İletişim Bilgilerimiz</h2>
      <p className="mb-6 text-sm text-slate-200 dark:text-slate-300 relative z-10">
        Aşağıdaki formu doldurabilir veya doğrudan iletişim kanallarımızı kullanarak bize ulaşabilirsiniz. Size en kısa sürede yanıt vereceğiz.
      </p>
      
      <div className="space-y-6 relative z-10">
        <a href="tel:+491784441011" className="flex items-center group">
          <div className="flex-shrink-0 bg-slate-600 dark:bg-slate-500 p-2 rounded-full group-hover:bg-slate-500 dark:group-hover:bg-slate-400 transition-colors">
            <Phone className="h-5 w-5 text-white" />
          </div>
          <div className="ml-3">
            <h3 className="text-base font-semibold text-white group-hover:text-slate-100 transition-colors">Telefon</h3>
            <p className="mt-0.5 text-sm text-slate-200 dark:text-slate-300 group-hover:text-white transition-colors">+49 ************</p>
            <p className="text-xs text-slate-300 dark:text-slate-400 mt-0.5">Hızlı iletişim için WhatsApp kullanabilirsiniz.</p>
          </div>
        </a>
        <a href="https://wa.me/491784441011" target="_blank" rel="noopener noreferrer" className="flex items-center group">
          <div className="flex-shrink-0 bg-slate-600 dark:bg-slate-500 p-2 rounded-full group-hover:bg-slate-500 dark:group-hover:bg-slate-400 transition-colors">
            <MessageCircle className="h-5 w-5 text-white" />
          </div>
          <div className="ml-3">
            <h3 className="text-base font-semibold text-white group-hover:text-slate-100 transition-colors">WhatsApp</h3>
            <p className="mt-0.5 text-sm text-slate-200 dark:text-slate-300 group-hover:text-white transition-colors">WhatsApp üzerinden ulaşın</p>
            <p className="text-xs text-slate-300 dark:text-slate-400 mt-0.5">Mesaj yoluyla hızlı destek.</p>
          </div>
        </a>
        <a href="mailto:<EMAIL>" className="flex items-center group">
          <div className="flex-shrink-0 bg-slate-600 dark:bg-slate-500 p-2 rounded-full group-hover:bg-slate-500 dark:group-hover:bg-slate-400 transition-colors">
            <Mail className="h-5 w-5 text-white" />
          </div>
          <div className="ml-3">
            <h3 className="text-base font-semibold text-white group-hover:text-slate-100 transition-colors">E-posta</h3>
            <p className="mt-0.5 text-sm text-slate-200 dark:text-slate-300 group-hover:text-white transition-colors"><EMAIL></p>
             <p className="text-xs text-slate-300 dark:text-slate-400 mt-0.5">Genel sorular ve resmi yazışmalar için.</p>
          </div>
        </a>
        
        <div className="flex items-center group"> {/* Adres tıklanabilir değil, bu yüzden a etiketi yok */}
          <div className="flex-shrink-0 bg-slate-600 dark:bg-slate-500 p-2 rounded-full">
            <MapPin className="h-5 w-5 text-white" />
          </div>
          <div className="ml-3">
            <h3 className="text-base font-semibold text-white">Adres</h3>
            <p className="mt-0.5 text-sm text-slate-200 dark:text-slate-300">Danziger Str. 2<br />63739 Aschaffenburg<br />Almanya</p>
            <p className="text-xs text-slate-300 dark:text-slate-400 mt-0.5">(Ziyaretçi kabul edilmemektedir)</p>
          </div>
        </div>
      </div>
    </div>
  );
};