import * as z from 'zod';

// İletişim Formu Şeması
export const contactFormSchema = z.object({
  name: z.string().min(2, { message: 'Adınız en az 2 karakter olmalıdır.' }).trim(),
  email: z.string().email({ message: 'Lütfen geçerli bir e-posta adresi girin.' }).trim(),
  phone: z.string().optional().or(z.literal('')), // İsteğe bağlı alan
  subject: z.string().min(5, { message: 'Konu en az 5 karakter olmalıdır.' }).trim(),
  message: z.string().min(10, { message: 'Mesajınız en az 10 karakter olmalıdır.' }).trim(),
  privacyPolicyAccepted: z.boolean().refine(val => val === true, {
    message: 'Devam etmek için gizlilik politikasını kabul etmelisiniz.',
  }),
});

export type ContactFormValues = z.infer<typeof contactFormSchema>;

// Diğer form şemaları buraya eklenebilir

// Öğretmen Profili Rezervasyon/Bilgi Formu Şeması
export const teacherBookingFormSchema = z.object({
  // Mevcut alanlar
  name: z.string().min(2, { message: 'Adınız en az 2 karakter olmalıdır.' }).trim(),
  email: z.string().email({ message: 'Lütfen geçerli bir e-posta adresi girin.' }).trim(),
  phone: z.string().optional().or(z.literal('')),
  message: z.string().min(10, { message: 'Mesajınız en az 10 karakter olmalıdır.' }).trim(), // Bu 'notes' olarak kullanılabilir veya kaldırılabilir
  courseTitle: z.string().optional(),

  // Yeni eklenen zorunlu alanlar
  studentId: z.string().min(1, { message: "Öğrenci ID'si gereklidir." }),
  teacherId: z.string().min(1, { message: "Öğretmen ID'si gereklidir." }),
  availabilitySlotId: z.string().min(1, { message: "Müsaitlik slot ID'si gereklidir." }),
  lessonTime: z.string().min(1, { message: "Ders zamanı gereklidir." }), // Action içinde Date'e çevrilecek
  durationMinutes: z.preprocess(
    (val) => parseInt(String(val), 10),
    z.number().positive({ message: "Ders süresi pozitif bir sayı olmalıdır." })
  ),
  pricePaid: z.string().min(1, { message: "Ücret bilgisi gereklidir." }), // Action içinde Decimal'e çevrilecek

  // Opsiyonel yeni alan
  notes: z.string().optional(),
});

export type TeacherBookingFormValues = z.infer<typeof teacherBookingFormSchema>;

// Örnek:
// export const teacherProfileSchema = z.object({
//   firstName: z.string().min(2, { message: "İsim en az 2 karakter olmalıdır." }),
//   lastName: z.string().min(2, { message: "Soyisim en az 2 karakter olmalıdır." }),
//   // ... diğer alanlar
// });
// export type TeacherProfileFormValues = z.infer<typeof teacherProfileSchema>;