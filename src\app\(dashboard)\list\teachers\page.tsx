import React from 'react';
import Pagination from '@/components/Pagination';
import TableSearch from '@/components/TableSearch';
import Table from '@/components/Table';
import { getAllApprovedTeachers, countAllApprovedTeachers } from '@/lib/actions/teacher.actions'; // countApprovedTeachers -> countAllApprovedTeachers
import { Teacher } from '@prisma/client';
import Link from 'next/link';
import Image from 'next/image';
import { ITEM_PER_PAGE } from '@/lib/constants';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge'; // Badge -> badge
// import { auth } from '@clerk/nextjs/server'; // Clerk kaldırıldı
import { redirect } from 'next/navigation';
import { Metadata } from 'next';

// Metadata tanımı
export const metadata: Metadata = {
  title: "Öğretmenler | AlmancaABC Admin",
  description: "AlmancaABC platformundaki öğretmenlerin listesi",
};

// Sayfa props tipi
interface TeachersPageProps {
  searchParams: {
    page?: string;
    search?: string;
  };
}

const TeachersPage = async ({ searchParams }: TeachersPageProps) => {
  // Admin yetkisi kontrolü
  // const authResult = await auth(); // Clerk kaldırıldı
  // const userId = authResult.userId; // Clerk kaldırıldı
  let userId: string | null = null;

  if (process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
    // console.warn("DEV_SKIP_AUTH_MIDDLEWARE: TeachersPage (admin view) - Using default 'test-admin-id'.");
    userId = "test-admin-id"; // Test için varsayılan admin ID'si
  }

  if (!userId) {
    // TODO: Clerk kaldırıldığı için "/sign-in" yerine uygun bir genel giriş/hata sayfasına yönlendirilmeli
    redirect("/"); // Şimdilik ana sayfaya yönlendir
  }
  
  // Sayfalama ve arama parametreleri
  const page = parseInt(searchParams?.page || '1', 10);
  const search = searchParams?.search || '';

  // Öğretmen verilerini ve toplam sayıyı çek
  const teachersData = await getAllApprovedTeachers({ searchTerm: search, page, limit: ITEM_PER_PAGE });
  const count = await countAllApprovedTeachers(search); // { searchTerm: search } -> search

  // Tablo sütun tanımları
  const columns = [
    { header: 'Öğretmen', accessor: 'info', className: '' },
    { header: 'Ülke/Şehir', accessor: 'location', className: 'hidden md:table-cell' },
    { header: 'Uzmanlık Alanları', accessor: 'specializations', className: 'hidden lg:table-cell' },
    { header: 'Ders Sayısı', accessor: 'lessonCount', className: 'hidden md:table-cell' },
    { header: 'Ücret (€/saat)', accessor: 'hourly_rate', className: 'hidden md:table-cell' },
    { header: 'İşlemler', accessor: 'actions', className: '' },
  ];

  // Her bir satırı render edecek fonksiyon
  // getAllApprovedTeachers'dan dönen tipe göre renderRow'daki teacher tipini güncelleyelim.
  // Bu tip, action dosyasındaki map işleminden sonraki halidir.
  type MappedTeacher = ReturnType<typeof getAllApprovedTeachers> extends Promise<infer R> ? (R extends Array<infer T> ? T : never) : never;

  const renderRow = (teacher: MappedTeacher) => (
    <tr key={teacher.id} className="border-b border-gray-200 hover:bg-gray-100">
      <td className="py-3 px-4">
        <div className="flex items-center gap-2">
          <div className="relative w-10 h-10 rounded-full overflow-hidden bg-gray-200">
            <Image
              src={teacher.profile_image_url || '/placeholder-avatar.png'}
              alt={`${teacher.firstName} ${teacher.lastName}`}
              fill
              className="object-cover"
              sizes="40px"
            />
          </div>
          <div className="flex flex-col">
            <span className="font-medium">{`${teacher.firstName || ''} ${teacher.lastName || ''}`.trim()}</span>
            <span className="text-xs text-gray-500">{teacher.title || 'Almanca Öğretmeni'}</span>
          </div>
        </div>
      </td>
      <td className="py-3 px-4 hidden md:table-cell">
        {teacher.country && teacher.city 
          ? `${teacher.country}, ${teacher.city}` 
          : teacher.country || teacher.city || '-'}
      </td>
      <td className="py-3 px-4 hidden lg:table-cell">
        <div className="flex flex-wrap gap-1">
          {Array.isArray(teacher.specializations) && teacher.specializations.length > 0 ? (
            teacher.specializations.slice(0, 3).map((spec, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {spec}
              </Badge>
            ))
          ) : (
            <span className="text-gray-500">-</span>
          )}
          {Array.isArray(teacher.specializations) && teacher.specializations.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{teacher.specializations.length - 3}
            </Badge>
          )}
        </div>
      </td>
      <td className="py-3 px-4 hidden md:table-cell">
        <Badge variant="secondary" className="font-normal">
          {teacher.completedLessons ?? 0}
        </Badge>
      </td>
      <td className="py-3 px-4 hidden md:table-cell">
        {teacher.hourly_rate ? `${teacher.hourly_rate}` : '-'}
      </td>
      <td className="py-3 px-4">
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline" asChild>
            <Link href={`/dashboard/list/teachers/${teacher.id}`}>
              Detaylar
            </Link>
          </Button>
          <Button size="sm" variant="outline" asChild>
            <Link href={`/teachers/${teacher.id}`} target="_blank">
              Profil
            </Link>
          </Button>
        </div>
      </td>
    </tr>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Öğretmenler</h1>
        <Button variant="outline" asChild>
          <Link href="/dashboard/admin">
            Admin Paneline Dön
          </Link>
        </Button>
      </div>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-4 border-b flex justify-between items-center">
          <TableSearch />
          <div className="flex gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/list/teacher-applications">
                Başvurular
              </Link>
            </Button>
            <Button size="sm" asChild>
              <Link href="/teachers/apply">
                Yeni Öğretmen
              </Link>
            </Button>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <Table columns={columns} data={teachersData} renderRow={renderRow} />
        </div>
        
        <div className="p-4 border-t">
          <Pagination count={count} page={page} />
        </div>
      </div>
    </div>
  );
};

export default TeachersPage;