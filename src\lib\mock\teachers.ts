// src/lib/mock/teachers.ts
// Mock öğretmen verileri

import { Teacher } from "@prisma/client";

// Mock öğretmen verisi oluşturan fonksiyon
export function getMockTeacher(teacherId: number = 1): Teacher {
  const mockTeacher = {
    id: `mock-teacher-${teacherId}`,
    user_id: `user-${teacherId}`,
    name: `Örnek Öğretmen ${teacherId}`,
    email: `teacher${teacherId}@example.com`,
    profile_image_url: '/images/teachers/teacher1.jpg',
    title: 'Almanca Öğretmeni',
    bio: 'Almanca öğretmeniyim ve 10 yılı aşkın deneyime sahibim. Öğrencilerime kişiselleştirilmiş eğitim sunuyorum.',
    country: 'Türkiye',
    city: 'İstanbul',
    languages: ['Türkçe', 'Almanca', 'İngilizce'],
    specializations: ['Genel Almanca', '<PERSON>ş Almancası', 'Sınav Hazırlık'],
    teaching_levels: ['A1', 'A2', 'B1', 'B2', 'C1'],
    hourly_rate: 250,
    availability: ['Hafta içi akşamları', 'Hafta sonu'],
    education: [
      { institution: 'İstanbul Üniversitesi', degree: 'Alman Dili ve Edebiyatı', year: '2010' }
    ],
    experience: [
      { company: 'Özel Lise', role: 'Almanca Öğretmeni', duration: '2010-2015' },
      { company: 'Dil Okulu', role: 'Kıdemli Eğitmen', duration: '2015-Günümüz' }
    ],
    certificates: [
      { name: 'TestDaF', issuer: 'TestDaF Enstitüsü', year: '2008' },
      { name: 'Pedagojik Formasyon', issuer: 'MEB', year: '2009' }
    ],
    verified: true,
    is_approved: true,
    stats: {
      reviewCount: 24,
      avgRating: 4.8
    },
    lessonPackages: [
      { id: 1, name: 'Tek Ders', lessons: 1, price: 250, discount: 0, perLesson: 250 },
      { id: 2, name: '4 Ders Paketi', lessons: 4, price: 900, discount: 10, perLesson: 225 },
      { id: 3, name: '8 Ders Paketi', lessons: 8, price: 1600, discount: 20, perLesson: 200 }
    ],
    schedule: {
      monday: ['18:00', '19:00', '20:00'],
      tuesday: ['18:00', '19:00', '20:00'],
      wednesday: ['18:00', '19:00', '20:00'],
      thursday: ['18:00', '19:00', '20:00'],
      friday: ['18:00', '19:00', '20:00'],
      saturday: ['10:00', '11:00', '12:00', '13:00', '14:00'],
      sunday: ['10:00', '11:00', '12:00', '13:00', '14:00']
    },
    created_at: new Date(),
    updated_at: new Date()
  };

  return mockTeacher as unknown as Teacher;
}

// Önerilen öğretmenler için mock veri
export function getMockRecommendedTeachers(count: number = 3, excludeId?: string): Teacher[] {
  const teachers: Teacher[] = [];
  
  for (let i = 1; i <= count; i++) {
    const teacherId = i + 1; // İlk öğretmenden farklı ID'ler kullan
    const teacher = getMockTeacher(teacherId);
    
    // Eğer excludeId belirtilmişse ve bu öğretmenin ID'si ile eşleşiyorsa, atla
    if (excludeId && teacher.id === excludeId) {
      continue;
    }
    
    teachers.push(teacher);
  }
  
  return teachers;
}
