import React from 'react';
import { Star, CheckCircle } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Teacher } from '@/types/teacher';

interface TeacherReviewsTabProps {
  teacher: Teacher & {
    reviewCount: number;
  };
}

export const TeacherReviewsTab: React.FC<TeacherReviewsTabProps> = ({ teacher }) => {
  return (
    <div className="space-y-6 p-4 md:p-6">
      <Card className="bg-white border border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-gray-800">
            <Star className="w-6 h-6 text-yellow-500" />
            Öğren<PERSON>ı ({teacher.reviewCount || teacher.reviews?.length || 0})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {teacher.reviews && teacher.reviews.length > 0 ? (
            <div className="grid gap-6">
              {teacher.reviews.map((review, index) => (
                <Card key={review.id || index} className="bg-gray-50 border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          {review.studentAvatar && <AvatarImage src={review.studentAvatar} alt={review.studentName} />}
                          <AvatarFallback className="bg-blue-500 text-white">
                            {(review.studentName || 'Ö').split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="flex items-center gap-2">
                            <h4 className="font-semibold text-gray-900">{review.studentName}</h4>
                            <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Doğrulandı
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="flex">
                              {[...Array(review.rating)].map((_, i) => (
                                <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                              ))}
                              {[...Array(5 - review.rating)].map((_, i) => (
                                <Star key={i} className="w-4 h-4 text-gray-300 fill-current" />
                              ))}
                            </div>
                            <span className="text-sm text-gray-500">{new Date(review.date).toLocaleDateString('tr-TR')}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <p className="text-gray-700 leading-relaxed">{review.comment}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">Bu öğretmen için henüz hiç yorum yapılmamış.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
