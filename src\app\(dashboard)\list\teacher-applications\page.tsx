// src/app/(dashboard)/list/teacher-applications/page.tsx
import { Suspense } from "react";
import { getPendingTeacherApplications, countPendingTeacherApplications } from "@/lib/actions/teacher.actions";
import TeacherApplicationsTable from "@/components/admin/TeacherApplicationsTable";
import { TableSkeleton } from "@/components/ui/table-skeleton";
import { Metadata } from "next";

// Metadata tanımı
export const metadata: Metadata = {
  title: "Öğretmen Başvuruları | AlmancaABC Admin",
  description: "Öğretmen başvurularını yönetin",
};

// Sayfa parametreleri için tip
type TeacherApplicationsPageProps = {
  searchParams: {
    page?: string;
    search?: string;
  };
};

export default async function TeacherApplicationsPage({
  searchParams,
}: TeacherApplicationsPageProps) {
  // Sayfa ve arama parametrelerini al
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const search = searchParams.search || "";
  const limit = 10; // Sayfa başına gösterilecek öğretmen sayısı

  // Toplam sayfa sayısını hesaplamak için bekleyen başvuru sayısını al
  const totalCount = await countPendingTeacherApplications({ search });
  const totalPages = Math.ceil(totalCount / limit);

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Öğretmen Başvuruları</h1>
        <div className="text-sm text-muted-foreground">
          Toplam: <span className="font-medium">{totalCount}</span> başvuru
        </div>
      </div>

      {/* Arama ve filtreleme alanı */}
      <div className="mb-6">
        <form className="flex gap-2">
          <input
            type="text"
            name="search"
            placeholder="İsim veya e-posta ile ara..."
            defaultValue={search}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:Roo-not-allowed disabled:opacity-50"
          />
          <button
            type="submit"
            className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
          >
            Ara
          </button>
        </form>
      </div>

      {/* Tablo */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <Suspense fallback={<TableSkeleton columns={5} rows={10} />}>
          <TeacherApplicationsTable
            page={page}
            limit={limit}
            search={search}
            totalPages={totalPages}
          />
        </Suspense>
      </div>
    </div>
  );
}
