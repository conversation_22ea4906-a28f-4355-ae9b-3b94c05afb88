// src/app/(dashboard)/admin/bookings/_components/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { format } from 'date-fns'; // Tarih formatlama için
import { tr } from 'date-fns/locale'; // Türkçe lokalizasyon

// BookingData tipini page.tsx'teki ile aynı veya Prisma'dan import edilecek şekilde tanımla
type BookingData = {
  id: string;
  studentId: string;
  teacherId: string;
  lessonTime: Date;
  durationMinutes: number;
  status: string;
  pricePaid: number;
  created_at: Date;
};

// Durum için renk e<PERSON> (Tailwind renkleri veya özel CSS sınıfları kullanılabilir)
const statusVariants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
  CONFIRMED: "default", // Yeşil gibi
  PENDING_PAYMENT: "secondary", // Sarı gibi
  CANCELLED: "destructive", // Kırmızı gibi
  COMPLETED: "outline", // Mavi gibi
};


export const columns: ColumnDef<BookingData>[] = [
  {
    accessorKey: "id",
    header: "Rez. ID",
    cell: ({ row }) => <div className="truncate w-20">{row.getValue("id")}</div>
  },
  {
    // TODO: Gerçek veride öğrenci adı/email gösterilmeli
    accessorKey: "studentId",
     header: ({ column }) => {
       return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Öğrenci
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
     cell: ({ row }) => <div>{row.getValue("studentId")}</div> // Geçici
  },
   {
    // TODO: Gerçek veride öğretmen adı/email gösterilmeli
    accessorKey: "teacherId",
     header: ({ column }) => {
       return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Öğretmen
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
     cell: ({ row }) => <div>{row.getValue("teacherId")}</div> // Geçici
  },
  {
    accessorKey: "lessonTime",
    header: ({ column }) => {
       return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Ders Zamanı
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const date = new Date(row.getValue("lessonTime"));
      // dd MMMM yyyy HH:mm formatı (örn: 14 Nisan 2025 16:00)
      const formatted = format(date, "dd MMMM yyyy HH:mm", { locale: tr });
      return <div className="font-medium">{formatted}</div>;
    },
  },
   {
    accessorKey: "durationMinutes",
    header: "Süre (dk)",
  },
  {
    accessorKey: "status",
    header: "Durum",
     cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return (
        <Badge variant={statusVariants[status] || "secondary"}>
          {/* TODO: Durum metinlerini i18n ile çevir */}
          {status}
        </Badge>
      );
    },
  },
   {
    accessorKey: "pricePaid",
    header: "Ödenen Tutar",
     cell: ({ row }) => {
      const amount = parseFloat(row.getValue("pricePaid"))
      const formatted = new Intl.NumberFormat("tr-TR", {
        style: "currency",
        currency: "TRY", // TODO: Para birimini dinamik al
      }).format(amount)

      return <div className="text-right font-medium">{formatted}</div>
    },
  },
   {
    id: "actions",
    cell: ({ row }) => {
      const booking = row.original;

      return (
        (<DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Menüyü aç</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Eylemler</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(booking.id)}
            >
              Rezervasyon ID Kopyala
            </DropdownMenuItem>
            <DropdownMenuSeparator />
             <DropdownMenuItem asChild>
               <Link href={`/admin/bookings/${booking.id}`}>Detayları Görüntüle</Link>
             </DropdownMenuItem>
             {/* TODO: Rezervasyonu düzenleme/iptal etme eylemleri eklenecek */}
             <DropdownMenuItem>Düzenle (Eylem Eklenecek)</DropdownMenuItem>
             <DropdownMenuItem className="text-destructive">
                İptal Et (Eylem Eklenecek)
             </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>)
      );
    },
     enableSorting: false,
     enableHiding: false,
  },
];