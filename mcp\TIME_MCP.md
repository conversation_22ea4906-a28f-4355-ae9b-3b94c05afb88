# Time MCP Sunucusu (Referans Sunucu)

## Neden Gerekli Olabilir?
AlmancaABC projesinde [`FullCalendar`](https://fullcalendar.io/) ve Zoom SDK gibi zaman ve takvimle yakından ilişkili araçlar kullanılmaktadır. Farklı saat dilimlerindeki öğretmen ve öğrenciler için doğru zaman yönetimi, ders planlaması ve saat dilimi dönüşümleri hayati önem taşır. Bu MCP sunucusu, zaman ve saat dilimi dönüşüm yetenekleri sağlayarak bu süreçleri kolaylaştırabilir.

## Potansiyel Kullanım Alanları
- Öğretmenlerin kendi yerel saatlerine göre uygunluk belirtmesi ve bu uygunlukların sistemde UTC veya standart bir saat diliminde saklanması.
- Öğrencilerin dersleri ve öğretmen uygunluklarını kendi yerel saat dilimlerinde görüntülemesi.
- Uluslararası bir platform için ders hatırlatıcılarının ve bildirimlerinin kullanıcıların yerel saatlerine göre doğru zamanda gönderilmesi.
- Farklı saat dilimleri arasındaki karmaşıklığı yöneterek zamanlama hatalarının önüne geçilmesi.

## Entegrasyon Durumu
**Not:** Bu MCP sunucusu henüz AlmancaABC projesine entegre edilmemiştir. Entegrasyon tamamlandığında bu doküman, entegrasyon detayları ve kullanım örnekleriyle güncellenmelidir.