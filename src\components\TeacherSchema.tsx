'use client';

import React from 'react';

interface Teacher {
  id: number;
  name: string;
  image: string;
  rating: number;
  reviews: number;
  specialization: string;
  hourlyRate: number;
  description: string;
}

export default function TeacherSchema({ teachers }: { teachers: Teacher[] }) {
  const teacherSchema = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "itemListElement": teachers.map((teacher, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "Person",
        "name": teacher.name,
        "description": teacher.description,
        "image": teacher.image,
        "jobTitle": "Almanca Öğretmeni",
        "worksFor": {
          "@type": "Organization",
          "name": "AlmancaABC",
          "sameAs": "https://almancaabc.com"
        },
        "knowsAbout": [teacher.specialization],
        "makesOffer": {
          "@type": "Offer",
          "price": teacher.hourlyRate,
          "priceCurrency": "TRY",
          "availability": "https://schema.org/InStock",
          "validFrom": new Date().toISOString().split('T')[0],
          "priceValidUntil": new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString().split('T')[0]
        },
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": teacher.rating,
          "bestRating": "5",
          "ratingCount": teacher.reviews,
          "reviewCount": teacher.reviews
        },
        "teaches": "Almanca"
      }
    }))
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(teacherSchema)
      }}
    />
  );
}