'use client';

import React from 'react';
import { X } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { TeacherProfileClientData } from '@/types/teacher';

interface ProfilePhotoModalProps {
  isOpen: boolean;
  onClose: () => void;
  teacher: TeacherProfileClientData;
}

export const ProfilePhotoModal: React.FC<ProfilePhotoModalProps> = ({ 
  isOpen, 
  onClose, 
  teacher 
}) => {
  if (!isOpen) return null;

  const handleBackgroundClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[100] flex items-center justify-center p-4"
      onClick={handleBackgroundClick}
    >
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Close Button */}
        <Button
          onClick={onClose}
          variant="ghost"
          size="sm"
          className="absolute top-4 right-4 z-10 bg-white/90 hover:bg-white rounded-full w-10 h-10 p-0"
        >
          <X className="w-5 h-5" />
        </Button>

        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 text-center">
          <h2 className="text-xl font-bold">{teacher.name || `${teacher.firstName} ${teacher.lastName}`}</h2>
          <p className="text-blue-100 text-sm">Almanca Öğretmeni</p>
        </div>

        {/* Photo Section */}
        <div className="p-8 flex justify-center items-center bg-gray-50">
          <div className="relative">
            {/* Large Avatar */}
            <Avatar className="w-80 h-80 border-4 border-white shadow-2xl">
              <AvatarImage 
                src={teacher.avatar || ''} 
                alt={`${teacher.name} - Almanca Öğretmeni`}
                className="object-cover"
              />              <AvatarFallback className="text-6xl font-bold bg-gradient-to-br from-blue-600 to-purple-600 text-white">
                {(teacher.name || '').split(' ').map((n: string) => n[0]).join('')}
              </AvatarFallback>
            </Avatar>

            {/* Online Status - if needed */}
            {teacher.isOnline && (
              <div className="absolute -bottom-2 -right-2">
                <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-4 border-emerald-400">
                  <div className="w-6 h-6 rounded-full bg-emerald-500 relative">
                    <div className="absolute inset-0 rounded-full bg-emerald-400 animate-ping"></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 bg-white border-t border-gray-200 text-center">
          <p className="text-gray-600 text-sm">
            Öğretmeninizle iletişime geçmek için mesaj gönderebilir veya randevu alabilirsiniz.
          </p>
        </div>
      </div>
    </div>
  );
};
