"use client"

import * as React from "react"
import { useParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

const levelInfo = {
  a1: {
    title: "A1 - Almanca Başlangıç Seviyesi Kursu",
    description: "Almanca öğrenmeye sıfırdan başlamak isteyenler için temel günlük konuşmaları anlama ve basit Almanca cümleler kurma yeteneği kazanın.",
    topics: [
      "Almanca Selamlaşma ve Tanışma",
      "Almanca Sayılar ve Temel Matematik",
      "Almanca Renkler ve Nesneler",
      "Almanca Aile ve Arkadaşlar",
      "Almanca Yiyecek ve İçecekler"
    ],
    duration: "2-3 ay",
    lessonsPerWeek: 2
  },
  a2: {
    title: "A2 - Almanca Temel Seviye Kursu",
    description: "Almanca günlük yaşamda sık kullanılan ifadeleri anlama ve kullanma becerisi geliştirin. Almanca iletişimde kendinizi daha rahat ifade edin.",
    topics: [
      "Almanca Alışveriş ve Para",
      "Almanca Seyahat ve Ulaşım",
      "Almanca Hobiler ve Boş Zaman",
      "Almanca Saat ve Zaman",
      "Almanca Hava Durumu"
    ],
    duration: "3-4 ay",
    lessonsPerWeek: 2
  },
  b1: {
    title: "B1 - Almanca Orta Seviye Kursu",
    description: "Almanca iş, okul ve günlük yaşamda karşılaşılan durumlarla başa çıkma yeteneği kazanın. Almanca konuşma ve yazma becerinizi geliştirin.",
    topics: [
      "Almanca İş ve Kariyer",
      "Almanca Eğitim ve Öğrenim",
      "Almanca Sağlık ve Spor",
      "Almanca Medya ve İletişim",
      "Almanca Kültür ve Sanat"
    ],
    duration: "4-5 ay",
    lessonsPerWeek: 3
  },
  b2: {
    title: "B2 - Almanca Orta-Üst Seviye Kursu",
    description: "Almanca karmaşık konularda akıcı iletişim kurma ve detaylı tartışmalara katılma becerisi edinin. Almanca akademik ve iş ortamlarında kendinizi rahatça ifade edin.",
    topics: [
      "Almanca Akademik Yazı",
      "Almanca İş Dünyası",
      "Almanca Politika ve Toplum",
      "Almanca Çevre ve Teknoloji",
      "Almanca Edebiyat ve Film"
    ],
    duration: "5-6 ay",
    lessonsPerWeek: 3
  },
  c1: {
    title: "C1 - Almanca İleri Seviye Kursu",
    description: "Almanca akademik ve profesyonel ortamlarda akıcı ve doğal iletişim kurma yetkinliği kazanın. Almanca sunum, tartışma ve yazılı iletişim becerilerinizi ileri seviyeye taşıyın.",
    topics: [
      "Almanca Akademik Araştırma",
      "Almanca Profesyonel Sunum",
      "Almanca Hukuk ve Ekonomi",
      "Almanca Bilim ve İnovasyon",
      "Almanca Felsefe ve Düşünce"
    ],
    duration: "6-8 ay",
    lessonsPerWeek: 3
  }
}

export default function CoursePage() {
  const params = useParams()
  const level = (params.level as string).toLowerCase()
  const course = levelInfo[level as keyof typeof levelInfo]

  if (!course) {
    return (
      <div className="container mx-auto px-4 py-12">
        <h1 className="text-3xl font-bold text-center mb-6">Kurs Bulunamadı</h1>
        <p className="text-center">
          Aradığınız seviyede bir kurs bulunmamaktadır. Lütfen geçerli bir seviye seçin.
        </p>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">{course.title}</h1>
        <p className="text-xl text-gray-600">{course.description}</p>
      </div>

      <div className="grid md:grid-cols-2 gap-8 mb-12">
        <Card>
          <CardContent className="p-6">
            <h2 className="text-2xl font-semibold mb-4">Kurs İçeriği</h2>
            <ul className="space-y-2">
              {course.topics.map((topic, index) => (
                <li key={index} className="flex items-center gap-2">
                  <span className="text-primary">•</span>
                  {topic}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-2xl font-semibold mb-4">Kurs Detayları</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-700">Süre</h3>
                <p>{course.duration}</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-700">Haftalık Ders</h3>
                <p>{course.lessonsPerWeek} ders (90 dakika)</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-700">Seviye</h3>
                <p>{level.toUpperCase()} - {level === "a1" ? "Başlangıç" : 
                   level === "a2" ? "Temel" :
                   level === "b1" ? "Orta" :
                   level === "b2" ? "Orta-Üst" : "İleri"} Seviye</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="text-center space-y-4">
        <Button size="lg" className="bg-primary hover:bg-primary/90">
          Hemen Kaydol
        </Button>
        <p className="text-sm text-gray-500">
          * Kurs ücretleri ve detaylı bilgi için lütfen bizimle iletişime geçin.
        </p>
      </div>
    </div>
  )
}
