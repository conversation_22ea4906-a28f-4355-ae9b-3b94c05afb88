// src/app/(dashboard)/admin/teacher-applications/_components/columns.tsx
"use client";

"use client"; // Client Component olarak işaretle

import { ColumnDef } from "@tanstack/react-table";
import { Teacher } from "@prisma/client";
// import { Button } from "@/components/ui/button"; // Kaldırıldı
// import Link from "next/link"; // Kaldırıldı
import { ActionsCell } from "./ActionsCell"; // Yeni ActionsCell bileşenini import et

// Bekleyen öğretmen başvuruları için sütun tanımları
export const columns: ColumnDef<Teacher>[] = [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorFn: (row) => `${row.firstName || ''} ${row.lastName || ''}`,
    header: "İsim Soyisim",
  },
  {
    accessorKey: "email",
    header: "E-posta",
    // TODO: Clerk'ten email çekme veya Teacher modeline ekleme
    cell: ({ row }) => {
      // Geçici olarak örnek email gösterimi
      const teacher = row.original;
      return `${teacher.firstName?.toLowerCase() || 'teacher'}@example.com`;
    }
  },
  {
    accessorKey: "created_at",
    header: "Başvuru Tarihi",
    cell: ({ row }) => {
      const date = new Date(row.getValue("created_at"));
      return date.toLocaleDateString();
    },
  },
  {
    id: "actions",
    header: "Eylemler",
    cell: ({ row }) => {
      const teacher = row.original;
      // ActionsCell bileşenini render et ve teacher prop'unu ilet
      return <ActionsCell teacher={teacher} />;
    },
  },
];