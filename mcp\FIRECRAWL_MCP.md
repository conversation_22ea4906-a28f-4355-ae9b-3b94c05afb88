# Firecrawl MCP <PERSON><PERSON> (AlmancaABC Projesi)

## 🔥 Firecrawl MCP Nedir?
Firecrawl MCP; web kazıma (scraping), site haritalama, toplu veri çekme, derin araştırma ve LLM tabanlı veri çıkarımı için gelişmiş araçlar sunar. Özellikle öğretmen listeleme, rakip analizleri ve otomatik içerik toplama gibi senaryolar için idealdir.

## 🚀 Kurulum & Entegrasyon (Örnek mcp_config.json)
Senin ortamında Firecrawl MCP şöyle tanımlı:
```json
"github.com/mendableai/firecrawl-mcp-server": {
  "command": "cmd",
  "args": [
    "/c",
    "npx",
    "-y",
    "firecrawl-mcp"
  ],
  "env": {
    "FIRECRAWL_API_KEY": "fc-..."
  },
  "disabled": false,
  "alwaysAllow": [
    "firecrawl_scrape",
    "firecrawl_crawl",
    "firecrawl_map",
    "firecrawl_batch_scrape",
    "firecrawl_check_batch_status",
    "firecrawl_check_crawl_status",
    "firecrawl_search",
    "firecrawl_extract",
    "firecrawl_deep_research",
    "firecrawl_generate_llmstxt"
  ]
}
```
> **Not:** API anahtarını gizli tut! Tüm ana araçlar `alwaysAllow` ile her zaman erişilebilir.

## 🔑 Temel Firecrawl Araçları & Parametreleri

| Komut                  | Açıklama                                                         | En Sık Parametreler         |
|------------------------|------------------------------------------------------------------|-----------------------------|
| `firecrawl_scrape`     | Tek bir sayfadan içerik kazır.                                   | url, formats, onlyMainContent, includeTags, excludeTags, waitFor, timeout, mobile |
| `firecrawl_map`        | Bir URL'den site haritası/bağlantı keşfi yapar.                  | url, search, ignoreSitemap, sitemapOnly, includeSubdomains, limit |
| `firecrawl_crawl`      | Çoklu sayfa derinlemesine kazıma (crawl).                       | url, maxDepth, limit, allowExternalLinks, deduplicateSimilarURLs, scrapeOptions |
| `firecrawl_search`     | Webde arama ve sonuçlardan içerik çekme.                        | query, limit, lang, country, scrapeOptions |
| `firecrawl_extract`    | LLM ile yapılandırılmış veri çıkarımı.                           | urls, prompt, systemPrompt, schema, allowExternalLinks |
| `firecrawl_deep_research` | Derin web araştırması ve analiz.                              | query, maxDepth, timeLimit, maxUrls |
| `firecrawl_generate_llmstxt` | LLMs.txt dosyası üretir (LLM uyumluluğu için).             | url, maxUrls, showFullText |

## 🛠️ Pratik Kullanım Senaryosu: Öğretmen Kartı Analizi (Preply Örneği)

### 1. Preply'den Öğretmen Kartlarını Çekmek
```json
{
  "url": "https://preply.com/tr/online/almanca-e%C4%9Fitimi",
  "formats": ["html"],
  "onlyMainContent": true,
  "waitFor": 1500,
  "timeout": 12000,
  "mobile": false,
  "includeTags": ["main", "section", "article"],
  "excludeTags": ["nav", "footer"]
}
```
> Bu ayarlar ile Preply'nin Almanca öğretmen kartlarının HTML yapısını çekebilirsin.

### 2. Kendi Kartlarını Geliştirmek İçin:
- Preply'den çektiğin HTML'i incele: Kartlarda hangi bilgiler (fotoğraf, isim, açıklama, fiyat, aksiyon butonları, etiketler vb.) öne çıkıyor?
- Kendi `/teachers` sayfanda, **shadcn/ui** ve **Tailwind** ile modern, erişilebilir ve mobil uyumlu kartlar tasarla.
- Gerekirse `firecrawl_extract` ile kartlardan yapılandırılmış veri (isim, fiyat, açıklama) çekebilirsin.

## 💡 İpuçları
- **Karmaşık sayfa yapılarında**: Önce `firecrawl_map` ile ilgili tüm alt sayfaları keşfet, sonra toplu olarak `firecrawl_scrape` veya `firecrawl_batch_scrape` ile veri çek.
- **Yapılandırılmış veri** gerekiyorsa: `firecrawl_extract` ile JSON şeması tanımlayarak otomatik bilgi çıkar.
- **Rakip analizi** veya trend araştırması için: `firecrawl_search` ve `firecrawl_deep_research` kullan.

## ℹ️ Daha Fazla Bilgi
- [Firecrawl Resmi Dokümantasyon](https://github.com/mendableai/firecrawl-mcp-server)
- [Firecrawl Web Sitesi](https://firecrawl.dev)

> **Bu rehber, AlmancaABC projesinde Firecrawl MCP'nin hızlı ve etkili kullanımını amaçlar. Geliştirme sırasında güncel tutmayı unutma!**
