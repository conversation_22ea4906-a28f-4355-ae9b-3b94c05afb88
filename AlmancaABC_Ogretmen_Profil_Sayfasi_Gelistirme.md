# AlmancaABC - Öğretmen Profil Sayfası Geliştirme Süreci (Revize Edilmiş - sprachmeister-academy-online Entegrasyonu)

Bu belge, AlmancaABC platformu için kapsamlı, modern, kullanıcı dostu ve `sprachmeister-academy-online` projesinden alınan tasarımla geliştirilecek öğretmen kişisel profil sayfası (`/ogretmenler/[id]`) sürecini içermektedir. Temel hedef, öğrencilerin öğretmenler hakkında detaylı bilgi edinmelerini, kolayca ders talebinde bulunmalarını ve öğretmenlerin de kendilerini en iyi şekilde tanıtabilmelerini sağlamaktır. **Bu çalışma, ana sayfay<PERSON> (`/`) değil, öğretmen detay sayfasını etkileyecektir.**

## 1. Amaç ve Hedefler

*   Mevcut öğretmen profil sayfasını (`src/app/ogretmenler/[id]/page.tsx` ve ilgili alt bileşenler) `sprachmeister-academy-online` projesindeki modern tasarım ve bileşenlerle tamamen yenilemek.
*   Kullanıcı deneyimini (UX) ve kullanıcı arayüzünü (UI) iyileştirmek.
*   SEO uyumluluğunu ve yapısal veri entegrasyonunu (Schema.org) sağlamak.
*   Kolay ve güvenli rezervasyon akışı sunmak.

## 2. Rakip Analizinden Öne Çıkanlar ve İlham Alınacak Noktalar

(Bu bölüm orijinal belgedeki gibi kalabilir, genel prensipler geçerlidir.)

İncelenen rakip siteler ([italki](Örnek Link: https://www.italki.com/tr/teacher/7100255/german), [ozelders.com](https://www.ozelders.com/ders-veren/mudy-b-632368), [preply.com](https://preply.com/tr/%C3%B6%C4%9Fretmen/295560), [ozeldersalani.com](https://www.ozeldersalani.com/emnekosee), [armut.com](https://armut.com/hizmetveren/firdevs-bakioglu-izmir-urla-almanca-ozel-ders_71138588), [superprof.com.tr](https://www.superprof.com.tr/den-kadar-almanca-almanya-danismanlik-sertifikali-egitim-geothe-telc-osd-testdaf-dsh-aile-birlesimi-hazirlik.html), Goethe Enstitüsü):

*   **Genel UX/UI:** Kullanıcı odaklı hızlı filtreleme/yönlendirme, modern ve temiz arayüzler.
*   **Öğretmen Kartları/Profilleri:** Video tanıtım, detaylı istatistikler, mezuniyet bilgisi, slogan/başlık, ücretsiz tanışma dersi vurgusu, anında müsaitlik gösterimi.
*   **Güven Unsurları:** Rozetler, onaylı yorumlar, platform garantileri.
*   **SEO ve İçerik:** Kapsamlı SSS, Schema.org kullanımı.
*   **Paketler ve Hedefler:** Hedefe yönelik ders paketleri.

## 3. Temel Özellikler ve Entegrasyon Adımları (`sprachmeister-academy-online` Kaynaklı)

Bu bölüm, `almancaabc` projesindeki mevcut öğretmen profil sayfasının (`src/app/ogretmenler/[id]/page.tsx` ve `src/components/teacher/TeacherProfileClient.tsx` merkezli), kullanıcının sağladığı `sprachmeister-academy-online/` projesindeki yeni tasarım ve bileşenlerle güncellenmesi sürecini detaylandırmaktadır.

**Referans Proje Kaynağı:** Kullanıcının sağladığı `sprachmeister-academy-online` proje dosyaları.

### 3.1. Dosya ve Bileşen Transferi (`sprachmeister-academy-online` -> `almancaabc`)

1.  [X] **Temel Yardımcı Dosyalar ve Tipler:**
    *   [X] `sprachmeister-academy-online/src/lib/utils.ts` -> `almancaabc/src/lib/utils.ts` (Tamamlandı)
    *   [X] `sprachmeister-academy-online/src/types/index.ts` içeriği `almancaabc/src/types/teacher.ts` dosyasına entegre edildi. (`Teacher` tipi ve ilgili diğer tipler güncellendi.) (Tamamlandı)
2.  [ ] **Hook'lar:**
    *   [X] `sprachmeister-academy-online/src/hooks/use-toast.ts` -> `almancaabc/src/hooks/useToast.ts` (Dosya adı `useToast.ts` olarak düzeltilecek) ✅
    *   [X] `sprachmeister-academy-online/src/hooks/use-mobile.tsx` -> `almancaabc/src/hooks/useMobile.tsx` ✅
    *   [X] `sprachmeister-academy-online/src/hooks/useTeacherData.ts` -> `almancaabc/src/hooks/useTeacherData.ts` (Bu hook, `almancaabc` projesinde API'den veri çekecek şekilde (`getTeacherProfileData` action'ı kullanılarak) güncellenecektir. Şimdilik veri yapısını anlamak için referans alınacaktır.) ✅
3.  [✅] **UI Bileşenleri (`components/ui/`):**
    *   [✅] `sprachmeister-academy-online/src/components/ui/accordion.tsx` -> `almancaabc/src/components/ui/accordion.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/alert-dialog.tsx` -> `almancaabc/src/components/ui/alert-dialog.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/alert.tsx` -> `almancaabc/src/components/ui/alert.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/aspect-ratio.tsx` -> `almancaabc/src/components/ui/aspect-ratio.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/avatar.tsx` -> `almancaabc/src/components/ui/avatar.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/badge.tsx` -> `almancaabc/src/components/ui/badge.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/breadcrumb.tsx` -> `almancaabc/src/components/ui/breadcrumb.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/button.tsx` -> `almancaabc/src/components/ui/button.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/calendar.tsx` -> `almancaabc/src/components/ui/calendar.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/card.tsx` -> `almancaabc/src/components/ui/card.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/carousel.tsx` -> `almancaabc/src/components/ui/carousel.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/chart.tsx` -> `almancaabc/src/components/ui/chart.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/checkbox.tsx` -> `almancaabc/src/components/ui/checkbox.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/collapsible.tsx` -> `almancaabc/src/components/ui/collapsible.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/command.tsx` -> `almancaabc/src/components/ui/command.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/context-menu.tsx` -> `almancaabc/src/components/ui/context-menu.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/dialog.tsx` -> `almancaabc/src/components/ui/dialog.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/drawer.tsx` -> `almancaabc/src/components/ui/drawer.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/dropdown-menu.tsx` -> `almancaabc/src/components/ui/dropdown-menu.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/form.tsx` -> `almancaabc/src/components/ui/form.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/hover-card.tsx` -> `almancaabc/src/components/ui/hover-card.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/input-otp.tsx` -> `almancaabc/src/components/ui/input-otp.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/input.tsx` -> `almancaabc/src/components/ui/input.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/label.tsx` -> `almancaabc/src/components/ui/label.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/menubar.tsx` -> `almancaabc/src/components/ui/menubar.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/navigation-menu.tsx` -> `almancaabc/src/components/ui/navigation-menu.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/pagination.tsx` -> `almancaabc/src/components/ui/pagination.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/popover.tsx` -> `almancaabc/src/components/ui/popover.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/progress.tsx` -> `almancaabc/src/components/ui/progress.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/radio-group.tsx` -> `almancaabc/src/components/ui/radio-group.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/resizable.tsx` -> `almancaabc/src/components/ui/resizable.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/scroll-area.tsx` -> `almancaabc/src/components/ui/scroll-area.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/select.tsx` -> `almancaabc/src/components/ui/select.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/separator.tsx` -> `almancaabc/src/components/ui/separator.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/sheet.tsx` -> `almancaabc/src/components/ui/sheet.tsx`
    *   [⏭️] `sprachmeister-academy-online/src/components/ui/sidebar.tsx` -> `almancaabc/src/components/ui/sidebar.tsx` (Özel bir bileşen, şimdilik atlanacak)
    *   [✅] `sprachmeister-academy-online/src/components/ui/skeleton.tsx` -> `almancaabc/src/components/ui/skeleton.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/slider.tsx` -> `almancaabc/src/components/ui/slider.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/sonner.tsx` -> `almancaabc/src/components/ui/sonner.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/switch.tsx` -> `almancaabc/src/components/ui/switch.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/table.tsx` -> `almancaabc/src/components/ui/table.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/tabs.tsx` -> `almancaabc/src/components/ui/tabs.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/textarea.tsx` -> `almancaabc/src/components/ui/textarea.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/toast.tsx` -> `almancaabc/src/components/ui/toast.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/toaster.tsx` -> `almancaabc/src/components/ui/toaster.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/toggle-group.tsx` -> `almancaabc/src/components/ui/toggle-group.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/toggle.tsx` -> `almancaabc/src/components/ui/toggle.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/tooltip.tsx` -> `almancaabc/src/components/ui/tooltip.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/ui/use-toast.ts` -> `almancaabc/src/components/ui/use-toast.ts`
    *   [✅] `sprachmeister-academy-online/src/hooks/use-toast.ts` -> `almancaabc/src/hooks/use-toast.ts`
    *   [✅] Kopyalama sonrası içe aktarma yolları (`@/lib/utils`, diğer `@/components/ui/*` vb.) kontrol edildi ve düzeltildi.
    *   [✅] `sprachmeister-academy-online/package.json` dosyasındaki bağımlılıklar incelenerek (`@radix-ui/*`, `cmdk`, `vaul`, `embla-carousel-react`, `recharts`, `input-otp` vb.) `almancaabc` projesinin `package.json` dosyasına eksik olanlar eklendi ve `bun install` komutu çalıştırıldı.
4.  [✅] **Öğretmen Profili Ana ve Alt Bileşenleri:**
    *   [✅] `sprachmeister-academy-online/src/components/Header.tsx` -> `almancaabc/src/components/teacher/profile/TeacherProfileHero.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/AchievementsBar.tsx` -> `almancaabc/src/components/teacher/profile/TeacherStatsBar.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/Navigation.tsx` -> `almancaabc/src/components/teacher/profile/TeacherTabNavigation.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/tabs/AboutTab.tsx` -> `almancaabc/src/components/teacher/profile/tabs/TeacherAboutTab.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/tabs/CoursesTab.tsx` -> `almancaabc/src/components/teacher/profile/tabs/TeacherCoursesTab.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/tabs/ReviewsTab.tsx` -> `almancaabc/src/components/teacher/profile/tabs/TeacherReviewsTab.tsx`
    *   [✅] `sprachmeister-academy-online/src/components/tabs/VideosTab.tsx` -> `almancaabc/src/components/teacher/profile/tabs/TeacherVideosTab.tsx`
    *   [✅] **Modals:**
        *   [✅] `sprachmeister-academy-online/src/components/modals/BookingCalendar.tsx` -> `almancaabc/src/components/modals/TeacherBookingCalendarModal.tsx`
        *   [✅] `sprachmeister-academy-online/src/components/modals/VideoModal.tsx` -> `almancaabc/src/components/modals/VideoPlayerModal.tsx`
    *   [✅] *Not: Kopyalama sonrası tüm içe aktarma yolları güncellendi ve TypeScript hataları giderildi.*
5.  [✅] **Stil Dosyalarının Entegrasyonu:**
    *   [✅] `sprachmeister-academy-online/src/index.css` içeriği incelendi ve gerekli utility sınıflarının (`modal-open-background-scroll`, `scrollbar-hide` vb.) `almancaabc/src/app/globals.css` dosyasında zaten mevcut olduğu teyit edildi. Ek bir işlem yapılmadı.
6.  [✅] **Ana Layout ve Provider'ların Güncellenmesi (`almancaabc/src/app/layout.tsx`):**
    *   [✅] `sprachmeister-academy-online/src/App.tsx` dosyası incelendi. `TooltipProvider` ve `Toaster` (`Sonner`) gibi bileşenlerin `almancaabc` projesinde zaten `layout.tsx` veya ilgili provider'lar içinde bulunduğu teyit edildi. `QueryClientProvider` Next.js App Router'da farklı yönetildiği için bu adımda ek bir işlem yapılmadı.
7.  [✅] **`TeacherProfileClient.tsx` Refaktörü (`almancaabc/src/components/teacher/TeacherProfileClient.tsx`):**
    *   [✅] Bu dosya, `sprachmeister-academy-online/src/pages/Index.tsx` dosyasının mantığını ve yapısını temel alarak tamamen yeniden düzenlendi.
    *   [✅] Yeni kopyalanan tüm alt bileşenleri (Hero, StatsBar, TabNavigation, Tabs, Modals) kullanacak şekilde güncellendi.
    *   [✅] State yönetimi (aktif sekme, modal görünürlüğü vb.) bu bileşen içine taşındı.
8.  [✅] **Backend Güncellemeleri:**
    *   [✅] **Prisma Şeması ([`prisma/schema.prisma`](prisma/schema.prisma)):** `Teacher` modeli, yeni tasarıma ve veri yapısına uyacak şekilde güncellendi. `LessonPackage` modeli daha esnek hale getirildi.
    *   [✅] `bunx prisma migrate dev --name integrate_sprachmeister_profile_updates` komutu çalıştırılarak veritabanı şeması güncellendi.
    *   [✅] **Server Actions ([`src/lib/actions/teacher.actions.ts`](src/lib/actions/teacher.actions.ts)):** `getTeacherProfileData` fonksiyonu, yeni şemaya ve `TeacherProfileClient`'in ihtiyaçlarına göre güncellendi.
9.  [⚙️] **Veri Akışının Sağlanması (`almancaabc/src/app/ogretmenler/[id]/page.tsx`):**
    *   [✅] Bu sunucu bileşeni, güncellenmiş `getTeacherProfileData` action'ını kullanacak şekilde güncellendi.
    *   [⚙️] Sunucudan istemciye veri aktarımı sırasında ortaya çıkan TypeScript tip uyuşmazlıkları (örn: `Decimal` nesnesi, `null` vs `undefined`) büyük ölçüde giderildi. Ancak son bir tip hatası (`oneOnOnePackages` ve `description` ile ilgili) çözülmeyi bekliyor.
10. [ ] **SEO ve Metadata:**
    *   [ ] `TeacherProfileClient.tsx` içindeki `useEffect` ile yapılan `document.title` ve meta açıklama ayarları korunacak ve dinamik öğretmen verisine göre çalışması sağlanacak.
    *   [ ] `sprachmeister-academy-online/index.html` dosyasındaki Schema.org (`Person`, `Offer` vb.) yapıları, `almancaabc` projesindeki öğretmen profil sayfası için `src/components/SchemaOrgWrapper.tsx` veya benzeri bir yapı kullanılarak dinamik olarak oluşturulacak şekilde entegre edilecek.
11. [ ] **Test ve İyileştirme:**
    *   [ ] Sayfanın tüm fonksiyonları (sekme geçişleri, modal açılıp kapanmaları, rezervasyon akışı) test edilecek.
    *   [ ] Mobil uyumluluk ve farklı ekran boyutlarında görünüm kontrol edilecek.
    *   [ ] Tarayıcı konsolunda hata olup olmadığı kontrol edilecek.
    *   [ ] TypeScript hataları giderilecek.
    *   [ ] Temel SEO etiketleri ve yapısal verilerin doğru yüklendiği kontrol edilecek.

## 4. Teknoloji Yığını (Mevcutla Uyumlu ve `sprachmeister-academy-online` Referanslı)

*   **Frontend:** Next.js (App Router), React (v18/v19), TypeScript
*   **Styling:** Tailwind CSS, Shadcn/ui (UI bileşenleri `sprachmeister-academy-online/src/components/ui/` klasöründen alınacak ve `almancaabc/src/components/ui/` altına yerleştirilecek)
*   **Animasyon:** Framer Motion
*   **Backend & Veritabanı:** Supabase (PostgreSQL), Prisma ORM
*   **Kimlik Doğrulama:** Clerk
*   **Form Yönetimi:** React Hook Form, Zod (Gerektiğinde kullanılacak)
*   **Takvim:** Yeni `BookingCalendar.tsx` içindeki özel takvim mantığı.
*   **State Management:** React Context API, `useState`, `useEffect`.
*   **Paket Yöneticisi:** `bun`

## 5. Geliştirme Süreci ve İlerleme Takibi

Bu doküman, yukarıdaki adımların ilerlemesini takip etmek için kullanılacaktır. Her adım tamamlandığında işaretlenecektir.

1.  [✅] **Plan Güncellemesi ve Onay:** Bu doküman, kullanıcının son direktiflerine göre revize edildi. 
2.  [✅] **Temel Dosyaların Kopyalanması ve Entegrasyonu** (`utils.ts` kopyalandı, `types/index.ts` içeriği `types/teacher.ts`'e entegre edildi)
3.  [✅] **Hook'ların Kopyalanması ve Entegrasyonu** (useToast.ts, useMobile.tsx, useTeacherData.ts tamamlandı)
4.  [ ] **UI Bileşenlerinin Kopyalanması, Entegrasyonu ve Bağımlılıkların Yüklenmesi**
5.  [ ] **Öğretmen Profili Ana ve Alt Bileşenlerinin Kopyalanması ve Entegrasyonu**
6.  [ ] **Stil Dosyalarının Entegrasyonu**
7.  [ ] **Ana Layout ve Provider'ların Güncellenmesi**
8.  [ ] **`TeacherProfileClient.tsx` Refaktörü**
9.  [ ] **Backend Güncellemeleri (Prisma Şeması, Migrasyon, Server Actions)**
10. [ ] **Veri Akışının Sağlanması (`page.tsx` -> `TeacherProfileClient.tsx`)**
11. [ ] **SEO ve Metadata Entegrasyonu**
12. [ ] **Kapsamlı Test ve İyileştirme**

---
*Bu belge geliştirme süreci boyunca güncellenecektir.*
