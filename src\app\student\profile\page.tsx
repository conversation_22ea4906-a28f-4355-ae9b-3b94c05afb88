// import { currentUser } from '@clerk/nextjs/server'; // Clerk kald<PERSON><PERSON><PERSON><PERSON><PERSON>
import { redirect } from 'next/navigation';

// Mock kullanıcı tipi (Clerk'ten gelen user nesnesine benzer)
interface MockUser {
  id: string;
  firstName: string | null;
  lastName: string | null;
  emailAddresses: Array<{ emailAddress: string }>;
  publicMetadata: {
    role?: string;
    // Diğer metadata alanları eklenebilir
  };
  // Clerk user nesnesinde bulunan diğer alanlar buraya eklenebilir
}

export default async function StudentProfilePage() {
  // const user = await currentUser(); // Clerk kaldırıldı

  let user: MockUser | null = null;

  if (process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true" || process.env.NEXT_PUBLIC_DEV_SKIP_AUTH_MIDDLEWARE === "true") {
    // console.warn("DEV_SKIP_AUTH_MIDDLEWARE: StudentProfilePage - Mock student data kullanılıyor.");
    user = {
      id: "test-student-id",
      firstName: "Test",
      lastName: "<PERSON>ğ<PERSON>ci",
      emailAddresses: [{ emailAddress: "<EMAIL>" }],
      publicMetadata: { role: "student" },
    };
  }

  // Kullanıcı yoksa veya rolü öğrenci değilse
  if (!user || user.publicMetadata?.role !== 'student') {
    // Normalde middleware yönlendirir, ama buraya ulaşırsa diye ana sayfaya yönlendir.
    redirect('/');
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-6 text-3xl font-bold">Öğrenci Profilim</h1>
      <div className="space-y-4 rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
        <p>
          <span className="font-semibold">Ad:</span> {user.firstName || "N/A"}
        </p>
        <p>
          <span className="font-semibold">Soyad:</span> {user.lastName || "N/A"}
        </p>
        <p>
          <span className="font-semibold">E-posta:</span>{' '}
          {user.emailAddresses[0]?.emailAddress || "N/A"}
        </p>
        <p>
          <span className="font-semibold">Rol:</span>{' '}
          <span className="rounded-full bg-secondary px-2 py-1 text-xs text-secondary-foreground">
            {String(user.publicMetadata?.role) || "N/A"}
          </span>
        </p>
        {/* Buraya daha fazla profil detayı eklenebilir */}
      </div>
    </div>
  );
}