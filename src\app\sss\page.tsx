// src/app/sss/page.tsx

'use client';

import FaqAccordion from '@/components/FaqAccordion';
import Link from 'next/link';
import { ChevronLeft, MessageSquarePlus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ModernContactForm } from '@/app/iletisim/page';
import { IletisimBilgileriKarti } from '@/components/IletisimBilgileriKarti';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
} from "@/components/ui/dialog";
import { motion } from "framer-motion";
import Head from 'next/head';
import { genelFaqData, FaqItem } from '@/lib/data/faq-data'; // Merkezi SSS verisini import ediyoruz

// FAQPage için JSON-LD verisi oluşturan fonksiyon (artık FaqItem tipini kullanıyor)
const generateFaqPageSchema = (faqItems: FaqItem[]) => {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqItems.map(item => ({
      "@type": "Question",
      "name": item.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": item.answer
      }
    }))
  };
};

const SssPage = () => {
  // Genel SSS sayfasında genelFaqData'yı kullanıyoruz
  const faqItemsToDisplay = genelFaqData;
  const faqPageSchema = generateFaqPageSchema(faqItemsToDisplay);

  return (<>
    <Head>
      <title>Sık Sorulan Sorular | AlmancaABC</title>
      <meta name="description" content="AlmancaABC platformu ve hizmetlerimiz hakkında sıkça sorulan sorular ve cevapları." />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqPageSchema) }}
      />
    </Head>
    <div className="container mx-auto py-8 px-4 md:px-6">
      <div className="mb-8">
        <Button variant="outline" asChild className="text-sm">
          <Link href="/yardim">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Yardım Merkezine Dön
          </Link>
        </Button>
      </div>
      <FaqAccordion items={faqItemsToDisplay} title="Genel Sık Sorulan Sorular" />

      {/* İletişim Formu (Popup) Bölümü */}
      <section className="py-8 md:py-12 bg-slate-50 dark:bg-slate-900 mt-12 md:mt-16 rounded-lg">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Bizimle İletişime Geçin
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6 max-w-xl mx-auto">
              Sorularınız mı var veya yardıma mı ihtiyacınız var? Hızlıca mesaj gönderebilir veya diğer tüm iletişim seçenekleri için <Link href="/iletisim" className="text-sky-600 hover:underline dark:text-sky-400">iletişim sayfamızı</Link> ya da <Link href="/yardim" className="text-sky-600 hover:underline dark:text-sky-400">yardım merkezimizi</Link> ziyaret edebilirsiniz.
            </p>
            <Dialog>
              <DialogTrigger asChild>
                <Button size="lg" className="bg-primary hover:bg-primary/90 text-white dark:bg-sky-600 dark:hover:bg-sky-700">
                  <MessageSquarePlus className="mr-2 h-5 w-5" />
                  Mesaj Gönder
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-4xl dark:bg-slate-800 p-0">
                <DialogHeader className="p-6 pb-4">
                  <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-gray-100">Bize Ulaşın</DialogTitle>
                  <DialogDescription className="text-sm text-gray-600 dark:text-gray-300">
                    Aşağıdaki formu doldurarak veya iletişim bilgilerimizi kullanarak bize ulaşabilirsiniz.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-1 lg:grid-cols-5 overflow-hidden rounded-b-lg">
                  <IletisimBilgileriKarti />
                  <div className="p-6 md:p-8 lg:col-span-3 bg-white dark:bg-slate-800">
                     <ModernContactForm />
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </motion.div>
        </div>
      </section>
    </div>
  </>);
};

export default SssPage;