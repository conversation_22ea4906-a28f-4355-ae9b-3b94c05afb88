"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, X } from 'lucide-react';
import dynamic from 'next/dynamic';

import { TeacherProfileClientData, ClientPackage as Package, PackageOption } from '@/types/teacher';
import { TeacherProfileHero } from "@/components/teacher/profile/TeacherProfileHero";
import { TeacherStatsBar } from "@/components/teacher/profile/TeacherStatsBar";
import { TeacherTabNavigation } from "@/components/teacher/profile/TeacherTabNavigation";
import { TeacherCoursesTab } from "@/components/teacher/profile/tabs/TeacherCoursesTab";
import { TeacherVideosTab } from "@/components/teacher/profile/tabs/TeacherVideosTab";
import { TeacherReviewsTab } from "@/components/teacher/profile/tabs/TeacherReviewsTab";
import { TeacherAboutTab } from "@/components/teacher/profile/tabs/TeacherAboutTab";

const TeacherBookingCalendarModal = dynamic(
  () => import('@/components/modals/TeacherBookingCalendarModal').then(mod => mod.TeacherBookingCalendarModal),
  {
    ssr: false,
    loading: () => (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white p-8 rounded-2xl shadow-2xl">
          <p className="text-lg font-semibold">Takvim Yükleniyor...</p>
        </div>
      </div>
    ),
  }
);

const VideoPlayerModal = dynamic(
  () => import('@/components/modals/VideoPlayerModal').then(mod => mod.VideoPlayerModal),
  {
    ssr: false,
    loading: () => (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white p-8 rounded-2xl shadow-2xl">
          <p className="text-lg font-semibold">Video Yükleniyor...</p>
        </div>
      </div>
    ),
  }
);

interface TeacherProfileClientProps {
  teacher: TeacherProfileClientData;
}

const TeacherProfileClient: React.FC<TeacherProfileClientProps> = ({ teacher }) => {
  const [activeTab, setActiveTab] = useState('courses');
  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<{ pkg: Package, option: PackageOption } | null>(null);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [showBookingSuccess, setShowBookingSuccess] = useState(false);

  const handleCloseCalendar = () => {
    setShowCalendar(false);
    setSelectedPackage(null);
  };

  const handleVideoPlay = (videoIndex: number) => {
    setCurrentVideoIndex(videoIndex);
    setShowVideoModal(true);
  };

  const handlePackageSelect = (pkg: Package, option: PackageOption) => {
    setSelectedPackage({ pkg, option });
    setShowCalendar(true);
  };

  const handleBookingSuccess = () => {
    setShowBookingSuccess(true);
    setTimeout(() => {
      setShowBookingSuccess(false);
    }, 5000);
  };

  useEffect(() => {
    if (showCalendar) {
      document.body.classList.add('modal-open-background-scroll');
    } else {
      document.body.classList.remove('modal-open-background-scroll');
    }
    return () => {
      document.body.classList.remove('modal-open-background-scroll');
    };
  }, [showCalendar]);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'courses':
        return <TeacherCoursesTab teacher={teacher} onPackageSelect={handlePackageSelect} />;
      case 'videos':
        return <TeacherVideosTab teacher={teacher} onVideoPlay={handleVideoPlay} />;
      case 'reviews':
        return <TeacherReviewsTab teacher={teacher} />;
      case 'about':
        return <TeacherAboutTab teacher={teacher} />;
      default:
        return <TeacherCoursesTab teacher={teacher} onPackageSelect={handlePackageSelect} />;
    }
  };

  return (
    <>
      <div className="min-h-screen bg-gray-50 w-full overflow-x-hidden" suppressHydrationWarning>
        <TeacherProfileHero teacher={teacher} onShowCalendar={() => setShowCalendar(true)} />
        <div className="w-full">
          <TeacherStatsBar teacher={teacher} />
          <TeacherTabNavigation activeTab={activeTab} onTabChange={setActiveTab} />
          
          <main className="w-full lg:max-w-7xl lg:mx-auto py-4 lg:py-8 px-4 sm:px-6 lg:px-8 box-border">
            <div className="bg-white rounded-xl lg:rounded-2xl shadow-sm border border-gray-200 overflow-hidden w-full">
              {renderTabContent()}
            </div>
          </main>
        </div>
      </div>

      {showCalendar && teacher && (
        <TeacherBookingCalendarModal
          onClose={handleCloseCalendar}
          selectedCourse={selectedPackage ? {
            title: selectedPackage.pkg.name,
            price: selectedPackage.option.price || "0"
          } : null}
          teacher={teacher}
          onBookingSuccess={handleBookingSuccess}
        />
      )}

      {showVideoModal && (
        <VideoPlayerModal
          showVideoModal={showVideoModal}
          onClose={() => setShowVideoModal(false)}
          teacher={teacher}
          currentVideoIndex={currentVideoIndex}
          onVideoIndexChange={setCurrentVideoIndex}
        />
      )}

      <AnimatePresence>
        {showBookingSuccess && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.9, opacity: 0, y: 20 }}
              className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full text-center relative"
            >
              <button
                onClick={() => setShowBookingSuccess(false)}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Rezervasyon Başarılı!</h2>
              <p className="text-gray-600 mb-4">
                Randevunuz başarıyla oluşturuldu. Kısa süre içinde size onay e-postası gönderilecektir.
              </p>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <p className="text-green-700 text-sm">
                  <strong>Önemli:</strong> Randevu detaylarınızı e-posta adresinize gönderdik.
                  Lütfen spam klasörünüzü de kontrol edin.
                </p>
              </div>
              <p className="text-sm text-gray-400">Bu pencere otomatik olarak kapanacaktır.</p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default TeacherProfileClient;
