// src/components/dashboard/PendingApplicationsCard.tsx
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"; // Avatar ekleyelim
import { getPendingTeacherApplicationsSummary } from "@/lib/actions/admin.actions";
import { formatDistanceToNow } from 'date-fns';
import { tr } from 'date-fns/locale';

// Server Action'dan dönen tipe uygun bir tip
type PendingTeacher = {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string; // Geçici
    created_at: Date;
}

export async function PendingApplicationsCard() {
  const pendingTeachers: PendingTeacher[] = await getPendingTeacherApplicationsSummary(5); // Son 5 başvuruyu al

  return (
    (<Card>
      <CardHeader>
        <CardTitle>Bekleyen Öğretmen Başvuruları</CardTitle>
        <CardDescription>
          Onay bekleyen son {pendingTeachers.length} başvuru.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {pendingTeachers.length === 0 ? (
          <p className="text-sm text-muted-foreground">Bekleyen başvuru bulunmuyor.</p>
        ) : (
          pendingTeachers.map((teacher) => (
            <div key={teacher.id} className="flex items-center gap-4">
              <Avatar className="hidden h-9 w-9 sm:flex">
                 {/* TODO: Gerçek profil resmi eklenecek */}
                <AvatarImage src={`https://avatar.vercel.sh/${teacher.email}.png`} alt="Avatar" />
                <AvatarFallback>{teacher.firstName?.[0]}{teacher.lastName?.[0]}</AvatarFallback>
              </Avatar>
              <div className="grid gap-1">
                <p className="text-sm font-medium leading-none">
                  {teacher.firstName} {teacher.lastName}
                </p>
                <p className="text-xs text-muted-foreground">{teacher.email}</p>
              </div>
              <div className="ml-auto text-xs text-muted-foreground">
                 {formatDistanceToNow(new Date(teacher.created_at), { addSuffix: true, locale: tr })}
              </div>
               {/* TODO: Onayla/Reddet butonları veya detay linki eklenebilir */}
               <Button asChild variant="outline" size="sm">
                 <Link href={`/admin/teachers/${teacher.id}`}>İncele</Link>
               </Button>
            </div>
          ))
        )}
         {pendingTeachers.length > 0 && (
             <Button asChild size="sm" className="mt-4 w-full">
                {/* Öğretmen başvuruları sayfasına yönlendir */}
                <Link href="/admin/teacher-applications">Tüm Bekleyenleri Gör</Link>
             </Button>
         )}
      </CardContent>
    </Card>)
  );
}