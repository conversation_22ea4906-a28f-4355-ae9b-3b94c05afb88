classDiagram
    direction LR

    %% --- Core Entities (MVP & Base) ---

    class Teacher {
        +String id (PK)
        +String bio
        +String[] specializations
        +Decimal hourly_rate
        +String profile_image_url
        +String intro_video_url
        +Boolean is_approved
        +Boolean is_visible
        +Int experience_years
        +Float average_rating
        +DateTime created_at
        +DateTime updated_at
        +AvailabilitySlot[] availabilitySlots
        +Booking[] bookingsAsTeacher
        +Review[] reviewsReceived
        +Course[] coursesAuthored
        +DigitalResource[] resourcesAuthored
        +CorporatePackage[] corporatePackagesTaught
        +ConsultationBooking[] consultationsGiven
    }

    class Student {
        +String id (PK)
        +String learningGoals
        +String proficiencyLevel
        +String[] favoriteTeacherIds
        +DateTime created_at
        +DateTime updated_at
        +Booking[] bookingsAsStudent
        +Review[] reviewsWritten
        +CourseEnrollment[] courseEnrollments
        +DigitalResourcePurchase[] resourcePurchases
        +ConsultationBooking[] consultationsReceived
    }

    class AvailabilitySlot {
        +String id (PK)
        +String teacherId (FK)
        +DateTime startTime
        +DateTime endTime
        +Boolean isBooked
        +Booking booking
    }

    class Booking {
        +String id (PK)
        +String studentId (FK)
        +String teacherId (FK)
        +String availabilitySlotId (FK)
        +String status
        +DateTime lessonTime
        +Int durationMinutes
        +Decimal pricePaid
        +Decimal commissionRate // O anki komisyon oranı
        +Decimal commissionFee
        +DateTime created_at
        +DateTime updated_at
        +Payment payment
        +VideoSession videoSession
        +Review review
    }

    class Payment {
        +String id (PK)
        +String bookingId (FK)
        +String stripePaymentIntentId
        +String paypalOrderId // MVP Sonrası
        +String iyzicoPaymentId // MVP Sonrası
        +Decimal amount
        +String currency
        +String status
        +DateTime created_at
    }

    class VideoSession {
        +String id (PK)
        +String bookingId (FK)
        +String videoProvider // "zoom", "videosdk", "dyte"
        +String meetingId
        +String startUrl (Teacher)
        +String joinUrl (Student)
        +DateTime scheduledStartTime
    }

    class Review {
      +String id (PK)
      +String bookingId (FK)
      +String studentId (FK)
      +String teacherId (FK)
      +Int rating // (e.g., 1-5 stars)
      +String comment
      +DateTime createdAt
    }

    %% --- Post-MVP & Ecosystem Entities ---

    class Course { // Offline Video Kurslar
        +String id (PK)
        +String teacherId (FK)
        +String title
        +String description
        +String level
        +Decimal price
        +String[] topics
        +String courseImageUrl
        +String videoPlatform // "youtube", "mux", "cloudinary"
        +Boolean isPublished
        +DateTime createdAt
        +CourseModule[] modules
        +CourseEnrollment[] enrollments
    }

    class CourseModule {
        +String id (PK)
        +String courseId (FK)
        +String title
        +Int order
        +CourseLesson[] lessons
    }

    class CourseLesson {
        +String id (PK)
        +String moduleId (FK)
        +String title
        +String videoUrl // YouTube linki veya Mux/Cloudinary ID
        +String description
        +Int order
        +DigitalResource[] attachedResources
    }

     class CourseEnrollment {
        +String id (PK)
        +String studentId (FK)
        +String courseId (FK)
        +DateTime enrolledAt
        +String status // e.g., IN_PROGRESS, COMPLETED
        +Float progressPercentage
    }

    class DigitalResource { // Eğitim Materyalleri
        +String id (PK)
        +String teacherId (FK) // veya platform admin
        +String title
        +String description
        +String fileUrl // Supabase Storage
        +String fileType // PDF, EBOOK, FLASHCARD
        +Decimal price
        +Boolean isFreeWithAds
        +DateTime createdAt
        +DigitalResourcePurchase[] purchases
    }

     class DigitalResourcePurchase {
        +String id (PK)
        +String studentId (FK)
        +String resourceId (FK)
        +Decimal pricePaid
        +DateTime purchasedAt
    }

    class DictionaryEntry { // Almanca-Türkçe Sözlük
        +String id (PK)
        +String germanWord
        +String turkishTranslation
        +String partOfSpeech
        +String exampleSentenceGerman
        +String exampleSentenceTurkish
    }

    class CorporatePackage { // Kurumsal Paketler
        +String id (PK)
        +String companyName
        +Int numberOfEmployees
        +Decimal monthlyPrice
        +String teacherId (FK) // Atanan öğretmen
        +DateTime startDate
        +DateTime endDate
    }

    class TranslationRequest { // Çeviri Hizmetleri
        +String id (PK)
        +String userId // Talep eden kullanıcı (Student veya Teacher olabilir)
        +String serviceType // DOCUMENT, SIMULTANEOUS, LOCALIZATION
        +String sourceLanguage
        +String targetLanguage
        +String fileUrl // Belge çevirisi için
        +String description
        +String status
        +Decimal estimatedCost
        +Decimal finalCost
        +DateTime createdAt
    }

    class ConsultationBooking { // Danışmanlık
        +String id (PK)
        +String studentId (FK) // Danışmanlık alan
        +String teacherId (FK) // Danışmanlık veren (veya ayrı Consultant modeli)
        +String topic // BUSINESS_GERMAN, MARKET_ENTRY
        +DateTime consultationTime
        +Int durationMinutes
        +Decimal price
        +String status
        +DateTime createdAt
    }

     class Notification {
        +String id (PK)
        +String userId (FK)
        +String type // NEW_BOOKING, LESSON_REMINDER, REVIEW_REQUEST etc.
        +String message
        +String linkUrl
        +Boolean isRead
        +DateTime createdAt
    }

    %% --- Relationships ---

    Teacher "1" -- "*" AvailabilitySlot : manages
    Teacher "1" -- "*" Booking : teaches
    Teacher "1" -- "*" Review : receives
    Teacher "1" -- "*" Course : authors
    Teacher "1" -- "*" DigitalResource : authors
    Teacher "1" -- "*" CorporatePackage : teaches
    Teacher "1" -- "*" ConsultationBooking : gives

    Student "1" -- "*" Booking : books
    Student "1" -- "*" Review : writes
    Student "1" -- "*" CourseEnrollment : enrollsIn
    Student "1" -- "*" DigitalResourcePurchase : purchases
    Student "1" -- "*" ConsultationBooking : receives

    AvailabilitySlot "1" -- "1" Booking : isFor
    Booking "1" -- "1" Payment : has
    Booking "1" -- "1" VideoSession : has
    Booking "1" -- "1" Review : isReviewedBy

    Course "1" -- "*" CourseModule : has
    Course "1" -- "*" CourseEnrollment : hasEnrollment
    CourseModule "1" -- "*" CourseLesson : has
    CourseLesson "1" -- "*" DigitalResource : attaches

    DigitalResource "1" -- "*" DigitalResourcePurchase : isPurchased

    User "1" -- "*" Notification : receives // Assuming User model exists conceptually via Clerk