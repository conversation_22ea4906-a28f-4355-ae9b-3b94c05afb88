"use client";

import { useState, useEffect } from "react";
import { loadStripe } from "@stripe/stripe-js";
import {
  Elements,
  CardElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { createPaymentIntent } from "@/lib/actions/payment.actions";
import { Loader2, CreditCard, Shield, Lock } from "lucide-react";

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface PaymentFormProps {
  bookingId: string;
  amount: number;
  currency?: string;
  onSuccess: () => void;
  onError: (error: string) => void;
}

const CheckoutForm = ({ bookingId, amount, currency = "try", onSuccess, onError }: PaymentFormProps) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);

  // PaymentIntent oluştur
  useEffect(() => {
    const initializePayment = async () => {
      try {
        const result = await createPaymentIntent({
          bookingId,
          amount,
          currency,
        });

        if (result.success && result.clientSecret) {
          setClientSecret(result.clientSecret);
        } else {
          onError(result.error || "Ödeme başlatılamadı");
        }
      } catch (error) {
        onError("Ödeme başlatılırken bir hata oluştu");
      }
    };

    initializePayment();
  }, [bookingId, amount, currency, onError]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements || !clientSecret) {
      return;
    }

    setIsLoading(true);

    const cardElement = elements.getElement(CardElement);

    if (!cardElement) {
      setIsLoading(false);
      return;
    }

    try {
      const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
        },
      });

      if (error) {
        onError(error.message || "Ödeme işlemi başarısız");
      } else if (paymentIntent && paymentIntent.status === "succeeded") {
        toast({
          title: "Ödeme Başarılı!",
          description: "Ders rezervasyonunuz onaylandı.",
        });
        onSuccess();
      }
    } catch (error) {
      onError("Ödeme işlemi sırasında bir hata oluştu");
    } finally {
      setIsLoading(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: "16px",
        color: "#424770",
        "::placeholder": {
          color: "#aab7c4",
        },
        fontFamily: "system-ui, -apple-system, sans-serif",
      },
      invalid: {
        color: "#9e2146",
      },
    },
    hidePostalCode: true,
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="w-5 h-5" />
          Ödeme Bilgileri
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Güvenlik Bilgisi */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Güvenli Ödeme</span>
            </div>
            <p className="text-xs text-blue-700">
              Ödeme bilgileriniz SSL şifreleme ile korunmaktadır. Kart bilgileriniz sunucularımızda saklanmaz.
            </p>
          </div>

          {/* Ödeme Tutarı */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="font-medium">Toplam Tutar:</span>
              <span className="text-lg font-bold text-green-600">
                {amount.toFixed(2)} {currency.toUpperCase()}
              </span>
            </div>
          </div>

          {/* Kart Bilgileri */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Kart Bilgileri
            </label>
            <div className="p-3 border border-gray-300 rounded-md bg-white">
              <CardElement options={cardElementOptions} />
            </div>
          </div>

          {/* Güvenlik Notları */}
          <div className="flex items-start gap-2 text-xs text-gray-600">
            <Lock className="w-3 h-3 mt-0.5 text-gray-400" />
            <div>
              <p>• Ödemeniz Stripe güvenli ödeme sistemi ile işlenir</p>
              <p>• Kart bilgileriniz şifrelenerek korunur</p>
              <p>• İptal durumunda ücret iade edilir</p>
            </div>
          </div>

          {/* Ödeme Butonu */}
          <Button
            type="submit"
            disabled={!stripe || !clientSecret || isLoading}
            className="w-full bg-green-600 hover:bg-green-700"
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Ödeme İşleniyor...
              </>
            ) : (
              <>
                <CreditCard className="w-4 h-4 mr-2" />
                {amount.toFixed(2)} {currency.toUpperCase()} Öde
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default function PaymentForm(props: PaymentFormProps) {
  return (
    <Elements stripe={stripePromise}>
      <CheckoutForm {...props} />
    </Elements>
  );
}
