'use client';

import React from 'react';

interface Course {
  id: number;
  title: string;
  image: string;
  level: string;
  duration: string;
  students: number;
  lessons: number;
  rating: number;
  price: number;
  description: string;
}

export default function CourseSchema({ courses }: { courses: Course[] }) {
  const courseSchema = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "itemListElement": courses.map((course, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "Course",
        "name": course.title,
        "description": course.description,
        "provider": {
          "@type": "Organization",
          "name": "AlmancaABC",
          "sameAs": "https://almancaabc.com"
        },
        "offers": {
          "@type": "Offer",
          "price": course.price,
          "priceCurrency": "TRY",
          "availability": "https://schema.org/InStock"
        },
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": course.rating,
          "bestRating": "5",
          "ratingCount": String(Math.round(course.students * 0.8)) // Öğrencilerin yaklaşık %80'i değerlendirme yapmış
        },
        "audience": {
          "@type": "Audience",
          "audienceType": `Almanca ${course.level} seviye`
        },
        "educationalCredentialAwarded": "Almanca Dil Sertifikası",
        "timeRequired": `P${course.duration.split(' ')[0]}W` // ISO 8601 formatında süre (örn. P8W = 8 hafta)
      }
    }))
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(courseSchema)
      }}
    />
  );
}