"use client" // Yo<PERSON> kaldırıldı, en başa alındı

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
// import { Badge } from "@/components/ui/badge" // Kullanılmıyor, kaldırıldı
import { CheckCircle } from "lucide-react" // Clock kaldırıldı
import type { TeacherSchedule as TeacherScheduleType } from "@/types/teacher"
import { format, addDays, isSameDay } from "date-fns"
import { tr } from "date-fns/locale"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { motion, AnimatePresence } from "framer-motion"

interface TeacherScheduleProps {
  schedule: TeacherScheduleType
  // hourlyRate: number // Kullanılmıyor, kaldırıldı
  teacherId: string
  // onSlotSelect?: (date: Date, time: string) => void;
}

// hourlyRate parametresi kaldırıldı
export function TeacherSchedule({ schedule, teacherId }: TeacherScheduleProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined)
  const [selectedTime, setSelectedTime] = useState<string | null>(null)
  const [availableDates, setAvailableDates] = useState<Date[]>([])

  // Müsait günlere göre tıklanabilir tarihleri hesapla
  useEffect(() => {
    // schedule ve gerekli özelliklerin varlığını kontrol et
    if (!schedule || !schedule.availableDays || !schedule.availableHours) {
      setAvailableDates([]); // Eğer schedule geçersizse boş dizi ata
      setSelectedDate(undefined);
      setSelectedTime(null);
      return; // useEffect'den erken çık
    }

    const dates: Date[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Sadece tarihi karşılaştır

    // Önümüzdeki 30 günü kontrol et
    for (let i = 0; i < 30; i++) {
      const currentDate = addDays(today, i);
      const dayName = format(currentDate, "EEEE", { locale: tr }); // Türkçe gün adı

      // Öğretmenin o gün müsait olup olmadığını ve o güne ait saat dilimi olup olmadığını kontrol et (Kontrol eklendiği için artık güvenli)
      if (schedule.availableDays.includes(dayName) && schedule.availableHours[dayName]?.length > 0) {
        dates.push(currentDate);
      }
    }
    setAvailableDates(dates);

    // Seçili tarihi temizle (program değiştiğinde)
    setSelectedDate(undefined);
    setSelectedTime(null);

  }, [schedule]); // schedule değiştiğinde yeniden hesapla

  const handleBooking = () => {
    if (!selectedDate || !selectedTime) return;
    // TODO: Gerçek rezervasyon mantığını buraya ekle (API call vb.)
    alert(
      `${format(selectedDate, "d MMMM yyyy", { locale: tr })} tarihinde saat ${selectedTime} için ders rezervasyonu isteği gönderildi (Öğretmen ID: ${teacherId}).`
    );
  }

  const getAvailableTimesForDate = (date: Date | undefined): string[] => {
    if (!date) return [];
    const dayName = format(date, "EEEE", { locale: tr });
    // schedule ve availableHours kontrolü ekle
    return schedule?.availableHours?.[dayName] || [];
  }

  const isDateAvailable = (date: Date): boolean => {
     return availableDates.some((availableDate) => isSameDay(date, availableDate));
  }

  return (
    <Card className="overflow-hidden">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Ders Programı</span>
          {/* Fiyat Badge'i kaldırıldı */}
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="grid grid-cols-1 md:grid-cols-2">
          {/* Takvim Alanı */}
          <div className="p-4 border-b md:border-b-0 md:border-r border-gray-100 flex justify-center"> {/* Kenarlık rengi açıldı */}
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={(date) => {
                  setSelectedDate(date);
                  setSelectedTime(null); // Yeni tarih seçildiğinde saati sıfırla
              }}
              disabled={(date) => {
                  const today = new Date();
                  today.setHours(0,0,0,0);
                  return date < today || !isDateAvailable(date); // Geçmiş tarihleri ve müsait olmayanları disable et
              }}
              className="rounded-md"
              locale={tr}
            />
          </div>
          {/* Saat Seçim Alanı */}
          <div className="p-4 min-h-[300px]">
            <AnimatePresence mode="wait">
              {selectedDate ? (
                <motion.div
                  key={selectedDate.toISOString()}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  <h4 className="font-medium mb-4 text-center md:text-left">
                     {format(selectedDate, "d MMMM yyyy, EEEE", { locale: tr })}
                  </h4>
                  <ScrollArea className="h-48 rounded-md border p-4 mb-4">
                    {getAvailableTimesForDate(selectedDate).length > 0 ? (
                       <div className="grid grid-cols-3 gap-2">
                         {getAvailableTimesForDate(selectedDate).map((time) => (
                           <TooltipProvider key={time} delayDuration={100}>
                             <Tooltip>
                               <TooltipTrigger asChild>
                                 <Button
                                   variant={selectedTime === time ? "default" : "outline"}
                                   size="sm"
                                   onClick={() => setSelectedTime(time)}
                                   className={`w-full ${selectedTime === time ? "ring-2 ring-primary ring-offset-2" : ""}`}
                                 >
                                   {time}
                                 </Button>
                               </TooltipTrigger>
                               <TooltipContent>
                                 <p>{selectedTime === time ? "Seçili saat" : "Müsait saat"}</p>
                               </TooltipContent>
                             </Tooltip>
                           </TooltipProvider>
                         ))}
                       </div>
                    ) : (
                         <p className="text-sm text-muted-foreground text-center h-full flex items-center justify-center">Bu tarih için müsait saat bulunmamaktadır.</p>
                    )}
                  </ScrollArea>
                   <Button className="w-full" disabled={!selectedTime} onClick={handleBooking}>
                     {selectedTime ? (
                       <>
                         <CheckCircle className="w-4 h-4 mr-2" />
                         {selectedTime} için Rezervasyon Yap
                       </>
                     ) : (
                       "Saat Seçin"
                     )}
                   </Button>
                   {selectedTime && (
                     <p className="text-xs text-muted-foreground text-center mt-2">
                       * Rezervasyon ücretsizdir ve iptal edilebilir
                     </p>
                   )}
                </motion.div>
              ) : (
                <motion.div
                  key="select-date"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="h-full flex items-center justify-center text-center text-muted-foreground"
                >
                  Lütfen takvimden müsait bir tarih seçin
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
