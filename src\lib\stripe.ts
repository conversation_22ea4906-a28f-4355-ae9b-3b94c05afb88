import Stripe from 'stripe';
import { loadStripe } from '@stripe/stripe-js';

// Server-side Stripe instance
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
});

// Client-side Stripe instance
let stripePromise: Promise<Stripe | null>;

export const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
  }
  return stripePromise;
};

// Stripe configuration constants
export const STRIPE_CONFIG = {
  currency: 'try', // Turkish Lira
  payment_method_types: ['card'],
  mode: 'payment',
  success_url: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
  cancel_url: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/payment/cancel`,
} as const;

// Commission rate (platform fee)
export const COMMISSION_RATE = 0.15; // 15% commission

// Helper function to calculate commission
export const calculateCommission = (amount: number): number => {
  return Math.round(amount * COMMISSION_RATE);
};

// Helper function to calculate teacher's share
export const calculateTeacherShare = (amount: number): number => {
  return amount - calculateCommission(amount);
};

// Convert amount to Stripe format (cents)
export const toStripeAmount = (amount: number): number => {
  return Math.round(amount * 100);
};

// Convert from Stripe format to regular amount
export const fromStripeAmount = (amount: number): number => {
  return amount / 100;
};
