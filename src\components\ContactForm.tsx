'use client';

import React, { useEffect, useActionState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod'; // z importu eklendi
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox'; // Checkbox importu eklendi
import { toast } from 'sonner';
import { contactFormSchema, type ContactFormValues } from '@/lib/formValidationSchemas';
import { sendContactMessage } from '@/lib/actions/contact.actions';

const ContactForm = () => {
  const initialState = { success: false, message: '', error: null, errors: [] as z.ZodIssue[] };
  const [state, formAction] = useActionState(sendContactMessage, initialState);

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: '',
      privacyPolicyAccepted: false,
    },
  });

  useEffect(() => {
    if (state?.success && state.message) {
      toast.success(state.message);
      form.reset(); // Formu sıfırla
    } else if (!state?.success && state?.message) { // Genel bir hata mesajı (örn: "Lütfen formdaki hataları düzeltin")
      toast.error(state.message);
    } else if (state?.error) { // Daha spesifik bir backend hatası
        toast.error(state.error);
    }

    // Alan bazlı hataları form.setError ile set etmeye gerek yok,
    // FormMessage component'i react-hook-form'un kendi error state'inden besleniyor.
    // Server'dan dönen errors varsa ve bunları göstermek istiyorsak,
    // bu hataları manuel olarak form field'larına set edebiliriz.
    // Ancak Zod resolver client-side'da zaten bu işi yapıyor.
    // Server action'dan dönen 'errors' (ZodIssue[]) varsa, bunları göstermek için:
    if (state?.errors && state.errors.length > 0) {
      state.errors.forEach((issue) => {
        // path[0] her zaman string olmayabilir, kontrol ekleyelim
        const fieldName = issue.path[0];
        if (typeof fieldName === 'string') {
          form.setError(fieldName as keyof ContactFormValues, {
            type: 'server',
            message: issue.message,
          });
        }
      });
    }
  }, [state, form]);

  return (
    <Form {...form}>
      {/* <form onSubmit={form.handleSubmit(() => formAction(new FormData(form.control._formValues)))} className="space-y-6"> */}
      <form
        action={formAction}
        // onSubmit içindeki formData oluşturma ve yorumlar kaldırıldı.
        // useActionState ve action prop'u form gönderimini zaten yönetiyor.
        // Client-side validasyon react-hook-form tarafından resolver ile hallediliyor.
        // Gerekirse form.handleSubmit ile sarılabilir, ancak useActionState ile bu genellikle gerekmez.
        className="space-y-6"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Adınız Soyadınız *</FormLabel>
              <FormControl>
                <Input placeholder="Adınız Soyadınız" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>E-posta Adresiniz *</FormLabel>
              <FormControl>
                <Input type="email" placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Telefon Numaranız (İsteğe Bağlı)</FormLabel>
              <FormControl>
                <Input type="tel" placeholder="+90 5XX XXX XX XX" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="subject"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Konu *</FormLabel>
              <FormControl>
                <Input placeholder="Mesajınızın konusu" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Mesajınız *</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Mesajınızı buraya yazın..."
                  className="min-h-[120px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="privacyPolicyAccepted"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 shadow">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  name="privacyPolicyAccepted" // Server action'ın alabilmesi için name attribute'ü önemli
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>
                  <a href="/gizlilik-politikasi" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">Gizlilik Politikası</a>&apos;nı okudum ve kabul ediyorum. *
                </FormLabel>
                <FormMessage />
              </div>
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full" disabled={form.formState.isSubmitting}>
          {form.formState.isSubmitting ? 'Gönderiliyor...' : 'Mesajı Gönder'}
        </Button>
      </form>
    </Form>
  );
};

export default ContactForm;