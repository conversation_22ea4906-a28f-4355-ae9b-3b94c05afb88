// src/components/dashboard/ChildrenScheduleCard.tsx
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { getUpcomingLessonsForChildren } from "@/lib/actions/parent.actions"; // Yeni action import edildi
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { CalendarDays } from "lucide-react"; // İkon değiştirildi

// Action'dan dönen tipe uygun bir tip
// Prisma.Booking & { studentName: string, teacherName: string, teacherProfileImageUrl: string | null } gibi birleştirilebilir
type UpcomingLessonChild = {
    id: string;
    studentId: string;
    teacherId: string;
    lessonTime: Date;
    durationMinutes: number;
    status: string;
    studentName: string; // Action'da eklendi
    teacherName: string; // Action'da eklendi
    teacherProfileImageUrl: string | null; // Action'da eklendi
}

export async function ChildrenScheduleCard() {
  // Yetkilendirme kontrolü action içinde yapılıyor varsayımı
  const upcomingLessons: UpcomingLessonChild[] = await getUpcomingLessonsForChildren(5); // Sonraki 5 ders (tüm çocuklar için toplam)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
            <CalendarDays className="h-5 w-5" />
            Çocukların Yaklaşan Dersleri
        </CardTitle>
        <CardDescription>
          Çocuklarınızın sonraki {upcomingLessons.length} dersi.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {upcomingLessons.length === 0 ? (
          <p className="text-sm text-muted-foreground">Yaklaşan ders bulunmuyor.</p>
        ) : (
          upcomingLessons.map((lesson) => (
            <div key={lesson.id} className="flex items-center gap-4 border-b pb-3 last:border-b-0 last:pb-0">
              {/* Öğretmen Avatarı */}
              <Avatar className="hidden h-9 w-9 sm:flex">
                <AvatarImage src={lesson.teacherProfileImageUrl ?? `https://avatar.vercel.sh/${lesson.teacherId}.png`} alt="Öğretmen Avatar" />
                <AvatarFallback>{lesson.teacherName?.[0]}</AvatarFallback>
              </Avatar>
              <div className="grid gap-1 flex-1">
                 <p className="text-sm font-medium leading-none">
                   {lesson.studentName} - {lesson.teacherName} {/* Öğrenci ve Öğretmen */}
                 </p>
                <p className="text-xs text-muted-foreground">
                    {format(new Date(lesson.lessonTime), "dd MMMM yyyy HH:mm", { locale: tr })} ({lesson.durationMinutes} dk)
                </p>
              </div>
               {/* TODO: Derse katılma linki veli için gerekli mi? Belki detay linki? */}
               <Button variant="outline" size="sm" asChild>
                 <Link href={`/parent/bookings/${lesson.id}`}>Detay</Link>
               </Button>
            </div>
          ))
        )}
         {upcomingLessons.length > 0 && (
             <Button asChild size="sm" className="mt-4 w-full" variant="outline">
                {/* TODO: Veli takvim sayfasına link */}
                <Link href="/parent/schedule">Tüm Programı Gör</Link>
             </Button>
         )}
      </CardContent>
    </Card>
  );
}