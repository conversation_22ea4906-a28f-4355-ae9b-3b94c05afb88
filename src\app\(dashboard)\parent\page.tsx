// src/app/(dashboard)/parent/page.tsx
import { ChildrenScheduleCard } from "@/components/dashboard/ChildrenScheduleCard"; // Yeni bileşen import edildi
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"; // Diğer kartlar için
import { ClipboardCheck, Parentheses } from "lucide-react"; // Kullanılmayan Users ikonu kaldırıldı
import Link from "next/link"; // Link için
import { Button } from "@/components/ui/button"; // Button için

// Örnek veriler kaldırıldı

export default function ParentDashboardPage() {
  return (
    <div className="flex flex-col gap-6">
      <h1 className="text-2xl font-semibold">Veli Paneli</h1>

       {/* Widget'lar */}
       <div className="grid gap-6 lg:grid-cols-3"> {/* Layout düzenlendi */}

         {/* Çocukların Yakla<PERSON><PERSON>rt<PERSON> (Daha geni<PERSON> alan kap<PERSON>n) */}
         <div className="lg:col-span-2">
            <ChildrenScheduleCard />
         </div>

         {/* Diğ<PERSON> (Yer Tutucu) */}
         <div className="space-y-6">
             {/* TODO: Kayıtlı Çocuklar Kartı */}
             <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Kayıtlı Çocuklarım</CardTitle>
                    <Parentheses className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    {/* TODO: Gerçek çocuk sayısını action ile çek */}
                    <div className="text-2xl font-bold">?</div> {/* Örnek Veri kaldırıldı */}
                    {/* TODO: Çocukları yönetme linki */}
                     <Button asChild size="sm" className="mt-2 w-full" variant="outline">
                        <Link href="/parent/children">Çocukları Yönet</Link>
                     </Button>
                </CardContent>
             </Card>
              {/* TODO: Yaklaşan Sınav/Ödev Kartı */}
             <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Yaklaşan Sınav/Ödev</CardTitle>
                    <ClipboardCheck className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    <p className="text-xs text-muted-foreground">Çocukların yaklaşan sınav/ödevleri buraya gelecek.</p>
                </CardContent>
             </Card>
         </div>
      </div>
    </div>
  );
}