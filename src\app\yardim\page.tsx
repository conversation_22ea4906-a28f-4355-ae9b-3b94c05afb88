'use client'; // Bu satır eklendi

import Link from 'next/link';
import { HelpCircle, FileText, ShieldCheck, Users, BookOpen, MessageSquarePlus } from 'lucide-react'; // MessageSquarePlus eklendi
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ModernContactForm } from '@/app/iletisim/page'; // Yeni formu import ediyoruz
import { IletisimBilgileriKarti } from '@/components/IletisimBilgileriKarti'; // İletişim bilgilerini kartını import ediyoruz
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
} from "@/components/ui/dialog";
import { motion } from "framer-motion"; // motion import edildi

const YardimPage = () => {
  const helpTopics = [
    {
      title: "Sık Sorulan <PERSON>ru<PERSON>",
      description: "Platformumuz ve hizmetlerimiz hakkında en çok merak edilen soruların yanıtları.",
      link: "/sss",
      icon: <HelpCircle className="h-8 w-8 text-blue-600" />,
    },
    {
      title: "Kullanım Koşulları",
      description: "AlmancaABC platformunun kullanımına ilişkin şartlar ve koşullar.",
      link: "/kullanim-kosullari",
      icon: <FileText className="h-8 w-8 text-green-600" />,
    },
    {
      title: "Üyelik Sözleşmesi",
      description: "Platformumuza üye olurken kabul ettiğiniz sözleşme detayları.",
      link: "/uyelik-sozlesmesi",
      icon: <Users className="h-8 w-8 text-purple-600" />,
    },
    {
      title: "Gizlilik Politikası",
      description: "Kişisel verilerinizin nasıl toplandığını, kullanıldığını ve korunduğunu açıklar.",
      link: "/gizlilik-politikasi",
      icon: <ShieldCheck className="h-8 w-8 text-red-600" />,
    },
    {
      title: "KVKK Aydınlatma Metni",
      description: "Kişisel Verilerin Korunması Kanunu kapsamında bilgilendirme metnimiz.",
      link: "/kvkk-aydinlatma-metni",
      icon: <BookOpen className="h-8 w-8 text-yellow-600" />,
    },
  ];

  return (
    <div className="container mx-auto py-12 px-4 md:px-6">
      <header className="text-center mb-12">
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl">
          Yardım Merkezi
        </h1>
        <p className="mt-4 text-xl text-gray-600 dark:text-gray-300">
          AlmancaABC platformuyla ilgili her türlü sorunuz için buradayız.
        </p>
      </header>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {helpTopics.map((topic) => (
          <Card key={topic.title} className="hover:shadow-lg transition-shadow duration-300">
            <CardHeader className="flex flex-row items-center space-x-4 pb-2">
              {topic.icon}
              <CardTitle className="text-xl font-semibold">{topic.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">{topic.description}</p>
              <Button asChild variant="outline" className="w-full">
                <Link href={topic.link}>Daha Fazla Bilgi</Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* İletişim Formu (Popup) Bölümü - Ana sayfadan kopyalandı */}
      <section className="py-8 md:py-12 bg-slate-50 dark:bg-slate-900 mt-16 rounded-lg">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Bizimle İletişime Geçin
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6 max-w-xl mx-auto">
              Sorularınız mı var veya yardıma mı ihtiyacınız var? Hızlıca mesaj gönderebilir veya diğer tüm iletişim seçenekleri için <Link href="/iletisim" className="text-sky-600 hover:underline dark:text-sky-400">iletişim sayfamızı</Link> ya da <Link href="/yardim" className="text-sky-600 hover:underline dark:text-sky-400">yardım merkezimizi</Link> ziyaret edebilirsiniz.
            </p>
            <Dialog>
              <DialogTrigger asChild>
                <Button size="lg" className="bg-primary hover:bg-primary/90 text-white dark:bg-sky-600 dark:hover:bg-sky-700">
                  <MessageSquarePlus className="mr-2 h-5 w-5" />
                  Mesaj Gönder
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-4xl dark:bg-slate-800 p-0">
                <DialogHeader className="p-6 pb-4">
                  <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-gray-100">Bize Ulaşın</DialogTitle>
                  <DialogDescription className="text-sm text-gray-600 dark:text-gray-300">
                    Aşağıdaki formu doldurarak veya iletişim bilgilerimizi kullanarak bize ulaşabilirsiniz.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-1 lg:grid-cols-5 overflow-hidden rounded-b-lg">
                  <IletisimBilgileriKarti />
                  <div className="p-6 md:p-8 lg:col-span-3 bg-white dark:bg-slate-800">
                    <ModernContactForm />
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default YardimPage;