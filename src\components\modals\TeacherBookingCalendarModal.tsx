"use client";

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ChevronLeft, ChevronRight, Calendar, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Teacher } from '@/types/teacher';

interface SelectedCourseType {
  title: string;
  price: string | number;
}

interface BookingCalendarProps {
  onClose: () => void;
  selectedCourse: SelectedCourseType | null;
  teacher: Teacher | null;
  onBookingSuccess: () => void;
}

export function TeacherBookingCalendarModal({ onClose, selectedCourse, teacher, onBookingSuccess }: BookingCalendarProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [step, setStep] = useState(1); // 1: Select date, 2: Select time, 3: Confirm
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    }

    document.body.classList.add('modal-open-background-scroll');
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.body.classList.remove('modal-open-background-scroll');
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  const handleBookingSuccess = () => {
    onClose();
    onBookingSuccess();
  };

  if (!teacher) {
    return null;
  }

  // Takvim yardımcı fonksiyonları
  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const isPastDate = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  const isUnavailable = (date: Date) => {
    // Örnek: Pazartesi ve Çarşamba günleri müsait değil
    const day = date.getDay();
    return day === 1 || day === 3;
  };

  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = { day: 'numeric', month: 'long', year: 'numeric' };
    return date.toLocaleDateString('tr-TR', options);
  };

  const availableTimes = ['09:00', '10:30', '13:00', '14:30', '16:00', '17:30', '19:00', '20:30'];

  const prevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };

  const nextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
    setStep(3);
  };

  const handleDateSelect = (date: Date) => {
    if (isPastDate(date) || isUnavailable(date)) return;
    setSelectedDate(date);
    setSelectedTime(null);
  };

  const handleConfirmBooking = () => {
    if (selectedDate && selectedTime) {
      // Burada rezervasyon işlemi yapılacak
      handleBookingSuccess();
    }
  };

  const renderCalendar = () => {
    const daysInMonth = getDaysInMonth(currentMonth);
    const firstDay = getFirstDayOfMonth(currentMonth);
    const days = [];

    // Boş günler (önceki aydan)
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="h-10"></div>);
    }

    // Ayın günleri
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
      const isSelected = selectedDate &&
        selectedDate.getDate() === day &&
        selectedDate.getMonth() === currentMonth.getMonth() &&
        selectedDate.getFullYear() === currentMonth.getFullYear();
      const isPast = isPastDate(date);
      const isNotAvailable = isUnavailable(date);

      days.push(
        <motion.button
          key={day}
          onClick={() => handleDateSelect(date)}
          disabled={isPast || isNotAvailable}
          className={`
            h-10 w-10 rounded-lg text-sm font-medium transition-all duration-200
            ${isSelected
              ? 'bg-blue-500 text-white shadow-md'
              : isPast || isNotAvailable
                ? 'text-gray-300 cursor-not-allowed'
                : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600'
            }
          `}
          whileHover={!isPast && !isNotAvailable ? { scale: 1.1 } : {}}
          whileTap={!isPast && !isNotAvailable ? { scale: 0.95 } : {}}
        >
          {day}
        </motion.button>
      );
    }

    return days;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <AnimatePresence>
        <motion.div
          ref={modalRef}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className="bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden"
        >
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900">
              {teacher.name} ile Ders Rezervasyonu
            </h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </Button>
          </div>

          <div className="p-6">
            {selectedCourse && (
              <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-gray-600">
                  Seçilen kurs: <span className="font-medium text-blue-600">{selectedCourse.title}</span>
                </p>
              </div>
            )}

            {/* Takvim */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Tarih Seçin</h3>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={prevMonth}>
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  <span className="text-sm font-medium min-w-[120px] text-center">
                    {currentMonth.toLocaleDateString('tr-TR', { month: 'long', year: 'numeric' })}
                  </span>
                  <Button variant="outline" size="sm" onClick={nextMonth}>
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-7 gap-1 mb-2">
                {['Paz', 'Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cmt'].map((day) => (
                  <div key={day} className="h-8 flex items-center justify-center text-xs font-medium text-gray-500">
                    {day}
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-7 gap-1">
                {renderCalendar()}
              </div>

              {selectedDate && step === 1 && !isPastDate(selectedDate) && !isUnavailable(selectedDate) && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="text-sm font-semibold text-gray-900 mb-2 text-center">
                    {formatDate(selectedDate)} - Uygun Saatler:
                  </h4>
                  <div className="grid grid-cols-4 gap-2">
                    {availableTimes.map((time, index) => (
                      <motion.button
                        key={index}
                        onClick={() => handleTimeSelect(time)}
                        className={`
                          py-2 px-2 rounded text-center text-xs font-medium transition-all duration-200
                          ${selectedTime === time
                            ? 'bg-blue-500 text-white shadow-sm'
                            : 'bg-white border border-gray-200 text-gray-700 hover:border-blue-300 hover:bg-blue-50'
                          }
                        `}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {time}
                      </motion.button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Onay Adımı */}
            {step === 3 && selectedDate && selectedTime && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 bg-green-50 rounded-lg border border-green-200"
              >
                <div className="flex items-center gap-3 mb-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <h4 className="font-semibold text-green-800">Rezervasyon Onayı</h4>
                </div>
                <div className="text-sm text-green-700 space-y-1">
                  <p><strong>Öğretmen:</strong> {teacher.name}</p>
                  <p><strong>Tarih:</strong> {formatDate(selectedDate)}</p>
                  <p><strong>Saat:</strong> {selectedTime}</p>
                  {selectedCourse && <p><strong>Kurs:</strong> {selectedCourse.title}</p>}
                </div>
                <div className="flex gap-3 mt-4">
                  <Button onClick={handleConfirmBooking} className="flex-1">
                    Rezervasyonu Onayla
                  </Button>
                  <Button variant="outline" onClick={() => setStep(1)} className="flex-1">
                    Değiştir
                  </Button>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
