"use client";

import React from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { getTeacherBookingsForCalendar } from "@/lib/actions/teacher.actions"; // Action import edildi
import { EventInput } from "@fullcalendar/core";
import { toast } from "sonner"; // Hata gösterimi için toast eklendi

export function TeacherCalendarSummary() {
  const [events, setEvents] = React.useState<EventInput[]>([]);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    async function fetchBookings() {
      setLoading(true);
      try {
        const bookings = await getTeacherBookingsForCalendar();
        setEvents(bookings);
      } catch (error) {
        // console.error("Error fetching bookings for calendar:", error); - REMOVED
        toast.error("Takvim verileri yüklenirken bir hata oluştu.");
        setEvents([]); // Hata durumunda boş dizi
      } finally {
        setLoading(false);
      }
    }
    fetchBookings();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Ders Takvimi Özeti</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[600px] flex items-center justify-center">
            <p>Takvim yükleniyor...</p> {/* Shadcn Skeleton eklenebilir */}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Ders Takvimi Özeti</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[600px]"> {/* Takvim için sabit yükseklik */}
          <FullCalendar
            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
            initialView="timeGridWeek" // Haftalık görünüm
            headerToolbar={{
              left: "prev,next today",
              center: "title",
              right: "dayGridMonth,timeGridWeek,timeGridDay",
            }}
            events={events} // Gerçek veri state'i kullanılıyor
            editable={false} // Öğretmen takviminde sürükle bırak olmasın (şimdilik)
            selectable={false}
            selectMirror={true}
            dayMaxEvents={true}
            weekends={true}
            locale="tr" // Türkçe yerelleştirme
            buttonText={{
              today: "Bugün",
              month: "Ay",
              week: "Hafta",
              day: "Gün",
              list: "Liste",
            }}
            allDaySlot={false} // Tüm gün etkinlikleri slotunu gizle
            slotMinTime="08:00:00" // Takvim başlangıç saati
            slotMaxTime="22:00:00" // Takvim bitiş saati
            // eventClick={(clickInfo) => {
            //   alert(\`Ders: \${clickInfo.event.title}\nÖğrenci: \${clickInfo.event.extendedProps.studentName}\`);
            // }}
            height="100%" // Parent'a göre yükseklik
            contentHeight="auto"
          />
        </div>
      </CardContent>
    </Card>
  );
}