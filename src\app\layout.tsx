import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import "flag-icons/css/flag-icons.min.css"; // flag-icons CSS import edildi
import Footer from "@/components/footer";
import { LanguageProvider } from "@/lib/i18n/LanguageContext";
import SchemaOrgWrapper from "@/components/SchemaOrgWrapper";
import { Toaster } from "@/components/ui/sonner";
import { Navbar } from "@/components/Navbar"; // Navbar import edildi

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AlmancaABC | Online Almanca Kursu & Özel Dersler",
  description: "AlmancaABC ile online Almanca dil kursları ve özel derslerle Almanca öğren! <PERSON><PERSON> öğ<PERSON>, esnek programlar ve uygun fiyatlar. Hemen başla!",
  metadataBase: new URL('https://almancaabc.com'),
  alternates: {
    canonical: '/',
    languages: {
      'tr': '/tr',
      'de': '/de',
    },
  },
  openGraph: {
    title: "AlmancaABC | Online Almanca Kursu & Özel Dersler",
    description: "AlmancaABC ile online Almanca dil kursları ve özel derslerle Almanca öğren! Uzman öğretmenler, esnek programlar ve uygun fiyatlar.",
    url: 'https://almancaabc.com',
    siteName: 'AlmancaABC',
    images: [
      {
        url: 'https://almancaabc.com/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'AlmancaABC - Online Almanca Eğitimi',
      },
    ],
    locale: 'tr_TR',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "AlmancaABC | Online Almanca Kursu & Özel Dersler",
    description: "AlmancaABC ile online Almanca öğren!",
    images: ['https://almancaabc.com/twitter-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
      <html lang="tr" suppressHydrationWarning>
        <head>
          <meta charSet="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <SchemaOrgWrapper />
        </head>
        <body className={`${geistSans.variable} ${geistMono.variable} antialiased`} suppressHydrationWarning>
          <LanguageProvider>
            <div className="flex flex-col min-h-screen">
              <Navbar />
              <div className="flex-grow">
                {children}
              </div>
              <Footer />
              <Toaster position="bottom-center" />
            </div>
          </LanguageProvider>
        </body>
      </html>
  );
}
