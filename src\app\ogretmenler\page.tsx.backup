"use client"

import { useState, useEffect, useMemo, useCallback } from "react" // useCallback eklendi
import { motion } from "framer-motion"
import { TeacherCard } from "@/components/TeacherCard"
import { FilterBar } from "@/components/FilterBar" // Import aktif edildi
import type { Teacher } from "@/types/teacher" // Teacher tipi import edildi
import type { TeacherFilters } from "@/types/filters" // Filters tipi import edildi
import { Button } from "@/components/ui/button" // Doğru import yolu
import { Loader2, SearchX } from "lucide-react" // SearchX ikonu eklendi
import AppLayoutWrapper from "@/components/AppLayoutWrapper" // Layout wrapper import edildi

// Mock teacher data (Sağlanan veriyi kullanıyoruz)
const mockTeachers: Teacher[] = [ // Teacher[] tipi eklendi
  {
    id: 1,
    name: "<PERSON><PERSON> <PERSON>",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 4.9,
    reviewCount: 120,
    specializations: ["A1-C2", "TestDaF", "İş Almancası"], // specialties -> specializations
    price: 120,
    badges: ["Native Speaker", "10+ Yı<PERSON>eyim"],
    levels: ["A1", "A2", "B1", "B2", "C1", "C2"],
    country: "DE",
    availability: ["Sabah", "Öğleden Sonra"],
    languages: ["Almanca", "İngilizce", "Türkçe"],
    is_verified: true, // verified -> is_verified
  },
  {
    id: 2,
    name: "Herr Müller",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 4.8,
    reviewCount: 98,
    specializations: ["İş Almancası", "Konuşma Pratiği", "A1-B2"], // specialties -> specializations
    price: 110,
    badges: ["TELC Sertifikalı", "5+ Yıl Deneyim"],
    levels: ["A1", "A2", "B1", "B2"],
    country: "AT",
    availability: ["Akşam", "Gece"],
    languages: ["Almanca", "İngilizce"],
    is_verified: false, // verified -> is_verified
  },
  {
    id: 3,
    name: "Frau Weber",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 5.0,
    reviewCount: 75,
    specializations: ["Goethe Sınavları", "Akademik Almanca", "C1-C2"], // specialties -> specializations
    price: 130,
    badges: ["Goethe Sertifikalı", "Akademisyen"],
    levels: ["C1", "C2"],
    country: "DE",
    availability: ["Sabah", "Öğleden Sonra", "Akşam"],
    languages: ["Almanca", "Türkçe"],
    is_verified: true, // verified -> is_verified
  },
  {
    id: 4,
    name: "Herr Klein",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 4.7,
    reviewCount: 89,
    specializations: ["Çocuklar için Almanca", "A1-B2", "Oyunlarla Öğrenme"], // specialties -> specializations
    price: 100,
    badges: ["Pedagojik Formasyon", "Çocuk Eğitimi Uzmanı"],
    levels: ["A1", "A2", "B1", "B2"],
    country: "CH",
    availability: ["Öğleden Sonra", "Akşam"],
    languages: ["Almanca", "İngilizce", "Fransızca"],
    is_verified: false, // verified -> is_verified
  },
  {
    id: 5,
    name: "Frau Wagner",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 4.9,
    reviewCount: 150,
    specializations: ["Sınav Hazırlık", "Akademik Almanca", "TestDaF"], // specialties -> specializations
    price: 140,
    badges: ["TestDaF Uzmanı", "Doktora Derecesi"],
    levels: ["B2", "C1", "C2"],
    country: "DE",
    availability: ["Sabah", "Akşam"],
    languages: ["Almanca", "İngilizce"],
    is_verified: true, // verified -> is_verified
  },
  {
    id: 6,
    name: "Herr Fischer",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 4.6,
    reviewCount: 67,
    specializations: ["Konuşma Pratiği", "Günlük Almanca", "A1-B1"], // specialties -> specializations
    price: 90,
    badges: ["Konuşma Uzmanı"],
    levels: ["A1", "A2", "B1"],
    country: "AT",
    availability: ["Öğleden Sonra", "Gece"],
    languages: ["Almanca", "Türkçe"],
    is_verified: false, // verified -> is_verified
  },
  {
    id: 7,
    name: "Frau Koch",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 4.8,
    reviewCount: 92,
    specializations: ["İş Almancası", "B1-C2", "Sunum Teknikleri"], // specialties -> specializations
    price: 125,
    badges: ["İş Almancası Uzmanı", "Native Speaker"],
    levels: ["B1", "B2", "C1", "C2"],
    country: "CH",
    availability: ["Sabah", "Öğleden Sonra", "Akşam"],
    languages: ["Almanca", "İngilizce", "İtalyanca"],
    is_verified: true, // verified -> is_verified
  },
  {
    id: 8,
    name: "Herr Bauer",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 4.7,
    reviewCount: 83,
    specializations: ["Teknik Almanca", "Mühendislik Almancası", "B2-C2"], // specialties -> specializations
    price: 135,
    badges: ["Teknik Terminoloji Uzmanı", "Mühendis"],
    levels: ["B2", "C1", "C2"],
    country: "DE",
    availability: ["Akşam", "Hafta Sonu"],
    languages: ["Almanca", "İngilizce"],
    is_verified: false, // verified -> is_verified
  },
  {
    id: 9,
    name: "Frau Hoffmann",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 4.9,
    reviewCount: 110,
    specializations: ["Edebiyat", "Yazma Becerileri", "C1-C2"], // specialties -> specializations
    price: 120,
    badges: ["Yazar", "Edebiyat Uzmanı"],
    levels: ["C1", "C2"],
    country: "AT",
    availability: ["Sabah", "Akşam"],
    languages: ["Almanca", "İngilizce", "Fransızca"],
    is_verified: true, // verified -> is_verified
  },
  {
    id: 10,
    name: "Herr Schulz",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 4.8,
    reviewCount: 95,
    specializations: ["Tıp Almancası", "Akademik Yazma", "B2-C2"], // specialties -> specializations
    price: 150,
    badges: ["Tıp Doktoru", "Akademik Almanca Uzmanı"],
    levels: ["B2", "C1", "C2"],
    country: "CH",
    availability: ["Öğleden Sonra", "Akşam", "Hafta Sonu"],
    languages: ["Almanca", "İngilizce", "Latince"],
    is_verified: false, // verified -> is_verified
  },
]

// AI önerilen öğretmenler (Sağlanan veriyi kullanıyoruz)
const AIRecommendations: Teacher[] = [ // Teacher[] tipi eklendi
  {
    id: 101,
    name: "Frau Becker",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 5.0,
    reviewCount: 180,
    specializations: ["A1-C2", "TestDaF", "Goethe"], // specialties -> specializations
    price: 150,
    badges: ["AI Önerisi", "Premium Öğretmen", "Native Speaker"],
    levels: ["A1", "A2", "B1", "B2", "C1", "C2"],
    country: "DE",
    availability: ["Sabah", "Öğleden Sonra", "Akşam"],
    languages: ["Almanca", "İngilizce", "Türkçe"],
    is_verified: true, // verified -> is_verified
  },
  {
    id: 102,
    name: "Herr Schneider",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 4.9,
    reviewCount: 150,
    specializations: ["İş Almancası", "Akademik Almanca"], // specialties -> specializations
    price: 140,
    badges: ["AI Önerisi", "İş Almancası Uzmanı"],
    levels: ["B1", "B2", "C1", "C2"],
    country: "AT",
    availability: ["Öğleden Sonra", "Akşam"],
    languages: ["Almanca", "İngilizce"],
    is_verified: false, // verified -> is_verified
  },
  {
    id: 103,
    name: "Frau Meyer",
    avatar: "/placeholder.svg?height=400&width=400",
    rating: 4.9,
    reviewCount: 130,
    specializations: ["Sınav Hazırlık", "Konuşma Pratiği"], // specialties -> specializations
    price: 135,
    badges: ["AI Önerisi", "Sınav Uzmanı"],
    levels: ["A2", "B1", "B2", "C1"],
    country: "CH",
    availability: ["Sabah", "Akşam"],
    languages: ["Almanca", "Fransızca"],
    is_verified: true, // verified -> is_verified
  },
]


export default function TeachersPage() {
  return (
    <div className="flex-grow">
      <TeachersPageContent />
    </div>
  )
}

function TeachersPageContent() {
  const [displayCount, setDisplayCount] = useState(7)
  const [isLoading, setIsLoading] = useState(false)
  const [filters, setFilters] = useState<TeacherFilters>({
    level: [] as string[],
    price: [0, 500] as [number, number],
    availability: [],
    specialties: [], // Bu TeacherFilters içinde kalabilir, Teacher tipindeki specializations ile karıştırılmamalı
    country: [],
    search: "",
  })

  const filterTeachers = useCallback((teachersArray: Teacher[]) => {
    if (!Array.isArray(teachersArray)) {
      console.error("teachersArray is not an array:", teachersArray)
      return []
    }
    return teachersArray.filter((teacher) => {
      // Search filter
      const searchMatch =
        !filters.search ||
        (teacher.name && teacher.name.toLowerCase().includes(filters.search.toLowerCase())) ||
        (teacher.specializations && teacher.specializations.some((s: string) => s.toLowerCase().includes(filters.search.toLowerCase()))) || // specialties -> specializations
        (teacher.badges && teacher.badges.some((b: string) => b.toLowerCase().includes(filters.search.toLowerCase()))) ||
        (teacher.languages && teacher.languages.some((l: string) => l.toLowerCase().includes(filters.search.toLowerCase())))

      // Level filter
      const levelMatch = filters.level.length === 0 || (!!teacher.levels && filters.level.some((level) => teacher.levels!.includes(level))) // teacher.levels kontrolü

      // Price filter
      const priceMatch = typeof teacher.price === 'number' && teacher.price >= Number(filters.price[0]) && teacher.price <= Number(filters.price[1]);

      // Availability filter
      const availabilityMatch =
        filters.availability.length === 0 || (!!teacher.availability && filters.availability.some((a: string) => teacher.availability!.includes(a))) // teacher.availability kontrolü

      // Specialties filter (Filtreleme için TeacherFilters'daki 'specialties' kullanılır, Teacher tipindeki 'specializations' ile eşleşir)
      const specialtiesMatch =
        filters.specialties.length === 0 ||
        (!!teacher.specializations && filters.specialties.some((s: string) => // teacher.specializations kontrolü
          teacher.specializations!.some((teacherSpecialty: string) => teacherSpecialty.toLowerCase().includes(s.toLowerCase())),
        ))

      // Country filter
      const countryMatch = filters.country.length === 0 || (teacher.country && filters.country.includes(teacher.country))

      return searchMatch && levelMatch && priceMatch && availabilityMatch && specialtiesMatch && countryMatch
    })
  }, [filters]) // filters bağımlılığı eklendi

  // filters bağımlılığı useMemo'lardan kaldırıldı
  const filteredTeachers = useMemo(() => filterTeachers(mockTeachers), [filterTeachers])
  const filteredAIRecommendations = useMemo(() => filterTeachers(AIRecommendations), [filterTeachers])

  useEffect(() => {
    setDisplayCount(7) // Reset display count when filters change
  }, [filters])

  const loadMore = async () => {
    setIsLoading(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))
    setDisplayCount((prev) => Math.min(prev + 7, filteredTeachers.length))
    setIsLoading(false)
  }

  const hasResults = filteredAIRecommendations.length > 0 || filteredTeachers.length > 0; // Sonuç olup olmadığını kontrol et

  // "Sonuç Yok" mesajını ve koşullu render'ı ekle
  const renderContent = () => {
    if (!hasResults) {
      return (
        <div className="text-center py-16">
          <SearchX className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-lg font-medium text-gray-900">Sonuç Bulunamadı</h3>
          <p className="mt-1 text-sm text-gray-500">Filtre kriterlerinizi değiştirmeyi deneyin.</p>
          {/* İsteğe bağlı: Filtreleri temizle butonu */}
          {/* <Button variant="link" className="mt-4" onClick={() => setFilters({ level: [], price: [0, 500], availability: [], specialties: [], country: [], search: "" })}>
            Filtreleri Temizle
          </Button> */}
        </div>
      );
    }

    return (
      <>
        {/* AI Recommendations Section */}
        {filteredAIRecommendations.length > 0 && (
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Size Özel Öneriler</h2>
            <div className="space-y-6">
              {filteredAIRecommendations.map((teacher, index) => (
                <motion.div
                  key={`ai-${teacher.id}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <TeacherCard teacher={teacher} index={index} isAIRecommended />
                </motion.div>
              ))}
            </div>
          </section>
        )}

        {/* Main Teachers Section */}
        {filteredTeachers.length > 0 && ( // Ana listeyi sadece öğretmen varsa göster
          <section>
            <div className="flex justify-between items-center mb-6">
              {/* Filtrelenmiş sayıyı göster */}
              <h1 className="text-3xl font-extrabold tracking-tight text-primary mb-2" tabIndex={0} aria-label={`Tüm Öğretmenler (${filteredTeachers.length})`}>Tüm Öğretmenler <span className="text-lg font-semibold text-muted-foreground">({filteredTeachers.length})</span></h1>
            </div>

            {/* Teacher Cards */}
            <div className="space-y-4 mt-8">
              {filteredTeachers.slice(0, displayCount).map((teacher, index) => (
                <motion.div
                  key={`teacher-${teacher.id}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <TeacherCard teacher={teacher} index={index} />
                </motion.div>
              ))}
            </div>

            {/* Load More Button */}
            {displayCount < filteredTeachers.length && (
              <div className="flex justify-center mt-8">
                <Button onClick={loadMore} disabled={isLoading} variant="outline" size="lg">
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Yükleniyor...
                    </>
                  ) : (
                    "Daha Fazla Öğretmen Yükle"
                  )}
                </Button>
              </div>
            )}
          </section>
        )}
      </>
    );
  }


  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Filter Bar */}
        <FilterBar
          filters={filters}
          setFilters={setFilters}
          totalTeachers={filteredTeachers.length + filteredAIRecommendations.length} // Toplamı filtrelemeden önceki sayı ile değiştirmek daha mantıklı olabilir
        />

        {/* İçeriği render eden fonksiyonu çağır */}
        {renderContent()}

      </div>
    </div>
  )
}