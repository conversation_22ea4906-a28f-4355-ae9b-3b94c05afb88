import React from 'react'; // React import'u eklendi

// Generic type T'yi ve Props tipini tanımlayalım
// T'nin id'ye sahip olabileceğini belirtelim (key için)
type TableProps<T extends { id?: string | number }> = {
  columns: { header: string; accessor: string; className?: string }[];
  renderRow: (item: T) => React.ReactNode; // 'any' yerine 'T' kullanalım
  data: T[]; // 'any[]' yerine 'T[]' kullanalım
};

// Generic fonksiyonel bileşen tanımı
const Table = <T extends { id?: string | number }>({
  columns,
  renderRow,
  data,
}: TableProps<T>) => { // Props tipini kullanalım
  return (
    (<table className="w-full mt-4">
      <thead>
        <tr className="text-left text-gray-500 text-sm border-b border-gray-200"> {/* Alt çizgi eklendi */}
          {columns.map((col) => (
            (<th key={col.accessor} className={`py-3 px-4 ${col.className || ''}`}>{col.header}</th>) // Padding eklendi
          ))}
        </tr>
      </thead>
      <tbody>
        {data.map((item, index) => ( // index eklendi (key için fallback)
          // item.id'nin T üzerinde opsiyonel olarak tanımlandığını varsayıyoruz
          // renderRow'un bir <tr> döndürdüğünü ve key'i orada aldığını varsaymak daha iyi olur.
          // Ancak fallback olarak burada bırakıyoruz.
          (<React.Fragment key={item.id ?? index}>
            {renderRow(item)}
          </React.Fragment>)
        ))}
      </tbody>
    </table>)
  );
};

export default Table;