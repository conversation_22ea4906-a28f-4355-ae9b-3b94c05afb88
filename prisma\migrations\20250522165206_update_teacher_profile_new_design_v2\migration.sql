/*
  Warnings:

  - You are about to drop the column `created_at` on the `LessonPackage` table. All the data in the column will be lost.
  - You are about to drop the column `is_active` on the `LessonPackage` table. All the data in the column will be lost.
  - You are about to drop the column `lessons` on the `LessonPackage` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `LessonPackage` table. All the data in the column will be lost.
  - You are about to drop the column `averageResponseTime` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `badges` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `bio` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `faqs` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `languages` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `stats` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `videoCoursesIntro` on the `Teacher` table. All the data in the column will be lost.
  - Added the required column `type` to the `LessonPackage` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `LessonPackage` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "LessonPackage" DROP COLUMN "created_at",
DROP COLUMN "is_active",
DROP COLUMN "lessons",
DROP COLUMN "updated_at",
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "discountPercentage" INTEGER,
ADD COLUMN     "enrolledStudentCount" INTEGER DEFAULT 0,
ADD COLUMN     "features" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "groupCourseSchedule" TEXT,
ADD COLUMN     "groupCourseStartDate" TIMESTAMP(3),
ADD COLUMN     "isActive" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "lessonDurationMinutes" INTEGER,
ADD COLUMN     "lessonsInPackage" INTEGER,
ADD COLUMN     "level" TEXT,
ADD COLUMN     "pricePerLessonCalculated" DECIMAL(65,30),
ADD COLUMN     "studentCapacity" TEXT,
ADD COLUMN     "thumbnailUrl" TEXT,
ADD COLUMN     "type" TEXT NOT NULL,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "videoCourseIsPopular" BOOLEAN DEFAULT false,
ADD COLUMN     "videoCourseMaterials" TEXT,
ADD COLUMN     "videoCourseTotalHours" DOUBLE PRECISION,
ADD COLUMN     "videoCourseTotalLessons" INTEGER,
ADD COLUMN     "youtubePlaylistUrl" TEXT;

-- AlterTable
ALTER TABLE "Teacher" DROP COLUMN "averageResponseTime",
DROP COLUMN "badges",
DROP COLUMN "bio",
DROP COLUMN "faqs",
DROP COLUMN "languages",
DROP COLUMN "stats",
DROP COLUMN "videoCoursesIntro",
ADD COLUMN     "aboutMe" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "averageResponseTimeHours" INTEGER DEFAULT 0,
ADD COLUMN     "benefits" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "newStudentsLast30Days" INTEGER DEFAULT 0,
ADD COLUMN     "satisfactionRate" INTEGER DEFAULT 0,
ADD COLUMN     "spokenLanguages" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "teachingMethodology" TEXT[] DEFAULT ARRAY[]::TEXT[];

-- CreateTable
CREATE TABLE "TeacherFAQ" (
    "id" TEXT NOT NULL,
    "teacherId" TEXT NOT NULL,
    "question" TEXT NOT NULL,
    "answer" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TeacherFAQ_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "TeacherFAQ_teacherId_idx" ON "TeacherFAQ"("teacherId");

-- CreateIndex
CREATE INDEX "LessonPackage_type_idx" ON "LessonPackage"("type");

-- AddForeignKey
ALTER TABLE "TeacherFAQ" ADD CONSTRAINT "TeacherFAQ_teacherId_fkey" FOREIGN KEY ("teacherId") REFERENCES "Teacher"("id") ON DELETE CASCADE ON UPDATE CASCADE;
