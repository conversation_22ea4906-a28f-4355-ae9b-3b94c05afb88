'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Award, Clock, Users, Star, Globe } from 'lucide-react';

interface TeacherBadgesProps {
  isVerified?: boolean;
  isPremium?: boolean;
  isOnline?: boolean;
  studentCount?: number;
  rating?: number;
  languages?: string[];
  specialties?: string[];
  layout?: 'horizontal' | 'vertical' | 'grid';
}

export const TeacherBadges: React.FC<TeacherBadgesProps> = ({
  isVerified = false,
  isPremium = false,
  isOnline = false,
  studentCount,
  rating,
  languages = [],
  specialties = [],
  layout = 'horizontal'
}) => {
  const badges = [];

  if (isVerified) {
    badges.push(
      <Badge key="verified" className="bg-green-500 hover:bg-green-600 text-white">
        <CheckCircle className="w-3 h-3 mr-1" />
        Doğrulanmış
      </Badge>
    );
  }

  if (isPremium) {
    badges.push(
      <Badge key="premium" className="bg-yellow-500 hover:bg-yellow-600 text-white">
        <Award className="w-3 h-3 mr-1" />
        Premium
      </Badge>
    );
  }

  if (isOnline) {
    badges.push(
      <Badge key="online" className="bg-green-400 hover:bg-green-500 text-white">
        <div className="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></div>
        Çevrimiçi
      </Badge>
    );
  }

  if (rating && rating > 0) {
    badges.push(
      <Badge key="rating" className="bg-yellow-400 hover:bg-yellow-500 text-white">
        <Star className="w-3 h-3 mr-1 fill-white" />
        {rating.toFixed(1)}
      </Badge>
    );
  }

  if (studentCount && studentCount > 0) {
    badges.push(
      <Badge key="students" variant="outline" className="border-blue-200 text-blue-700">
        <Users className="w-3 h-3 mr-1" />
        {studentCount} Öğrenci
      </Badge>
    );
  }

  languages.forEach(lang => {
    badges.push(
      <Badge key={`lang-${lang}`} variant="secondary" className="bg-blue-100 text-blue-800">
        <Globe className="w-3 h-3 mr-1" />
        {lang}
      </Badge>
    );
  });

  specialties.forEach(specialty => {
    badges.push(
      <Badge key={`spec-${specialty}`} variant="outline" className="border-purple-200 text-purple-700">
        {specialty}
      </Badge>
    );
  });

  const layoutClasses = {
    horizontal: 'flex flex-wrap gap-2',
    vertical: 'flex flex-col gap-2',
    grid: 'grid grid-cols-2 gap-2'
  };

  return (
    <div className={layoutClasses[layout]}>
      {badges}
    </div>
  );
};