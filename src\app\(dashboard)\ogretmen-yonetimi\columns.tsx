import { ColumnDef } from "@tanstack/react-table"
import { Button } from "@/components/ui/button"
import { MoreHorizontal } from "lucide-react"

export type Teacher = {
  id: string
  name: string
  email: string
  status: "active" | "pending"
}

export const columns: ColumnDef<Teacher>[] = [
  { accessorKey: "name", header: "Ad Soyad" },
  { accessorKey: "email", header: "E-posta" },
  { accessorKey: "status", header: "Durum" },
  {
    id: "actions",
    cell: ({ row }) => (
      <Button variant="ghost" size="sm">
        <MoreHorizontal className="h-4 w-4" />
      </Button>
    ),
  },
]
