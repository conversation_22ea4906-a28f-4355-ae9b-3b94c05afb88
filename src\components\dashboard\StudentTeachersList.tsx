// src/components/dashboard/StudentTeachersList.tsx
import Link from "next/link";
// import Image from "next/image"; // Kullanılmadığı için kaldırıldı
import { getTeachersForStudentDashboard } from "@/lib/actions/student.actions";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export async function StudentTeachersList({ limit = 4 }: { limit?: number }) {
  const teachers = await getTeachersForStudentDashboard(limit);

  if (!teachers || teachers.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Öğretmenler</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Uygun öğretmen bulunamadı.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    (<Card>
      <CardHeader>
        <CardTitle>Öğretmenleri Keşfet</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {teachers.map((teacher) => (
            <div
              key={teacher.id}
              className="flex items-center justify-between p-3 border rounded-lg hover:shadow-sm transition-shadow"
            >
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage
                    src={teacher.profile_image_url || "/placeholder.svg"}
                    alt={`${teacher.firstName} ${teacher.lastName}`}
                  />
                  <AvatarFallback>
                    {teacher.firstName?.[0]}
                    {teacher.lastName?.[0]}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-semibold">
                    {teacher.firstName} {teacher.lastName}
                  </h3>
                  <p className="text-xs text-muted-foreground truncate max-w-xs">
                    {teacher.bio || "Almanca Öğretmeni"}
                  </p>
                  {teacher.specializations && teacher.specializations.length > 0 && (
                    <div className="mt-1">
                      {teacher.specializations.slice(0, 2).map((spec) => (
                        <Badge key={spec} variant="outline" className="mr-1 text-xs">
                          {spec}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              <div className="text-right">
                {teacher.hourly_rate && (
                  <p className="text-sm font-semibold mb-1">
                    {teacher.hourly_rate}€/saat
                  </p>
                )}
                <Button asChild variant="ghost" size="sm">
                  <Link href={`/teachers/${teacher.id}`}>
                    Profil <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>
        {teachers.length >= limit && (
            <div className="mt-4 text-center">
                <Button asChild variant="outline">
                    <Link href="/ogretmenler">Tüm Öğretmenler</Link>
                </Button>
            </div>
        )}
      </CardContent>
    </Card>)
  );
}