import React from 'react';
import { Metadata } from 'next';
// import Image from 'next/image'; // Kullanılmadığı için kaldırıldı
import Link from 'next/link'; // Link componenti import edildi
import { Button } from '@/components/ui/button'; // Button componenti import edildi
// import ContactForm from '@/components/ContactForm'; // Eski formu kaldırıyoruz
import { ModernContactForm } from '@/app/iletisim/page'; // Yeni formu import ediyoruz
import { IletisimBilgileriKarti } from '@/components/IletisimBilgileriKarti'; // İletişim bilgilerini kartını import ediyoruz

export const metadata: Metadata = {
  title: 'Hakkımızda | AlmancaABC - Sadece Almanca, Tam Odak!',
  description: 'AlmancaABC olarak vizyonumuz, Almanya ve Türkiye arasında Almanca öğreniminde köprü kurmak. Misyonumuz, mevcut 120 binlik blog trafiğimizle en iyi öğretmenleri öğrencilerle buluşturarak, Almanca öğrenimini herkes için erişilebilir ve etkili kılmaktır. Sadece Almanca\\\'ya odaklanarak bu alanda lider olmayı hedefliyoruz.',
  keywords: 'AlmancaABC hakkında, Almanca öğren, online Almanca, Almanca kursu, AlmancaABC vizyon, Almanca misyon, Türkiye Almanya Almanca, Almanca özel ders, Almanca öğretmenleri',
  alternates: {
    canonical: '/hakkimizda',
  },
  openGraph: {
    title: 'AlmancaABC Hakkında | Sadece Almanca, Tam Odak!',
    description: 'AlmancaABC\\\'nin kuruluş hikayesini, sadece Almanca\\\'ya odaklanma stratejisini, Almanya merkezli güvenilirliğini ve Türkiye-Almanya arasındaki dil köprüsünü nasıl kurduğunu öğrenin.',
    url: 'https://almancaabc.com/hakkimizda', // Sitenizin tam URL'si ile güncelleyin
    siteName: 'AlmancaABC',
    images: [
      {
        url: 'https://almancaabc.com/og-hakkimizda-almancaabc-v2.png', // Güncellenmiş Hakkımızda OG görseli
        width: 1200,
        height: 630,
        alt: 'AlmancaABC Hakkımızda - Sadece Almanca Online Eğitim Platformu',
      },
    ],
    locale: 'tr_TR',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AlmancaABC | Hakkımızda: Almanca\\\'da Uzmanlaşmış, Güvenilir Eğitim',
    description: 'Neden sadece Almanca? AlmancaABC\\\'nin benzersiz odaklanmasını, güçlü blog altyapısını ve Almanya merkezli olmasının avantajlarını keşfedin. Hedefimiz Almanca öğreniminde pazar lideri olmak.',
    images: ['https://almancaabc.com/twitter-hakkimizda-almancaabc-v2.png'], // Güncellenmiş Hakkımızda Twitter görseli
  },
};

// AboutPage Schema.org JSON-LD
const aboutPageSchema = {
  "@context": "https://schema.org",
  "@type": "AboutPage",
  "name": "Hakkımızda | AlmancaABC - Sadece Almanca, Tam Odak!",
  "description": "AlmancaABC olarak vizyonumuz, Almanya ve Türkiye arasında Almanca öğreniminde köprü kurmak. Misyonumuz, mevcut 120 binlik blog trafiğimizle en iyi öğretmenleri öğrencilerle buluşturarak, Almanca öğrenimini herkes için erişilebilir, etkili ve keyifli kılmaktır. Sadece Almanca diline odaklanarak bu niş alanda pazar lideri olmayı hedefliyoruz.",
  "url": "https://almancaabc.com/hakkimizda", // Sitenizin tam URL'si ile güncelleyin
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://almancaabc.com/hakkimizda" // Sitenizin tam URL'si ile güncelleyin
  },
  "publisher": {
    "@type": "EducationalOrganization",
    "name": "AlmancaABC",
    "url": "https://almancaabc.com",
    "logo": {
      "@type": "ImageObject",
      "url": "https://almancaabc.com/logo.png" // Ana logonuzun URL'si
    },
    "description": "Sadece Almanca diline odaklanmış, Türkiye ve Almanya pazarlarında uzman öğretmenlerle öğrencileri buluşturan lider online eğitim platformu. Mevcut 120 binlik blog trafiğiyle güçlü bir başlangıç.",
    "knowsAbout": ["Almanca Dili", "Online Almanca Eğitimi", "Almanca Özel Ders", "Almanca Grup Dersleri", "İş Almancası", "Aile Birleşimi Almanca"],
    "memberOf": {
      "@type": "Organization",
      "name": "Türk-Alman Ticaret ve Sanayi Odası" // Varsayımsal, gerçekse teyit edin
    },
    "sameAs": [ // Sosyal medya linklerinizi ekleyin
      "https://www.facebook.com/almancaabc",
      "https://www.instagram.com/almancaabc",
      "https://www.linkedin.com/company/almancaabc", // Örnek
      "https://twitter.com/almancaabc_com" // Örnek
    ]
  },
  "about": {
    "@type": "EducationalOrganization",
    "name": "AlmancaABC",
    "description": "AlmancaABC, Almanca öğrenmek isteyen bireyleri, Almanya'ya göç etmeyi planlayan aileleri ve Türk-Alman iş dünyasındaki profesyonelleri hedefleyen, sadece Almanca'ya odaklanmış bir online eğitim platformudur. Aylık 120.000'i aşan blog ziyaretçisiyle güçlü bir organik erişime ve Almanya merkezli olmanın getirdiği güvenilirliğe sahiptir. Misyonumuz, en iyi Almanca öğretmenlerini öğrencilerle buluşturarak, kişiselleştirilmiş ve etkili bir öğrenme deneyimi sunmaktır.",
    "url": "https://almancaabc.com",
    "logo": "https://almancaabc.com/logo.png",
    "slogan": "Sadece Almanca. Tam Odak. Başarıya Ulaşın.",
    "foundingDate": "2023", // Gerçek kuruluş tarihi ile güncelleyin
    "founder": {
      "@type": "Person",
      "name": "Sn. Şahin Nahm" // Gerçek kurucu adı ile güncelleyin
    },
    "location": {
      "@type": "Place",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "DE",
        "addressLocality": "Berlin", // Örnek, gerçek şehir ile güncelleyin
        "postalCode": "10115" // Örnek, gerçek posta kodu ile güncelleyin
      }
    }
  },
  "breadcrumb": {
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Ana Sayfa",
        "item": "https://almancaabc.com/"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Hakkımızda",
        "item": "https://almancaabc.com/hakkimizda"
      }
    ]
  }
};

const HakkimizdaPage = () => {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(aboutPageSchema) }}
      />
      <div className="bg-gradient-to-b from-slate-50 to-white dark:from-slate-900 dark:to-slate-850">
        <div className="container mx-auto px-4 py-12 md:py-16 lg:py-20">
          <header className="mb-16 md:mb-20 text-center">
            <h1 className="text-[50px] font-extrabold tracking-tighter text-slate-900 dark:text-slate-50 sm:text-6xl md:text-7xl">
              AlmancaABC: <span className="text-primary">Sadece Almanca</span>, Tam Odak.
            </h1>
            <p className="mt-6 text-xl text-slate-600 dark:text-slate-300 md:text-2xl max-w-3xl mx-auto">
              Almanca öğrenme serüveninizde size rehberlik etmek için buradayız. AlmancaABC, Türkiye ve Almanya&apos;daki en yetkin öğretmenleri bir araya getirerek, Almanca&apos;yı etkili ve keyifli bir şekilde öğrenmeniz için tasarlandı.
            </p>
          </header>

          <section className="mb-20 md:mb-24">
            <div className="grid grid-cols-1 gap-12 md:grid-cols-2 md:gap-16 items-center">
              <div className="relative aspect-[4/3] w-full rounded-xl overflow-hidden shadow-2xl group transition-all duration-500 hover:shadow-primary/30">
                {/* 
                  Aşağıdaki Image bileşeni, '/images/hakkimizda/almancaabc-ekip-toplantisi.jpg' kaynağı bulunamadığı için 404 hatası veriyordu.
                  Bu nedenle yorum satırı haline getirilmiştir.
                  Eğer bu veya benzer bir resim eklenecekse, Next.js 13+ sürümleri için `layout` ve `objectFit` propları yerine 
                  `fill` prop'u ve `className="object-cover"` gibi Tailwind sınıfları kullanılmalıdır.
                  Örnek doğru kullanım:
                  <Image
                    src="/path/to/your/image.jpg" // Gerçek resim yolu
                    alt="Açıklayıcı bir alternatif metin"
                    fill
                    className="object-cover transform group-hover:scale-105 transition-transform duration-500 ease-in-out"
                    priority // Eğer LCP (Largest Contentful Paint) elemanı ise
                  />
                */}
                <div className="w-full h-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center rounded-xl">
                  <p className="text-slate-500 dark:text-slate-400 p-4 text-center">Ekip çalışması veya ilham verici bir Almanca öğrenme anını yansıtan bir görsel buraya eklenecektir.</p>
                </div>
                {/* Orijinal görselin üzerindeki gradient efekti için bu div kalabilir veya kaldırılabilir, tasarıma göre karar verilir.
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/10 group-hover:from-black/30 transition-all duration-500"></div> 
                */}
              </div>
              <div className="prose prose-xl dark:prose-invert max-w-none">
                <h2 className="text-4xl font-bold text-slate-800 dark:text-slate-100 mb-6 !leading-tight">
                  Biz Kimiz ve Neden Sadece Almanca?
                </h2>
                <p className="text-slate-700 dark:text-slate-300">
                  AlmancaABC, Almanca diline tutkuyla bağlı bir ekip tarafından, dil öğrenimini yeniden tanımlamak amacıyla kuruldu. Aylık 120.000&apos;i aşan ziyaretçi sayısıyla Almanya ve Türkiye&apos;de Almanca öğrenmek isteyenlerin güvendiği blogumuzun üzerine inşa ettiğimiz bu platformda, <strong className="font-semibold text-primary">Almanya merkezli</strong> olmanın getirdiği güvenilirlik ve <strong className="font-semibold text-primary">modern teknoloji</strong> anlayışımızla fark yaratıyoruz.
                </p>
                <p className="text-slate-700 dark:text-slate-300">
                  Peki, neden sadece Almanca? Çünkü inanıyoruz ki <strong className="font-semibold text-primary">niş uzmanlık</strong>, derinlemesine bilgi ve daha kaliteli bir eğitim deneyimi sunar. Tüm enerjimizi ve kaynaklarımızı tek bir dile odaklayarak, öğrencilerimize ve öğretmenlerimize en iyisini sunmayı hedefliyoruz. Amacımız, Almanca öğreniminde <strong className="font-semibold text-primary">pazar lideri</strong> olmak ve bu alanda bir referans noktası haline gelmek.
                </p>
                <p className="text-slate-700 dark:text-slate-300">
                  İster Almanya&apos;ya göç etmeyi, ister kariyerinizde yeni kapılar açmayı, isterse de sadece yeni bir dil ve kültür keşfetmeyi hedefliyor olun, AlmancaABC size özel, etkili ve motive edici çözümler sunar.
                </p>
              </div>
            </div>
          </section>

          <section className="mb-20 md:mb-24 bg-slate-100 dark:bg-slate-800 py-16 md:py-20 rounded-xl shadow-xl">
            <div className="container mx-auto px-4">
              <h2 className="text-4xl font-bold text-slate-800 dark:text-slate-100 mb-12 md:mb-16 text-center">
                AlmancaABC&apos;yi Farklı Kılan Nedir?
              </h2>
              <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                {[
                  { icon: "🎯", title: 'Niş Uzmanlık: Sadece Almanca', description: 'Tüm kaynaklarımızı ve enerjimizi sadece Almanca öğretimine odaklayarak, size bu dilde en derinlemesine uzmanlığı ve en etkili öğrenme yöntemlerini sunuyoruz.' },
                  { icon: "🌟", title: 'Öğrenci Odaklı Yaklaşım', description: 'Her öğrencimizin benzersiz hedeflerine ve öğrenme stiline saygı duyuyor, kişiselleştirilmiş ders planları ve materyallerle başarıya ulaşmanızı sağlıyoruz.' },
                  { icon: "👩‍🏫", title: 'Seçkin Eğitmen Kadrosu', description: 'Alanında deneyimli, pedagojik formasyona sahip, anadili Almanca olan veya bu seviyede yetkinliğe sahip, tutkulu öğretmenlerle çalışıyoruz.' },
                  { icon: "💻", title: 'Modern ve Erişilebilir Teknoloji', description: 'Kullanıcı dostu arayüzümüz, interaktif ders materyallerimiz ve kesintisiz canlı ders altyapımızla (Zoom SDK) öğrenmeyi her yerden, her zaman kolaylaştırıyoruz.' },
                  { icon: "💰", title: 'Şeffaf ve Adil Fiyatlandırma', description: 'Düşük komisyon oranları (%10-15 hedefi), esnek ders paketleri ve gizli ücretler olmadan kaliteli Almanca eğitimini herkes için ulaşılabilir kılıyoruz.' },
                  { icon: "🤝", title: 'Güçlü Topluluk ve Sürekli Destek', description: 'Öğrenenler ve öğretenler arasında etkileşimi teşvik eden, sorularınıza hızlı yanıtlar bulabileceğiniz (<30dk hedefi) destekleyici bir AlmancaABC topluluğu inşa ediyoruz.' },
                ].map((item) => (
                  <div key={item.title} className="p-6 bg-white dark:bg-gray-700 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1">
                    <div className="text-4xl mb-3">{item.icon}</div>
                    <h3 className="text-xl font-semibold text-primary mb-2">{item.title}</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">{item.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </section>

          <section className="mb-16 md:mb-20">
            <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-10 md:mb-12 text-center">
              Misyonumuz ve Vizyonumuz
            </h2>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="p-8 bg-white dark:bg-gray-700 rounded-lg shadow-xl border-l-4 border-primary">
                <h3 className="text-2xl font-semibold text-primary mb-4">Misyonumuz</h3>
                <div className="prose dark:prose-invert max-w-none">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    Almanca dilini ve kültürünü, Türkiye ve Almanya başta olmak üzere tüm dünyadaki öğrenicilere yenilikçi, etkileşimli ve erişilebilir yöntemlerle ulaştırmak; bireylerin kişisel, akademik ve profesyonel hedeflerine ulaşmalarını sağlamak ve Türk-Alman kültürel ve ticari ilişkilerine kalıcı katkılarda bulunmaktır. Mevcut 120 binlik blog trafiğimizi bu misyon için bir kaldıraç olarak kullanıyoruz.
                  </p>
                </div>
              </div>
              <div className="p-8 bg-white dark:bg-gray-700 rounded-lg shadow-xl border-l-4 border-sky-500 dark:border-sky-400">
                <h3 className="text-2xl font-semibold text-primary dark:text-sky-400 mb-4">Vizyonumuz</h3>
                <div className="prose dark:prose-invert max-w-none">
                  <p className="text-slate-800 dark:text-slate-200 leading-relaxed">
                    Sadece Almanca diline odaklanarak, teknoloji ve pedagojiyi mükemmel bir şekilde birleştiren, kullanıcı deneyimini her zaman en üst düzeye çıkaran ve sürekli gelişen bir ekosistemle, Almanca öğrenimi ve öğretiminde dünya çapında tanınan, güvenilen ve tercih edilen <strong className="font-semibold text-primary dark:text-sky-400">lider online eğitim platformu</strong> olmaktır. Modern, kullanıcı dostu ve etkili bir deneyim sunarak; birebir ve grup dersleri, çeviri, danışmanlık gibi ek hizmetler ve güçlü bir topluluk ile Türk-Alman iş ve kültür dünyasına köprü kuran bir ekosistem yaratmayı hedefliyoruz. Nihai hedefimiz, bu niş alanda <strong className="font-semibold text-primary dark:text-sky-400">pazarın öncüsü</strong> olmaktır.
                  </p>
                </div>
              </div>
            </div>
          </section>

          <section className="text-center py-12 md:py-16 bg-primary/5 dark:bg-primary/10 rounded-xl shadow-lg">
              <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-6">
                Almanca Hayallerinize Bir Adım Daha Yaklaşın!
              </h2>
              <p className="text-lg text-gray-700 dark:text-gray-300 mb-10 max-w-2xl mx-auto">
                İster sıfırdan başlayın, ister Almanca&apos;nızı mükemmelleştirmek isteyin, AlmancaABC size özel programlar ve uzman öğretmenlerle hedeflerinize ulaşmanız için yanınızda.
              </p>
              <div className="space-y-4 sm:space-y-0 sm:flex sm:justify-center sm:space-x-6">
                <Button asChild size="lg" className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 text-lg rounded-md shadow-md hover:shadow-lg transition-all duration-300">
                  <Link href="/kurslar">Kurslarımızı Keşfedin</Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="w-full sm:w-auto border-primary text-primary hover:bg-primary/10 hover:text-primary px-8 py-3 text-lg rounded-md shadow-md hover:shadow-lg transition-all duration-300">
                  <Link href="/teachers">Size Uygun Öğretmeni Bulun</Link>
                </Button>
              </div>
          </section>

          <section className="py-16 md:py-20">
            <div className="container mx-auto px-4">
              <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-10 md:mb-12 text-center">
                Sorularınız mı Var? Bizimle İletişime Geçin!
              </h2>
              {/* İletişim sayfasındaki yapıya benzer bir yapı kullanıyoruz */}
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl overflow-hidden">
                <div className="grid grid-cols-1 lg:grid-cols-5">
                  <IletisimBilgileriKarti />
                  <div className="p-6 md:p-8 lg:col-span-3 bg-white dark:bg-slate-800">
                     <p className="text-gray-700 dark:text-gray-300 mb-6 text-md">
                      AlmancaABC, sunduğumuz hizmetler, öğretmenlerimiz veya platformumuz hakkında daha fazla bilgi almak, olası işbirliklerini görüşmek ya da aklınızdaki herhangi bir soruyu sormak için lütfen aşağıdaki formu doldurun. Size en kısa sürede geri dönüş yapacağız.
                    </p>
                    <ModernContactForm />
                  </div>
                </div>
              </div>
            </div>
          </section>

        </div>
      </div>
    </>
  );
};

export default HakkimizdaPage;
