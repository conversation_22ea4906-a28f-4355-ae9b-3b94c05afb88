// src/app/(dashboard)/teacher/page.tsx
import { UpcomingLessonsCard } from "@/components/dashboard/UpcomingLessonsCard";
import { TeacherCalendarSummary } from "@/components/dashboard/TeacherCalendarSummary";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { GraduationCap, MessageSquare, BookOpenCheck, History } from "lucide-react"; // Yeni ikonlar eklendi
import { getTeacherActiveStudentCount, getTeacherCompletedLessonsCount } from "@/lib/actions/teacher.actions"; // Action'lar import edildi

export default async function TeacherDashboardPage() {
  const activeStudentCount = await getTeacherActiveStudentCount();
  const completedLessonsThisMonth = await getTeacherCompletedLessonsCount('month');
  const totalCompletedLessons = await getTeacherCompletedLessonsCount('all');

  return (
    <div className="flex flex-col gap-6">
      <h1 className="text-2xl font-semibold">Öğretmen Paneli</h1>

      {/* Widget'lar */}
      <div className="grid gap-6 lg:grid-cols-3"> {/* Layout düzenlendi */}

         {/* Yaklaşan Dersler Kartı (Daha geniş alan kaplasın) */}
         <div className="lg:col-span-2"> {/* Büyük ekranlarda 2 sütun kaplasın */}
            <UpcomingLessonsCard />
         </div>

         {/* Diğer Kartlar (Yer Tutucu) */}
         <div className="space-y-6"> {/* Sağdaki kartlar alt alta */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Toplam Öğrenci
                </CardTitle>
                <GraduationCap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{activeStudentCount}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Bu Ay Tamamlanan Ders
                </CardTitle>
                <BookOpenCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{completedLessonsThisMonth}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Toplam Tamamlanan Ders
                </CardTitle>
                <History className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalCompletedLessons}</div>
              </CardContent>
            </Card>
            {/* Okunmamış Mesaj Kartı (Yer Tutucu) */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Okunmamış Mesaj</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                 {/* TODO: Gerçek okunmamış mesaj sayısını action ile çek */}
                <div className="text-2xl font-bold">0</div> {/* Örnek Veri */}
              </CardContent>
            </Card>
         </div>
      </div>

      {/* Ders Takvimi Özeti */}
       <div className="lg:col-span-3 mt-6">
         <TeacherCalendarSummary />
       </div>

    </div>
  );
}