# Brave Search MCP Server Guide

## Server Information

- **Identifier:** `github.com/modelcontextprotocol/servers/tree/main/src/brave-search`
- **Startup Command:** `cmd /c npx -y @modelcontextprotocol/server-brave-search`
- **Environment Variables:** `BRAVE_API_KEY="BSAeI8XpRwnBk4eR_k40uSGh5ajuWkv"` (Set during startup)
- **Description:** Provides tools for performing web and local searches using the Brave Search API.

## Available Tools

### `brave_web_search`
- **Description:** Performs a general web search using the Brave Search API. Ideal for news, articles, and broad information gathering. Supports pagination and filtering. Max 20 results per request.
- **Input Schema:**
  ```json
  {
    "type": "object",
    "properties": {
      "query": { "type": "string", "description": "Search query (max 400 chars, 50 words)" },
      "count": { "type": "number", "description": "Number of results (1-20, default 10)", "default": 10 },
      "offset": { "type": "number", "description": "Pagination offset (max 9, default 0)", "default": 0 }
    },
    "required": ["query"]
  }
  ```

### `brave_local_search`
- **Description:** Searches for local businesses and places using Brave's Local Search API. Best for location-specific queries (e.g., 'near me'). Returns detailed business info. Falls back to web search if no local results.
- **Input Schema:**
  ```json
  {
    "type": "object",
    "properties": {
      "query": { "type": "string", "description": "Local search query (e.g. 'pizza near Central Park')" },
      "count": { "type": "number", "description": "Number of results (1-20, default 5)", "default": 5 }
    },
    "required": ["query"]
  }
