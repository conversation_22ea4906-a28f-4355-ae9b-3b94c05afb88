"use client";

import { useState, useEffect } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/hooks/use-toast";
import { createAvailabilitySlot, deleteAvailabilitySlot, getAvailabilitySlots } from "@/lib/actions/availability.actions";
import type { AvailabilitySlot } from "@prisma/client";
import { DateSelectArg, EventClickArg } from "@fullcalendar/core";

interface TeacherAvailabilityCalendarProps {
  teacherId: string;
  isEditable?: boolean; // Öğretmen kendi takvimini düzenleyebilir, öğrenciler sadece görür
}

export default function TeacherAvailabilityCalendar({ 
  teacherId, 
  isEditable = false 
}: TeacherAvailabilityCalendarProps) {
  const [availabilitySlots, setAvailabilitySlots] = useState<AvailabilitySlot[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Müsaitlik slotlarını yükle
  useEffect(() => {
    const loadAvailabilitySlots = async () => {
      try {
        const slots = await getAvailabilitySlots(teacherId);
        setAvailabilitySlots(slots);
      } catch {
        toast({
          title: "Hata",
          description: "Müsaitlik bilgileri yüklenirken bir hata oluştu.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadAvailabilitySlots();
  }, [teacherId]);

  // AvailabilitySlot'ları FullCalendar event formatına çevir
  const events = availabilitySlots.map((slot) => ({
    id: slot.id,
    title: slot.isBooked ? "Rezerve Edilmiş" : "Müsait",
    start: slot.startTime,
    end: slot.endTime,
    backgroundColor: slot.isBooked ? "#ef4444" : "#22c55e", // Kırmızı: rezerve, Yeşil: müsait
    borderColor: slot.isBooked ? "#dc2626" : "#16a34a",
    textColor: "#ffffff",
    extendedProps: {
      isBooked: slot.isBooked,
      slotId: slot.id,
    },
  }));

  // Yeni müsaitlik slotu ekleme (sadece düzenlenebilir modda)
  const handleDateSelect = async (selectInfo: DateSelectArg) => {
    if (!isEditable) return;

    const { start, end } = selectInfo;
    
    // Geçmiş tarih kontrolü
    if (start < new Date()) {
      toast({
        title: "Hata",
        description: "Geçmiş tarihler için müsaitlik ekleyemezsiniz.",
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await createAvailabilitySlot(start, end, teacherId);

      if (result.success && result.slot) {
        setAvailabilitySlots(prev => [...prev, result.slot!]);
        toast({
          title: "Başarılı",
          description: "Müsaitlik zaman dilimi eklendi.",
        });
      } else {
        toast({
          title: "Hata",
          description: result.error || "Müsaitlik eklenirken bir hata oluştu.",
          variant: "destructive",
        });
      }
    } catch {
      toast({
        title: "Hata",
        description: "Müsaitlik eklenirken bir hata oluştu.",
        variant: "destructive",
      });
    }
  };

  // Müsaitlik slotu silme (sadece düzenlenebilir modda ve rezerve edilmemişse)
  const handleEventClick = async (clickInfo: EventClickArg) => {
    if (!isEditable) return;

    const { isBooked, slotId } = clickInfo.event.extendedProps;
    
    if (isBooked) {
      toast({
        title: "Uyarı",
        description: "Rezerve edilmiş zaman dilimlerini silemezsiniz.",
        variant: "destructive",
      });
      return;
    }

    if (confirm("Bu müsaitlik zaman dilimini silmek istediğinizden emin misiniz?")) {
      try {
        const result = await deleteAvailabilitySlot(slotId);
        
        if (result.success) {
          setAvailabilitySlots(prev => prev.filter(slot => slot.id !== slotId));
          toast({
            title: "Başarılı",
            description: "Müsaitlik zaman dilimi silindi.",
          });
        } else {
          toast({
            title: "Hata",
            description: result.error || "Müsaitlik silinirken bir hata oluştu.",
            variant: "destructive",
          });
        }
      } catch {
        toast({
          title: "Hata",
          description: "Müsaitlik silinirken bir hata oluştu.",
          variant: "destructive",
        });
      }
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Müsaitlik Takvimi</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Takvim yükleniyor...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>
            {isEditable ? "Müsaitlik Takvimi Yönetimi" : "Müsaitlik Takvimi"}
          </CardTitle>
          <div className="flex gap-2">
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              Müsait
            </Badge>
            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
              <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
              Rezerve
            </Badge>
          </div>
        </div>
        {isEditable && (
          <p className="text-sm text-muted-foreground">
            Müsait olduğunuz zaman dilimlerini seçmek için takvimde sürükleyerek alan seçin. 
            Silmek için müsait zaman dilimine tıklayın.
          </p>
        )}
      </CardHeader>
      <CardContent>
        <div className="h-[600px]">
          <FullCalendar
            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
            initialView="timeGridWeek"
            headerToolbar={{
              left: "prev,next today",
              center: "title",
              right: "dayGridMonth,timeGridWeek,timeGridDay",
            }}
            events={events}
            editable={false}
            selectable={isEditable}
            selectMirror={true}
            dayMaxEvents={true}
            weekends={true}
            locale="tr"
            buttonText={{
              today: "Bugün",
              month: "Ay",
              week: "Hafta",
              day: "Gün",
            }}
            allDaySlot={false}
            slotMinTime="06:00:00"
            slotMaxTime="24:00:00"
            slotDuration="01:00:00"
            snapDuration="00:30:00"
            select={handleDateSelect}
            eventClick={handleEventClick}
            height="100%"
            contentHeight="auto"
            selectConstraint={{
              start: new Date().toISOString().split('T')[0], // Bugünden itibaren
            }}
            businessHours={{
              daysOfWeek: [1, 2, 3, 4, 5, 6, 0], // Pazartesi-Pazar
              startTime: "08:00",
              endTime: "22:00",
            }}
            selectOverlap={false} // Çakışan seçimleri engelle
            eventOverlap={false} // Çakışan eventleri engelle
          />
        </div>
      </CardContent>
    </Card>
  );
}
