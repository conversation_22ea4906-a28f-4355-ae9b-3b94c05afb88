import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import Kurslar<PERSON><PERSON><PERSON> from './KurslarListesi';
import { kurslarData, type Kurs } from '@/lib/data/courses';
import { Button } from '@/components/ui/button';
import ContentPageLayout from '@/components/layout/ContentPageLayout'; // Yeni bileşeni import et
import { ArrowUp } from 'lucide-react'; // ArrowUp ikonu eklendi
import FaqAccordion from "@/components/FaqAccordion"; // FaqAccordion import edildi
import { kurslarFaqData, FaqItem } from '@/lib/data/faq-data'; // Merkezi SSS verisi import edildi
import Head from 'next/head'; // Head import edildi

export const metadata: Metadata = {
  title: 'Almanca Kursları | AlmancaABC - Tüm Seviyeler İçin Online Dersler',
  description: "AlmancaABC’de A1, A2, B1, B2, C1, C2 ve İş Almancası seviyelerinde online Almanca kurslarını keşfedin. Uzman öğretmenlerle Almanca öğrenmeye başlayın.",
  keywords: "Almanca kursları, online Almanca dersleri, Almanca seviyeleri, A1 Almanca, B1 Almanca, C1 Almanca, İş Almancası, Almanca öğren, AlmancaABC kurs, almanca kurs, almanca kursu, almanca kursu goethe, almanca kursu online, almanca kursları istanbul, almanca kurs önerisi, almanca kursları fiyatları, almanca kursları online, almanca kurs ücretleri, almanca kursu ücretsiz, almanca kurs fiyatları 2024, almanca kurs fiyatları 2025",
  alternates: {
    canonical: '/kurslar',
  },
  openGraph: {
    title: 'Tüm Seviyeler İçin En İyi Almanca Kursu | AlmancaABC',
    description: 'Başlangıçtan ileri seviyeye ve İş Almancasına kadar tüm Almanca kurslarımızı inceleyin ve Almanca öğrenme hedeflerinize ulaşın.',
    url: 'https://almancaabc.com/kurslar',
    siteName: 'AlmancaABC',
    images: [
      {
        url: 'https://almancaabc.com/og-kurslar.png',
        width: 1200,
        height: 630,
        alt: 'AlmancaABC Almanca Kursları',
      },
    ],
    locale: 'tr_TR',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AlmancaABC | Online Almanca Kursları ve Seviyeleri',
    description: "A1’den C2’ye ve İş Almancasına kadar tüm Almanca seviyeleri için online kurslarımızı keşfedin!",
    images: ['https://almancaabc.com/twitter-kurslar.png'],
  },
};

const generateCourseSchema = (kurs: Kurs) => {
  return {
    "@type": "Course",
    "name": `${kurs.seviye} - ${kurs.baslik}`,
    "description": kurs.aciklama,
    "provider": {
      "@type": "Organization",
      "name": "AlmancaABC",
      "url": "https://almancaabc.com"
    },
    "offers": {
      "@type": "Offer",
      "category": "Paid",
      "price": kurs.indirimli_fiyat ? kurs.indirimli_fiyat.toString() : kurs.fiyat.toString(),
      "priceCurrency": "TRY",
      "url": `https://almancaabc.com/kurslar#kurs-${kurs.id}`
    },
  };
};

const coursePageSchema = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "Almanca Kursları | AlmancaABC",
  "description": "AlmancaABC tarafından sunulan çeşitli seviyelerdeki online Almanca kurslarını keşfedin.",
  "url": "https://almancaabc.com/kurslar",
  "mainEntity": {
    "@type": "ItemList",
    "name": "Almanca Kursları",
    "description": "AlmancaABC’de sunulan tüm Almanca kursları.",
    "itemListElement": kurslarData.map((kurs, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": generateCourseSchema(kurs)
    }))
  }
};

// FAQPage için JSON-LD verisi oluşturan fonksiyon
const generateFaqPageSchema = (faqItems: FaqItem[]) => {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqItems.map(item => ({
      "@type": "Question",
      "name": item.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": item.answer
      }
    }))
  };
};

const KurslarPage = () => {
  const faqPageSchemaForCourses = generateFaqPageSchema(kurslarFaqData);

  return (<>
    <Head>
      {/* Mevcut metadata'ya ek olarak SSS Schema.org verisi */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqPageSchemaForCourses) }}
      />
    </Head>
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(coursePageSchema) }}
    />
    <KurslarListesi />
    {/* SSS Bölümü Başlangıcı */}
    <section className="py-12 md:py-16 bg-slate-50 dark:bg-slate-800/50">
      <div className="container mx-auto px-4 md:px-6">
        <FaqAccordion
          items={kurslarFaqData}
          title="Kurslarla İlgili Sıkça Sorulan Sorular"
          subtitle="Almanca kurslarımız hakkında merak ettikleriniz ve daha fazlası."
        />
      </div>
    </section>
    {/* SSS Bölümü Sonu */}
    <section className="py-6 md:py-8 lg:py-10 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4 md:px-6">
        <ContentPageLayout showToc={true}>
          <h2 className="text-3xl md:text-3xl font-bold mb-6">Almanca Hayallerinize Ulaşın: Size En Uygun Almanca Kursu Bulun</h2>
          <div className="prose prose-lg max-w-none">
            <p className="mb-8">
              Almanca öğrenmek, kariyerinizde yeni kapılar açmaktan yurt dışında eğitime, keyifli seyahatlerden kişisel gelişiminize kadar hayatınıza pek çok değer katabilir. Eğer siz de &quot;Almanca kursları&quot; arayışıyla bu yolculuğa ilk adımı attıysanız, doğru adrestesiniz. AlmancaABC olarak, Almanca öğrenme serüveninizde size destek olmak için buradayız. Bu rehberde, farklı ihtiyaç ve bütçelere uygun kurs seçeneklerimizi, dikkat etmeniz gerekenleri ve merak ettiklerinizi bulacaksınız.
            </p>

            <h3 className="text-2xl font-bold mb-6 mt-8">Neden Bir Almanca Kursuna Katılmalısınız?</h3>
            <p className="mb-8">
              Almanca, Avrupa&apos;nın en yaygın ana dillerinden biri ve Almanya, Avusturya, İsviçre gibi ülkelerin resmi dili. Kendi başınıza öğrenmeye çalışmak elbette değerli, ancak iyi yapılandırılmış bir Almanca kursu, dilbilgisini sağlam temellere oturtmak, düzenli pratik yapmak ve motivasyonunuzu korumak için büyük kolaylık sağlar. Kısacası Almanca kursları, dili A1&apos;den C2&apos;ye kadar seviyelerine göre öğreten programlardır. AlmancaABC&apos;nin online dersleriyle bu süreci sizin için daha da kolaylaştırıyoruz.
            </p>

            <h3 className="text-2xl font-bold mb-6 mt-8">AlmancaABC ile Online Almanca Kursu Seçenekleriniz</h3>
            <p className="mb-8">
              AlmancaABC, esnekliği ve kaliteyi bir araya getiren online Almanca kursları sunar. İhtiyaçlarınıza en uygun olanı seçmek AlmancaABC ile çok kolay!
            </p>
            <ul className="mb-8">
              <li>
                <strong>Esnek Online Almanca Kursları:</strong> Kendi hızınızda, dilediğiniz yerden öğrenme özgürlüğü sunan online kurslarımız tam size göre! İnteraktif derslerimiz, uzman öğretmenlerimiz ve zengin kaynaklarımızla öğrenmeyi keyifli bir deneyime dönüştürüyoruz.
              </li>
              <li>
                <strong>Etkileşimli Canlı Dersler:</strong> Sanal sınıf ortamında, deneyimli öğretmenlerimiz ve diğer öğrencilerle bir araya gelerek Almancanızı geliştirin. Bu dersler, özellikle konuşma pratiği yapmak ve anında geri bildirim almak için idealdir.
              </li>
            </ul>
            <p className="mb-8">
              İster İstanbul&apos;da, ister Ankara&apos;da, ister İzmir&apos;de olun, AlmancaABC ile coğrafi sınırlamalar olmadan, Türkiye&apos;nin veya dünyanın her yerinden kaliteli Almanca eğitimine ulaşabilirsiniz.
            </p>

            <h3 className="text-2xl font-bold mb-6 mt-8">Goethe Enstitüsü ve AlmancaABC: Farkımız Nedir?</h3>
            <p className="mb-8">
              Goethe Enstitüsü, Alman kültürünü ve dilini yayma amacıyla uluslararası geçerliliğe sahip sınavlara hazırlık ve kaliteli eğitimleriyle tanınır. AlmancaABC olarak biz de Goethe standartlarına yakın kalitede, ancak daha esnek ve erişilebilir online Almanca kursları sunmayı amaçlıyoruz. Belediyelerin sunduğu ücretsiz veya düşük maliyetli kurslar başlangıç seviyesi için bir seçenek olabilirken, AlmancaABC size kişiye özel bir öğrenme deneyimi ve uzman öğretmen kadrosuyla hedeflerinize daha hızlı ulaşma fırsatı sunar.
            </p>

            <h3 className="text-2xl font-bold mb-6 mt-8">Almanca Kurs Fiyatları AlmancaABC’de Nasıl Belirleniyor?</h3>
            <p className="mb-8">
              &quot;Almanca kurs fiyatları ne kadar?&quot; en çok merak edilen sorulardan biri. Fiyatlarımız; kursun türüne (birebir veya grup), seviyesine, süresine ve öğretmen deneyimine göre değişir. AlmancaABC olarak, fiyat politikamızda şeffaf olmaya ve her bütçeye uygun seçenekler sunmaya özen gösteriyoruz. Kurs ücretlerimiz genellikle ders saatini, materyalleri ve öğretmen uzmanlığını kapsar. Güncel fiyatlarımız için kurslar sayfamızı inceleyebilir veya bizimle iletişime geçebilirsiniz. 2024 ve 2025 yılına ait güncel fiyat bilgilerimiz sitemizde mevcuttur.
            </p>

            <h3 className="text-2xl font-bold mb-6 mt-8">Almanca Kurs Seviyeleri ve Kullanılan Kitaplar</h3>
            <p className="mb-8">
              AlmancaABC kursları, Avrupa Ortak Dil Referans Çerçevesi&apos;ne (CEFR) göre A1&apos;den C2&apos;ye kadar belirlenen seviyelerde ilerler. Her seviyenin kendine özgü hedefleri ve öğrenme süresi bulunur. Kurslarımızda kullandığımız kitaplar ve materyaller de bu seviyelere uygun olarak özenle seçilir ve öğrenme sürecinizi en iyi şekilde destekler.
            </p>

            <h3 className="text-2xl font-bold mb-6 mt-8">AlmancaABC&apos;den Kurs Seçerken Nelere Dikkat Etmelisiniz?</h3>
            <p className="mb-8">
              Bir Almanca kursu arayışındaysanız veya AlmancaABC&apos;den bir kurs seçmeyi düşünüyorsanız, şu noktalara göz atmanızı öneririz:
            </p>
            <ul className="mb-8">
              <li><strong>Öğretmenlerimizin Deneyimi:</strong> Alanında uzman ve pedagojik formasyona sahip öğretmenlerle çalışıyoruz.</li>
              <li><strong>Güncel Müfredat:</strong> Sürekli güncellenen, modern ve etkileşimli ders içerikleri sunuyoruz.</li>
              <li><strong>Esnek Ders Saatleri:</strong> Kendi programınıza uygun ders saatlerini seçme imkanı sağlıyoruz.</li>
              <li><strong>Öğrenci Yorumları:</strong> Diğer öğrencilerimizin deneyimlerini inceleyerek fikir edinebilirsiniz.</li>
              <li><strong>Kullanım Kolaylığı ve Destek:</strong> Kullanıcı dostu platformumuz ve hızlı teknik destek ekibimizle her zaman yanınızdayız.</li>
            </ul>

            <h3 className="text-2xl font-bold mb-6 mt-8">Almanya&apos;daki Öğretmenlerle Almanca Öğrenme Fırsatı</h3>
            <p>
              Dili yerinde öğrenme deneyimine en yakın tecrübeyi yaşamak isteyenler için AlmancaABC harika bir çözüm sunuyor. Platformumuz üzerinden Almanya&apos;da yaşayan deneyimli öğretmenlerimizle canlı dersler yapabilir, Alman kültürünü ve dilini ana dilini konuşan eğitmenlerden öğrenebilirsiniz. Bu, dil öğreniminizi kültürel bir deneyimle zenginleştirmenin en güzel yollarından biridir.
            </p>

            <h3 className="text-2xl font-bold mb-6 mt-8">Ücretsiz Almanca Kaynakları AlmancaABC&apos;de</h3>
            <p>
              Evet, ücretsiz Almanca kaynakları da sunuyoruz! AlmancaABC blogumuzda ve sosyal medya hesaplarımızda çeşitli ücretsiz kaynaklar, dil öğrenme ipuçları ve mini dersler paylaşıyoruz. Ayrıca, platformumuzdaki ücretsiz seviye tespit sınavıyla Almanca seviyenizi kolayca öğrenebilirsiniz.
            </p>

            <h3 className="text-2xl font-bold mb-6 mt-8">Sonuç: AlmancaABC ile Almanca Öğrenme Yolculuğunuza Bugün Başlayın!</h3>
            <p>
              Almanca öğrenme serüveninizde doğru kursu ve platformu seçmek, başarınız için çok önemli. AlmancaABC olarak, ister birebir özel dersleri ister grup derslerini tercih edin, hedeflerinize ve öğrenme stilinize en uygun seçimi yapmanız için size destek olmaya hazırız. Online Almanca kurslarımızla esnek, kaliteli ve etkileşimli bir öğrenme deneyimi sizi bekliyor. Umarız bu rehber, &quot;Almanca kursları&quot; arayışınızda size yol göstermiştir. Şimdi sıra, AlmancaABC ile size en uygun Almanca kursunu bularak bu heyecan verici dilin kapılarını aralamakta!
            </p>
          </div>
        </ContentPageLayout>
      </div>
    </section>
    {/* Yeni Eklenen Buton */}
    <section className="py-8 bg-gray-100 dark:bg-gray-800">
      <div className="container mx-auto px-4 md:px-6 text-center">
        <Button
          asChild
          size="lg"
          className="bg-white text-sky-600 hover:bg-sky-100 dark:bg-sky-400 dark:text-sky-900 dark:hover:bg-sky-300 px-12 py-7 text-xl font-bold rounded-xl shadow-2xl hover:shadow-sky-500/40 transition-all duration-300 transform hover:scale-105 group"
        >
          <Link href="#kaydirmali-kurs-listesi"> {/* Yukarıdaki kurs listesine yönlendirme */}
            Kursları Keşfet
            <ArrowUp size={24} className="ml-3 group-hover:-translate-y-1 transition-transform duration-300" />
          </Link>
        </Button>
      </div>
    </section>
  </>);
};

export default KurslarPage;