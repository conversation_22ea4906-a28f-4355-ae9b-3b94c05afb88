'use client';

import React from 'react';
import { MessageCircle, Send, Youtube, Facebook, Instagram, Twitter, Linkedin } from 'lucide-react'; // Rss as BlogIcon importu kaldırıldı (kullanılmıyor)
import Link from 'next/link';

const socialLinksData = [
  {
    name: "WhatsApp",
    href: "https://www.whatsapp.com/channel/0029Va7U3OEIyPtT3ljHfm2c",
    icon: MessageCircle,
    gradient: "from-green-400 to-green-600",
  },
  {
    name: "Telegram",
    href: "https://telegram.me/AlmancaABC",
    icon: Send,
    gradient: "from-blue-400 to-blue-600",
  },
  {
    name: "YouTube",
    href: "https://www.youtube.com/@AlmancaABC",
    icon: Youtube,
    gradient: "from-red-500 to-red-700",
  },
  {
    name: "Facebook",
    href: "https://www.facebook.com/almancaabc/",
    icon: Facebook,
    gradient: "from-blue-500 to-blue-700",
  },
  {
    name: "Instagram",
    href: "https://www.instagram.com/almancaabc/",
    icon: Instagram,
    gradient: "from-yellow-400 via-pink-500 to-purple-600",
  },
  {
    name: "Twitter",
    href: "https://twitter.com/AlmancaABC",
    icon: Twitter,
    gradient: "from-sky-400 to-sky-600",
  },
  {
    name: "LinkedIn",
    href: "https://www.linkedin.com/company/*********/",
    icon: Linkedin,
    gradient: "from-blue-600 to-blue-800",
  },
  // { // Blog linki aktif değil, istenirse eklenebilir
  //   name: "Blog",
  //   href: "/blog", // Projenizdeki blog sayfasına yönlendirin
  //   icon: BlogIcon, 
  //   gradient: "from-orange-400 to-orange-600",
  // },
];

interface SosyalMedyaKartiProps {
  className?: string;
  title?: string;
  description?: string;
  bottomText?: string;
}

export const SosyalMedyaKarti: React.FC<SosyalMedyaKartiProps> = ({
  className,
  title = "Bizi Takip Edin",
  description = "En son haberler, Almanca öğrenme ipuçları ve özel tekliflerimiz için sosyal medya kanallarımızı takip edin.",
  bottomText = "Sosyal medya hesaplarımızda güncel içeriklerimizi kaçırmayın!",
}) => {
  return (
    // Dıştaki div kaldırıldı, doğrudan içteki kart yapısı döndürülüyor.
    // className prop'u artık bu ana div'e uygulanıyor.
    // Bu kapanış etiketi bir önceki diff'te eksik kalmıştı.
    (<div className={`bg-white dark:bg-slate-800 rounded-2xl shadow-xl max-w-3xl w-full p-8 text-center ${className}`}>
      <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-4">{title}</h2>
      <p className="text-gray-600 dark:text-gray-300 mb-8 max-w-xl mx-auto">
        {description}
      </p>
      <div className="flex flex-wrap justify-center gap-5">
        {socialLinksData.map((link) => (
          <Link
            key={link.name}
            href={link.href}
            target="_blank"
            rel="noopener noreferrer"
            aria-label={link.name}
            className="group">
            <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${link.gradient} flex items-center justify-center text-white shadow-md hover:shadow-lg transform hover:scale-110 transition-all duration-300`}>
              <link.icon size={24} />
            </div>
          </Link>
        ))}
      </div>
      {bottomText && (
        <div className="mt-8 text-sm text-gray-500 dark:text-gray-400 flex items-center justify-center gap-2">
          <span className="w-2 h-2 bg-green-500 rounded-full animate-ping"></span>
          {bottomText}
        </div>
      )}
    </div>)
  );
};

export default SosyalMedyaKarti;