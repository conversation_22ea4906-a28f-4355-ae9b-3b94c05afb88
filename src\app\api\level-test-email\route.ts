import { NextRequest, NextResponse } from "next/server"
import { levelTestEmailSchema } from "@/lib/schemas/level-test-email.schema"
import { sendLevelTestEmail } from "@/lib/actions/level-test.actions"

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const parseResult = levelTestEmailSchema.safeParse(body)
    if (!parseResult.success) {
      return NextResponse.json({ success: false, error: "Geçersiz e-posta adresi." }, { status: 400 })
    }
    const { email } = parseResult.data
    const result = await sendLevelTestEmail(email)
    if (result.success) {
      return NextResponse.json({ success: true })
    } else {
      return NextResponse.json({ success: false, error: result.error }, { status: 500 })
    }
  } catch (error) {
    return NextResponse.json({ success: false, error: "Beklenmeyen bir hata o<PERSON>." }, { status: 500 })
  }
}
