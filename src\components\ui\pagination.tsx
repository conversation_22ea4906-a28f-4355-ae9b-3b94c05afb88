// src/components/ui/pagination.tsx
"use client";

import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

type PaginationProps = {
  currentPage: number;
  totalPages: number;
};

export function Pagination({ currentPage, totalPages }: PaginationProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Sayfa değiştiğinde URL'i güncelleme
  const createPageURL = (pageNumber: number | string) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", pageNumber.toString());
    return `${pathname}?${params.toString()}`;
  };

  // Gösterilecek sayfa numaralarını hesapla
  const generatePagination = () => {
    // Toplam sayfa sayısı 7 veya daha az ise tüm sayfaları göster
    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // Mevcut sayfa başlangıç veya sona yakınsa
    if (currentPage <= 3) {
      return [1, 2, 3, 4, 5, "...", totalPages];
    }

    if (currentPage >= totalPages - 2) {
      return [1, "...", totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
    }

    // Mevcut sayfa ortadaysa
    return [
      1,
      "...",
      currentPage - 1,
      currentPage,
      currentPage + 1,
      "...",
      totalPages,
    ];
  };

  const pages = generatePagination();

  return (
    (<div className="flex items-center justify-center gap-1 text-sm">
      <Button
        variant="outline"
        size="icon"
        className="h-8 w-8"
        disabled={currentPage <= 1}
        asChild
      >
        <Link
          href={createPageURL(currentPage - 1)}
          aria-label="Önceki sayfa"
          >
          <ChevronLeft className="h-4 w-4" />
        </Link>
      </Button>
      {pages.map((page, i) => (
        <div key={i}>
          {page === "..." ? (
            <div className="px-3 py-1">...</div>
          ) : (
            <Button
              variant={currentPage === page ? "default" : "outline"}
              size="icon"
              className="h-8 w-8"
              asChild
            >
              <Link href={createPageURL(page)}>
                {page}
              </Link>
            </Button>
          )}
        </div>
      ))}
      <Button
        variant="outline"
        size="icon"
        className="h-8 w-8"
        disabled={currentPage >= totalPages}
        asChild
      >
        <Link
          href={createPageURL(currentPage + 1)}
          aria-label="Sonraki sayfa"
          >
          <ChevronRight className="h-4 w-4" />
        </Link>
      </Button>
    </div>)
  );
}
