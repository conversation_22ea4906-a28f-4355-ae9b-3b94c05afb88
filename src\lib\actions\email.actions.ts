'use server'

import { Resend } from 'resend'

const resend = new Resend('re_ZsBC8Mji_4iVQ3gvbtfemZbA83oF7nSaJ')

export async function sendNewsletterEmail(email: string) {
  try {
    // Test modunda sadece kendi mail adresinize gönderebilirsiniz
    // Gerçek domain doğrulaması yapılana kadar
    const data = await resend.emails.send({
      from: 'AlmancaABC <<EMAIL>>',
      to: ['<EMAIL>'], // Kendi mail adresiniz
      subject: 'AlmancaABC Yeni Abone',
      html: `<p>Yeni bir abone: ${email}</p><p>AlmancaABC yeniliklerinden haberdar olmak için abone olundu.</p>`
    })
    // console.log('Mail gönderildi:', data) // Removed debug log
    return { success: true, data }
  } catch (error) {
    // console.error('Mail gönderim hatası:', error) // Removed debug log
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'E-posta gönderiminde beklenmeyen bir hata oluştu'
    }
  }
}
