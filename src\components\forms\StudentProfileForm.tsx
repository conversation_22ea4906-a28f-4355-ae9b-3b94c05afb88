"use client";
import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Student } from "@prisma/client";

// Zod şema
const studentProfileSchema = z.object({
  name: z.string().min(2, "İsim en az 2 karakter olmalı"),
  email: z.string().email("Geçerli bir e-posta giriniz"),
  phone: z.string().optional(),
  learningGoals: z.string().optional(),
  proficiencyLevel: z.string().optional(),
});

type StudentProfileFormValues = z.infer<typeof studentProfileSchema>;

interface StudentProfileFormProps {
  defaultValues?: StudentProfileFormValues;
  onSubmit: (data: StudentProfileFormValues) => void;
  isLoading?: boolean;
}

const StudentProfileForm: React.FC<StudentProfileFormProps> = ({
  defaultValues,
  onSubmit,
  isLoading = false,
}) => {
  const form = useForm<StudentProfileFormValues>({
    resolver: zodResolver(studentProfileSchema),
    defaultValues,
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>İsim Soyisim</FormLabel>
              <Input {...field} placeholder="Adınızı giriniz" />
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>E-posta</FormLabel>
              <Input {...field} placeholder="E-posta adresiniz" type="email" />
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Telefon</FormLabel>
              <Input {...field} placeholder="Telefon numaranız" />
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="learningGoals"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Hedefleriniz</FormLabel>
              <Input {...field} placeholder="Öğrenme hedefleriniz" />
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="proficiencyLevel"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Seviye</FormLabel>
              <Input {...field} placeholder="Dil seviyeniz (A1, B1 vb.)" />
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Kaydediliyor..." : "Kaydet"}
        </Button>
      </form>
    </Form>
  );
};

export default StudentProfileForm;
