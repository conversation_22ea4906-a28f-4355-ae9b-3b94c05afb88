export type Question = {
  id: number
  level: 'A1' | 'A2' | 'B1' | 'B2' | 'C1'
  question: string
  options: string[]
  correctAnswer: number
}

export const questions: Question[] = [
  {
    id: 1,
    level: 'A1',
    question: 'Wie heißt "merhaba" auf Deutsch?',
    options: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Bit<PERSON>'],
    correctAnswer: 1
  },
  {
    id: 2,
    level: 'A1',
    question: 'Was bedeutet "ich"?',
    options: ['sen', 'o', 'ben', 'biz'],
    correctAnswer: 2
  },
  {
    id: 3,
    level: 'A2',
    question: 'Welcher Satz ist richtig?',
    options: [
      'Ich gehe zur Schule gestern.',
      'Ich bin zur Schule gegangen.',
      'Ich habe zur Schule gegehen.',
      'Ich war zur Schule gegangen.'
    ],
    correctAnswer: 1
  },
  {
    id: 4,
    level: 'A2',
    question: 'Was ist der Plural von "das Buch"?',
    options: ['die Bücher', 'die Buchen', 'die Buchs', 'das Bücher'],
    correctAnswer: 0
  },
  {
    id: 5,
    level: 'B1',
    question: 'Welche Präposition passt? "Ich interessiere mich ___ Musik."',
    options: ['für', 'an', 'mit', 'zu'],
    correctAnswer: 0
  },
  {
    id: 6,
    level: 'B1',
    question: 'Ergänzen Sie den Satz im Konjunktiv II: "Wenn ich Zeit _____, würde ich dich besuchen."',
    options: ['hätte', 'habe', 'hatte', 'haben'],
    correctAnswer: 0
  },
  {
    id: 7,
    level: 'B2',
    question: 'Welcher Satz ist im Passiv?',
    options: [
      'Der Brief wird geschrieben.',
      'Sie schreibt den Brief.',
      'Der Brief ist schön.',
      'Sie hat den Brief geschrieben.'
    ],
    correctAnswer: 0
  },
  {
    id: 8,
    level: 'B2',
    question: 'Was bedeutet die Redewendung "Ich verstehe nur Bahnhof"?',
    options: [
      'Ich bin am Bahnhof.',
      'Ich verstehe nichts.',
      'Ich mag Züge.',
      'Ich arbeite am Bahnhof.'
    ],
    correctAnswer: 1
  },
  {
    id: 9,
    level: 'C1',
    question: 'Welches Wort passt nicht in die Reihe?',
    options: ['dennoch', 'trotzdem', 'obwohl', 'weil'],
    correctAnswer: 3
  },
  {
    id: 10,
    level: 'C1',
    question: 'Welcher Satz enthält einen Fehler?',
    options: [
      'Nachdem er aufgestanden war, machte er sich einen Kaffee.',
      'Kaum hatte er den Brief gelesen, als das Telefon klingelte.',
      'Obwohl er krank war, ist er zur Arbeit gegangen.',
      'Wenn er das gewusst hätte, hätte er anders gehandelt gehabt.'
    ],
    correctAnswer: 3
  },
  {
    id: 11,
    level: 'A2',
    question: 'Ergänzen Sie: "Können Sie mir ___ Weg zum Bahnhof zeigen?"',
    options: ['der', 'den', 'dem', 'das'],
    correctAnswer: 1
  },
  {
    id: 12,
    level: 'B1',
    question: 'Welche Zeitform wird verwendet? "Sie hatte schon gegessen, als ich ankam."',
    options: [
      'Präteritum',
      'Perfekt',
      'Plusquamperfekt',
      'Präsens'
    ],
    correctAnswer: 2
  }
]

export const calculateLevel = (correctAnswers: number): string => {
  if (correctAnswers <= 4) return 'A1'
  if (correctAnswers <= 6) return 'A2'
  if (correctAnswers <= 8) return 'B1'
  if (correctAnswers <= 10) return 'B2'
  return 'C1'
}
