// scripts/add-teachers-simple.js
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Rastgele ID üreten fonksiyon
function generateRandomId() {
  return Math.floor(10000000 + Math.random() * 90000000).toString();
}

// Basitleştirilmiş öğretmen verileri
const teachers = [
  {
    firstName: 'Ayşe',
    lastName: 'Yılmaz',
    title: '<PERSON><PERSON><PERSON><PERSON> Almanca Öğretmeni',
    bio: '10 yıllık deneyimli, sabırlı ve öğrenci odaklı bir öğretmenim.',
    specializations: ['<PERSON>ş Almancası', 'Günlük Konuşma'],
    levels: ['A1', 'A2', 'B1', 'B2'],
    hourly_rate: 150,
    profile_image_url: 'https://randomuser.me/api/portraits/women/1.jpg',
    country: 'Türkiye',
    city: 'İstanbul',
    languages: ['<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>'],
    is_approved: true,
    is_verified: true,
    experience_years: 10,
    average_rating: 4.9,
    completedLessons: 127
  },
  {
    firstName: 'Ahmet',
    lastName: 'Kaya',
    title: 'Sınav Odaklı Almanca Uzmanı',
    bio: "Goethe Enstitüsü'nde 5 yıl çalıştım. TestDaF ve telc sınavlarında uzmanlığım var.",
    specializations: ['Sınav Hazırlık', 'Akademik Almanca'],
    levels: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],
    hourly_rate: 180,
    profile_image_url: 'https://randomuser.me/api/portraits/men/2.jpg',
    country: 'Türkiye',
    city: 'Ankara',
    languages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: true,
    experience_years: 7,
    average_rating: 4.8,
    completedLessons: 98
  },
  {
    firstName: 'Zeynep',
    lastName: 'Demir',
    title: 'Anadil Seviyesinde Konuşma Pratiği',
    bio: "Almanya'da doğdum ve büyüdüm. Doğal bir Almanca konuşma pratiği sunuyorum.",
    specializations: ['Konuşma Pratiği', 'Günlük Almanca'],
    levels: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],
    hourly_rate: 140,
    profile_image_url: 'https://randomuser.me/api/portraits/women/3.jpg',
    country: 'Almanya',
    city: 'Berlin',
    languages: ['Almanca', 'Türkçe'],
    is_approved: true,
    is_verified: false,
    experience_years: 4,
    average_rating: 5.0,
    completedLessons: 56
  },
  {
    firstName: 'Can',
    lastName: 'Öztürk',
    title: 'Çocuklar İçin Eğlenceli Almanca',
    bio: 'Çocuklara ve gençlere Almanca öğretme konusunda 8 yıllık deneyime sahibim.',
    specializations: ['Çocuklar için Almanca', 'Gençler için Almanca'],
    levels: ['A1', 'A2', 'B1', 'B2', 'C1'],
    hourly_rate: 160,
    profile_image_url: 'https://randomuser.me/api/portraits/men/4.jpg',
    country: 'Türkiye',
    city: 'İzmir',
    languages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: true,
    experience_years: 8,
    average_rating: 4.7,
    completedLessons: 84
  },
  {
    firstName: 'Fatma',
    lastName: 'Şahin',
    title: 'Akademik Almanca ve YDS Uzmanı',
    bio: 'Akademik Almanca ve YDS hazırlığı konusunda uzmanım.',
    specializations: ['Akademik Almanca', 'YDS Hazırlık'],
    levels: ['A1', 'A2', 'B1', 'B2', 'C1'],
    hourly_rate: 170,
    profile_image_url: 'https://randomuser.me/api/portraits/women/5.jpg',
    country: 'Türkiye',
    city: 'İstanbul',
    languages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: true,
    experience_years: 9,
    average_rating: 4.9,
    completedLessons: 110
  },
  {
    firstName: 'Ali',
    lastName: 'Vural',
    title: 'Tıbbi ve Mesleki Almanca Eğitmeni',
    bio: 'Sağlık sektöründe çalışanlar için özel Almanca dersleri veriyorum.',
    specializations: ['Tıbbi Almanca', 'Mesleki Almanca'],
    levels: ['B1', 'B2', 'C1', 'C2'],
    hourly_rate: 190,
    profile_image_url: 'https://randomuser.me/api/portraits/men/6.jpg',
    country: 'Almanya',
    city: 'Münih',
    languages: ['Türkçe', 'Almanca', 'İngilizce'],
    is_approved: true,
    is_verified: true,
    experience_years: 12,
    average_rating: 4.6,
    completedLessons: 75
  },
  {
    firstName: 'Elif',
    lastName: 'Aksoy',
    title: 'Çeviri Teknikleri ve İleri Dilbilgisi',
    bio: 'Almanca-Türkçe çeviri ve dilbilgisi konularında yardımcı olabilirim.',
    specializations: ['Çeviri Teknikleri', 'İleri Gramer'],
    levels: ['B2', 'C1', 'C2'],
    hourly_rate: 165,
    profile_image_url: 'https://randomuser.me/api/portraits/women/7.jpg',
    country: 'Türkiye',
    city: 'Bursa',
    languages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: true,
    experience_years: 7,
    average_rating: 4.8,
    completedLessons: 92
  },
  {
    firstName: 'Mustafa',
    lastName: 'Yıldız',
    title: 'Günlük Konuşma ve Kültür Rehberi',
    bio: 'Almanya kültürü ve günlük yaşam hakkında konuşarak pratik yapalım.',
    specializations: ['Günlük Konuşma', 'Alman Kültürü'],
    levels: ['A1', 'A2', 'B1', 'B2'],
    hourly_rate: 155,
    profile_image_url: 'https://randomuser.me/api/portraits/men/8.jpg',
    country: 'Almanya',
    city: 'Hamburg',
    languages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: false,
    experience_years: 6,
    average_rating: 4.7,
    completedLessons: 88
  }
];

// Öğretmenleri veritabanına ekleyen fonksiyon
async function addTeachers() {
  console.log('Öğretmenler ekleniyor...');
  
  for (const teacher of teachers) {
    try {
      // Öğretmeni ekle
      const createdTeacher = await prisma.teacher.create({
        data: {
          id: generateRandomId(),
          ...teacher,
          // Decimal tipindeki değerleri düzelt
          hourly_rate: new prisma.Decimal(teacher.hourly_rate),
          average_rating: new prisma.Decimal(teacher.average_rating || 0)
        },
      });
      console.log(`Öğretmen eklendi: ${createdTeacher.firstName} ${createdTeacher.lastName} (ID: ${createdTeacher.id})`);
    } catch (error) {
      console.error(`Öğretmen eklenirken hata oluştu:`, error);
    }
  }
  
  console.log('İşlem tamamlandı!');
}

// Fonksiyonu çalıştır ve bağlantıyı kapat
addTeachers()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (error) => {
    console.error('Hata:', error);
    await prisma.$disconnect();
    process.exit(1);
  });
