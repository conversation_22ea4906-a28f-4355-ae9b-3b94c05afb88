'use server';

import { z } from 'zod';
import { contactFormSchema, ContactFormValues } from '@/lib/formValidationSchemas'; // ContactFormValues eklendi
// import { Resend } from 'resend'; // TODO: E-posta gönderimi aktif edildiğinde bu satırı da aktif et.

interface ActionResult {
  success: boolean;
  message?: string | null;
  error?: string | null;
  errors?: z.ZodIssue[];
}

// const resend = new Resend(process.env.RESEND_API_KEY); // TODO: E-posta gönderimi aktif edildiğinde bu satırı da aktif et.

export async function sendContactMessage(
  prevState: ActionResult | undefined,
  formDataOrValues: FormData | ContactFormValues,
): Promise<ActionResult> {
  try {
    let rawFormData: ContactFormValues;

    if (formDataOrValues instanceof FormData) {
      // FormData'dan verileri al
      const formData = formDataOrValues;
      rawFormData = {
        name: formData.get('name') as string,
        email: formData.get('email') as string,
        phone: (formData.get('phone') as string) || undefined, // Opsiyonel olduğu için undefined olabilir
        subject: formData.get('subject') as string,
        message: formData.get('message') as string,
        privacyPolicyAccepted: formData.get('privacyPolicyAccepted') === 'on',
      };
    } else {
      // Doğrudan ContactFormValues objesi geldiyse onu kullan
      rawFormData = formDataOrValues;
    }

    const validatedFields = contactFormSchema.safeParse(rawFormData);

    if (!validatedFields.success) {
      // Removed console.error - replaced with appropriate error return
      return {
        success: false,
        error: 'Lütfen formdaki hataları düzeltin.',
        errors: validatedFields.error.issues,
      };
    }

    const { name, email, phone, subject, message, privacyPolicyAccepted } = validatedFields.data; // privacyPolicyAccepted eklendi

    // TODO: Alan adı doğrulaması (almancaabc.com) Resend üzerinde tamamlandıktan sonra bu bölümü aktif edin.
    // // E-posta gönderme işlemi
    // try {
    //   const { data, error: emailError } = await resend.emails.send({
    //     from: 'AlmancaABC İletişim <<EMAIL>>', // DEĞİŞTİRİN: Resend'de doğrulanmış bir gönderen adresi
    //     to: ['<EMAIL>'], // Alıcı adresi
    //     subject: `Yeni İletişim Formu: ${subject} (Gönderen: ${name})`,
    //     replyTo: email,
    //     html: `
    //       <h1>Yeni İletişim Formu Mesajı</h1>
    //       <p><strong>Ad Soyad:</strong> ${name}</p>
    //       <p><strong>E-posta:</strong> ${email}</p>
    //       ${phone ? `<p><strong>Telefon:</strong> ${phone}</p>` : ''}
    //       <p><strong>Konu:</strong> ${subject}</p>
    //       <hr>
    //       <p><strong>Mesaj:</strong></p>
    //       <p>${message.replace(/\n/g, '<br>')}</p>
    //     `,
    //   });

    //   if (emailError) {
    //     console.error('Resend e-posta gönderme hatası:', emailError);
    //     return {
    //       success: false,
    //       error: 'E-postanız gönderilirken bir sorun oluştu. Lütfen daha sonra tekrar deneyin veya doğrudan <EMAIL> adresine e-posta gönderin.',
    //     };
    //   }

    //   console.log('E-posta başarıyla gönderildi:', data);
    //   return {
    //     success: true,
    //     message: 'Mesajınız başarıyla gönderildi! En kısa sürede size dönüş yapacağız.',
    //   };

    // } catch (emailServiceError) {
    //   console.error('Resend servisiyle iletişimde hata:', emailServiceError);
    //   return {
    //     success: false,
    //     error: 'E-posta servisiyle iletişim kurulamadı. Lütfen daha sonra tekrar deneyin.',
    //   };
    // }

    // Şimdilik konsola yazdırma aktif
    console.log('Alınan İletişim Mesajı (TODO: E-posta gönderimi aktif edilecek):');
    console.log('Ad Soyad:', name);
    console.log('E-posta:', email);
    if (phone) {
      console.log('Telefon:', phone);
    }
    console.log('Konu:', subject);
    console.log('Mesaj:', message);
    console.log('Gizlilik Politikası Kabulü:', privacyPolicyAccepted);

    // Removed console.log statements - keeping only TODO comments for development tracking
    return {
      success: true,
      message: 'Mesajınız başarıyla alındı. En kısa sürede size dönüş yapacağız.',
    };

  } catch (error) {
    // Removed console.error - replaced with appropriate error return
    return {
      success: false,
      error: 'Mesajınız işlenirken beklenmedik bir hata oluştu. Lütfen daha sonra tekrar deneyin.',
    };
  }
}

// Form state tipi tanımı
interface ContactFormState {
  message?: string;
  errors?: {
    name?: string[];
    email?: string[];
    phone?: string[];
    subject?: string[];
    message?: string[];
    privacyPolicyAccepted?: string[];
  };
}

export async function submitContactForm(prevState: ContactFormState, formData: FormData) {
  try {
    const validatedFields = ContactFormSchema.safeParse({
      name: formData.get('name'),
      email: formData.get('email'),
      phone: formData.get('phone'),
      subject: formData.get('subject'),
      message: formData.get('message'),
      privacyPolicyAccepted: formData.get('privacyPolicyAccepted') === 'on',
    });

    if (!validatedFields.success) {
      // console.error('Doğrulama hatası:', validatedFields.error.flatten().fieldErrors);
      return {
        errors: validatedFields.error.flatten().fieldErrors,
        message: 'Form doğrulama hatası. Lütfen tüm alanları kontrol edin.',
      };
    }

    const { name, email, phone, subject, message, privacyPolicyAccepted } = validatedFields.data;

    // E-posta gönderimi (şimdilik devre dışı)
    // try {
    //   const { data, error: emailError } = await resend.emails.send({
    //     from: 'AlmancaABC <<EMAIL>>',
    //     to: ['<EMAIL>'],
    //     subject: `İletişim Formu: ${subject}`,
    //     html: `
    //       <h2>Yeni İletişim Mesajı</h2>
    //       <p><strong>Ad Soyad:</strong> ${name}</p>
    //       <p><strong>E-posta:</strong> ${email}</p>
    //       <p><strong>Telefon:</strong> ${phone || 'Belirtilmemiş'}</p>
    //       <p><strong>Konu:</strong> ${subject}</p>
    //       <p><strong>Mesaj:</strong></p>
    //       <p>${message}</p>
    //       <p><strong>Gizlilik Politikası Kabul Edildi:</strong> ${privacyPolicyAccepted ? 'Evet' : 'Hayır'}</p>
    //     `,
    //   });
    //
    //   if (emailError) {
    //     // console.error('Resend e-posta gönderme hatası:', emailError);
    //     throw emailError;
    //   }
    //
    //   // console.log('E-posta başarıyla gönderildi:', data);
    // } catch (emailServiceError) {
    //   // E-posta servisi hatası durumunda da formu başarılı olarak işaretle
    //   // ancak hatayı logla
    //   // console.error('Resend servisiyle iletişimde hata:', emailServiceError);
    // }

    // Geçici olarak console.log ile mesajı göster (e-posta servisi aktif olmadığı için)
    // TODO: E-posta servisi aktif edildiğinde bu kısım kaldırılacak
    // console.log('Alınan İletişim Mesajı (TODO: E-posta gönderimi aktif edilecek):');
    // console.log('Ad Soyad:', name);
    // console.log('E-posta:', email);
    // console.log('Telefon:', phone);
    // console.log('Konu:', subject);
    // console.log('Mesaj:', message);
    // console.log('Gizlilik Politikası Kabulü:', privacyPolicyAccepted);

    // All console.log statements have been removed/commented out
    return {
      message: 'Mesajınız başarıyla gönderildi. En kısa sürede size dönüş yapacağız.',
    };
  } catch (error) {
    // Removed console.error - replaced with appropriate error return
    return {
      message: 'Mesaj gönderilirken bir hata oluştu. Lütfen tekrar deneyin.',
    };
  }
}