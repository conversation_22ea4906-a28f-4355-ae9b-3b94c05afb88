import React from 'react';
import { Toolt<PERSON>, Toolt<PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface VerifiedBadgeProps {
  isVerified?: boolean;
  className?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'auto'; // xs: çok küçük, sm: küçük, md: orta, lg: b<PERSON>yük, auto: parent'a göre
  variant?: 'outline' | 'solid' | 'simple'; // outline: çerçeveli, solid: dolgulu, simple: basit
}

export function VerifiedBadge({ 
  isVerified, 
  className,
  size = 'auto', 
  variant = 'simple'
}: VerifiedBadgeProps) {
  if (!isVerified) {
    return null;
  }

  // Size sınıfları
  const sizeClasses = {
    xs: "min-w-3 min-h-3 w-3 h-3",
    sm: "min-w-4 min-h-4 w-4 h-4",
    md: "min-w-5 min-h-5 w-5 h-5",
    lg: "min-w-6 min-h-6 w-6 h-6",
    auto: "w-4 h-4 sm:w-5 sm:h-5"
  };
  // Varyant sınıfları
  const variantClasses = {
    outline: "text-blue-600", // Halka kaldırıldı, sadece mavi renk kaldı
    solid: "text-white bg-blue-600 rounded-full p-0.5",
    simple: "text-blue-600" // Varsayılan 
  };
  
  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className={cn(
            "inline-flex items-center justify-center flex-shrink-0",
            variantClasses[variant],
            className
          )}>            <svg 
              className={cn(sizeClasses[size])} 
              viewBox="0 0 40 40" 
              fill="currentColor"
              style={{ flexShrink: 0, minWidth: 'fit-content' }}
            >
              <path d="M19.998 3.094 14.638 0l-2.972 5.15H5.432v6.354L0 14.64 3.094 20 0 25.359l5.432 3.137v5.905h5.975L14.638 40l5.36-3.094L25.358 40l3.232-5.6h6.162v-6.01L40 25.359 36.905 20 40 14.641l-5.248-3.03v-6.46h-6.419L25.358 0l-5.36 3.094Zm7.415 11.225 2.254 2.287-11.43 11.5-6.835-6.93 2.244-2.258 4.587 4.581 9.18-9.18Z"></path>
            </svg>
          </span>
        </TooltipTrigger>
        <TooltipContent>
          <p>Doğrulandı</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
