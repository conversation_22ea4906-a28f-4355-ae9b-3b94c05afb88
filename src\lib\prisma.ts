// src/lib/prisma.ts
import { PrismaClient } from '@prisma/client';

// Global scope'ta PrismaClient örneği oluşturma (Next.js geliştirme ortamında HMR ile uyumluluk için)
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    // İsteğe bağlı: Geliştirme ortamında sorgu loglarını görmek için
    // log: ['query', 'info', 'warn', 'error'],
  });

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

export default prisma;