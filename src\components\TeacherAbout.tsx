"use client" // Bu bileşen muhtemelen client tarafında state tutmayacak, sonra kaldırılabilir

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card" // Düzeltildi
import { Badge } from "@/components/ui/badge" // Düzeltildi
import { GraduationCap, Briefcase, Award } from "lucide-react"
import type { TeacherProfile } from "@/types/teacher" // Düzeltildi

interface TeacherAboutProps {
  teacher: TeacherProfile
}

export function TeacherAbout({ teacher }: TeacherAboutProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Hakkında</CardTitle>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Hakkında Metni */}
        {teacher.about && (
          <div className="space-y-4">
            <h3 className="font-semibold">Ken<PERSON>i <PERSON>kında</h3>
            <p className="text-muted-foreground whitespace-pre-wrap">{teacher.about}</p> {/* whitespace-pre-wrap eklendi */}
          </div>
        )}

        {/* U<PERSON><PERSON><PERSON><PERSON>/}
        {teacher.specialties && teacher.specialties.length > 0 && (
          <div className="space-y-4">
            <h3 className="font-semibold">Uzmanlık Alanları</h3>
            <div className="flex flex-wrap gap-2">
              {teacher.specialties.map((specialty) => (
                <Badge key={specialty} variant="secondary">
                  {specialty}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Eğitim */}
        {teacher.education && teacher.education.length > 0 && (
          <div className="space-y-4">
            <h3 className="font-semibold flex items-center gap-2">
              <GraduationCap className="w-5 h-5 text-muted-foreground" /> {/* İkon rengi eklendi */}
              Eğitim
            </h3>
            <div className="space-y-4">
              {teacher.education.map((edu, index) => (
                <div key={index} className="flex justify-between items-start gap-2"> {/* gap eklendi */}
                  <div>
                    <p className="font-medium">{edu.degree}</p>
                    <p className="text-sm text-muted-foreground">{edu.institution}</p>
                  </div>
                  {/* Yıl için Badge yerine basit text */}
                  <span className="text-sm text-muted-foreground flex-shrink-0">{edu.year}</span>
                  {/* Doğrulama için Badge kullanılabilir */}
                  {/* {edu.verified && <Badge variant="default">Doğrulandı</Badge>} */}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Deneyim */}
        {teacher.experience && teacher.experience.length > 0 && (
          <div className="space-y-4">
            <h3 className="font-semibold flex items-center gap-2">
              <Briefcase className="w-5 h-5 text-muted-foreground" /> {/* İkon rengi eklendi */}
              Deneyim
            </h3>
            <div className="space-y-4">
              {teacher.experience.map((exp, index) => (
                <div key={index} className="flex justify-between items-start gap-2"> {/* gap eklendi */}
                  <div>
                    <p className="font-medium">{exp.role}</p>
                    <p className="text-sm text-muted-foreground">{exp.company}</p>
                  </div>
                   {/* Süre için Badge yerine basit text */}
                  <span className="text-sm text-muted-foreground flex-shrink-0">{exp.duration}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Sertifikalar */}
        {teacher.certificates && teacher.certificates.length > 0 && (
          <div className="space-y-4">
            <h3 className="font-semibold flex items-center gap-2">
              <Award className="w-5 h-5 text-muted-foreground" /> {/* İkon rengi eklendi */}
              Sertifikalar
            </h3>
            <div className="space-y-4">
              {teacher.certificates.map((cert, index) => (
                <div key={index} className="flex justify-between items-start gap-2"> {/* gap eklendi */}
                  <div>
                    <p className="font-medium">{cert.name}</p>
                    <p className="text-sm text-muted-foreground">{cert.issuer}</p>
                  </div>
                   {/* Yıl için Badge yerine basit text */}
                   <span className="text-sm text-muted-foreground flex-shrink-0">{cert.year}</span>
                   {/* Doğrulama için Badge kullanılabilir */}
                   {/* {cert.verified && <Badge variant="default">Doğrulandı</Badge>} */}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}