"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { toast } from "@/hooks/use-toast";
import { createBooking } from "@/lib/actions/booking.actions";
import PaymentForm from "@/components/payment/PaymentForm";
import type { AvailabilitySlot } from "@prisma/client";
import { format } from "date-fns";
import { tr } from "date-fns/locale";

const bookingSchema = z.object({
  studentName: z.string().min(2, "İsim en az 2 karakter olmalıdır"),
  studentEmail: z.string().email("Geçerli bir e-posta adresi giriniz"),
  lessonType: z.string().min(1, "Ders türü seçiniz"),
  durationMinutes: z.number().min(30, "Ders süresi en az 30 dakika olmalıdır"),
  notes: z.string().optional(),
});

type BookingFormData = z.infer<typeof bookingSchema>;

interface BookingFormProps {
  slot: AvailabilitySlot;
  teacherId: string;
  teacherName: string;
  hourlyRate: number;
  studentId?: string;
  onSuccess: () => void;
  onCancel: () => void;
}

const lessonTypes = [
  { value: "TRIAL", label: "Deneme Dersi", duration: 30 },
  { value: "STANDARD", label: "Standart Ders", duration: 60 },
  { value: "INTENSIVE", label: "Yoğun Ders", duration: 90 },
  { value: "CONVERSATION", label: "Konuşma Pratiği", duration: 45 },
  { value: "EXAM_PREP", label: "Sınav Hazırlığı", duration: 60 },
];

export function BookingForm({
  slot,
  teacherId,
  teacherName,
  hourlyRate,
  studentId,
  onSuccess,
  onCancel,
}: BookingFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedLessonType, setSelectedLessonType] = useState<string>("");
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [bookingId, setBookingId] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<BookingFormData>({
    resolver: zodResolver(bookingSchema),
    defaultValues: {
      durationMinutes: 60,
    },
  });

  const durationMinutes = watch("durationMinutes");

  // Fiyat hesaplama
  const calculatePrice = (duration: number) => {
    return (hourlyRate * duration) / 60;
  };

  // Ders türü seçildiğinde süreyi otomatik ayarla
  const handleLessonTypeChange = (value: string) => {
    setSelectedLessonType(value);
    setValue("lessonType", value);

    const lessonType = lessonTypes.find(type => type.value === value);
    if (lessonType) {
      setValue("durationMinutes", lessonType.duration);
    }
  };

  // Ödeme başarılı olduğunda
  const handlePaymentSuccess = () => {
    setShowPaymentModal(false);
    toast({
      title: "Ödeme Başarılı!",
      description: "Ders rezervasyonunuz onaylandı. E-posta ile bilgilendirme gönderilecektir.",
    });
    onSuccess();
  };

  // Ödeme hatası olduğunda
  const handlePaymentError = (error: string) => {
    toast({
      title: "Ödeme Hatası",
      description: error,
      variant: "destructive",
    });
  };

  const onSubmit = async (data: BookingFormData) => {
    setIsSubmitting(true);

    try {
      const bookingData = {
        studentId: studentId || "guest", // Misafir kullanıcı için
        teacherId,
        availabilitySlotId: slot.id,
        lessonTime: slot.startTime.toISOString(),
        durationMinutes: data.durationMinutes,
        pricePaid: calculatePrice(data.durationMinutes).toString(),
        notes: data.notes || "",
        name: data.studentName,
        email: data.studentEmail,
        courseTitle: data.lessonType,
        message: `${teacherName} ile ${format(slot.startTime, "d MMMM yyyy HH:mm", { locale: tr })} tarihinde ${data.durationMinutes} dakikalık ders rezervasyonu`,
      };

      const result = await createBooking(bookingData);

      if (result.success && result.bookingId) {
        // Rezervasyon başarılı, ödeme modalını aç
        setBookingId(result.bookingId);
        setShowPaymentModal(true);

        toast({
          title: "Rezervasyon Oluşturuldu!",
          description: "Ödeme işlemi için devam edin.",
        });
      } else {
        toast({
          title: "Rezervasyon Hatası",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Rezervasyon oluşturulurken bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {/* Ders Bilgileri */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">Ders Detayları</h3>
        <p className="text-sm text-gray-600">
          <strong>Öğretmen:</strong> {teacherName}
        </p>
        <p className="text-sm text-gray-600">
          <strong>Tarih:</strong> {format(slot.startTime, "d MMMM yyyy, EEEE", { locale: tr })}
        </p>
        <p className="text-sm text-gray-600">
          <strong>Saat:</strong> {format(slot.startTime, "HH:mm", { locale: tr })} - {format(slot.endTime, "HH:mm", { locale: tr })}
        </p>
      </div>

      {/* Öğrenci Bilgileri */}
      <div className="space-y-3">
        <div>
          <Label htmlFor="studentName">Adınız Soyadınız *</Label>
          <Input
            id="studentName"
            {...register("studentName")}
            placeholder="Adınız ve soyadınız"
          />
          {errors.studentName && (
            <p className="text-sm text-red-600">{errors.studentName.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="studentEmail">E-posta Adresiniz *</Label>
          <Input
            id="studentEmail"
            type="email"
            {...register("studentEmail")}
            placeholder="<EMAIL>"
          />
          {errors.studentEmail && (
            <p className="text-sm text-red-600">{errors.studentEmail.message}</p>
          )}
        </div>
      </div>

      {/* Ders Türü */}
      <div>
        <Label htmlFor="lessonType">Ders Türü *</Label>
        <Select onValueChange={handleLessonTypeChange} value={selectedLessonType}>
          <SelectTrigger>
            <SelectValue placeholder="Ders türünü seçiniz" />
          </SelectTrigger>
          <SelectContent>
            {lessonTypes.map((type) => (
              <SelectItem key={type.value} value={type.value}>
                {type.label} ({type.duration} dk)
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.lessonType && (
          <p className="text-sm text-red-600">{errors.lessonType.message}</p>
        )}
      </div>

      {/* Ders Süresi */}
      <div>
        <Label htmlFor="durationMinutes">Ders Süresi (dakika)</Label>
        <Input
          id="durationMinutes"
          type="number"
          min="30"
          max="120"
          step="15"
          {...register("durationMinutes", { valueAsNumber: true })}
        />
        {errors.durationMinutes && (
          <p className="text-sm text-red-600">{errors.durationMinutes.message}</p>
        )}
      </div>

      {/* Notlar */}
      <div>
        <Label htmlFor="notes">Ek Notlar (İsteğe bağlı)</Label>
        <Textarea
          id="notes"
          {...register("notes")}
          placeholder="Ders hakkında özel istekleriniz varsa buraya yazabilirsiniz..."
          rows={3}
        />
      </div>

      {/* Fiyat Bilgisi */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <div className="flex justify-between items-center">
          <span className="font-semibold">Toplam Ücret:</span>
          <span className="text-lg font-bold text-blue-600">
            {calculatePrice(durationMinutes).toFixed(2)} TL
          </span>
        </div>
        <p className="text-xs text-gray-600 mt-1">
          {hourlyRate} TL/saat × {durationMinutes} dakika
        </p>
      </div>

      {/* Butonlar */}
      <div className="flex gap-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
          className="flex-1"
        >
          İptal
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
          className="flex-1"
        >
          {isSubmitting ? "Rezervasyon Yapılıyor..." : "Rezervasyon Yap"}
        </Button>
      </div>
    </form>

    {/* Ödeme Modal */}
    <Dialog open={showPaymentModal} onOpenChange={setShowPaymentModal}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Ödeme İşlemi</DialogTitle>
        </DialogHeader>
        {bookingId && (
          <PaymentForm
            bookingId={bookingId}
            amount={calculatePrice(durationMinutes)}
            currency="try"
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
          />
        )}
      </DialogContent>
    </Dialog>
  </>
  );
}
