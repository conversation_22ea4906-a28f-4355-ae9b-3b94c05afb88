"use server";

import { stripe, toStripeAmount, calculateCommission } from "@/lib/stripe";
import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import Strip<PERSON> from "stripe";
import { Resend } from "resend";

// E-posta sistemi
const resend = new Resend(process.env.RESEND_API_KEY);
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || 'AlmancaABC <<EMAIL>>';

interface CreatePaymentIntentParams {
  bookingId: string;
  amount: number;
  currency?: string;
  metadata?: Record<string, string>;
}

interface PaymentIntentResult {
  success: boolean;
  clientSecret?: string;
  paymentIntentId?: string;
  error?: string;
}

// PaymentIntent oluşturma
export async function createPaymentIntent({
  bookingId,
  amount,
  currency = "try",
  metadata = {},
}: CreatePaymentIntentParams): Promise<PaymentIntentResult> {
  try {
    // Booking'i kontrol et
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        student: true,
        teacher: true,
        payment: true,
      },
    });

    if (!booking) {
      return {
        success: false,
        error: "Rezervasyon bulunamadı.",
      };
    }

    // Zaten ödeme yapılmış mı kontrol et
    if (booking.payment) {
      return {
        success: false,
        error: "Bu rezervasyon için zaten ödeme yapılmış.",
      };
    }

    // Komisyon hesapla
    const commissionAmount = calculateCommission(amount);
    const stripeAmount = toStripeAmount(amount);

    // PaymentIntent oluştur
    const paymentIntent = await stripe.paymentIntents.create({
      amount: stripeAmount,
      currency: currency.toLowerCase(),
      payment_method_types: ["card"],
      metadata: {
        bookingId,
        studentId: booking.studentId,
        teacherId: booking.teacherId,
        commissionAmount: commissionAmount.toString(),
        ...metadata,
      },
      description: `AlmancaABC - ${booking.teacher.firstName} ${booking.teacher.lastName} ile ders rezervasyonu`,
    });

    // Payment kaydı oluştur
    await prisma.payment.create({
      data: {
        stripePaymentIntentId: paymentIntent.id,
        amount: amount,
        currency: currency.toUpperCase(),
        status: "pending",
        bookingId: bookingId,
      },
    });

    return {
      success: true,
      clientSecret: paymentIntent.client_secret!,
      paymentIntentId: paymentIntent.id,
    };
  } catch (error) {
    console.error("PaymentIntent oluşturma hatası:", error);
    return {
      success: false,
      error: "Ödeme işlemi başlatılırken bir hata oluştu.",
    };
  }
}

// Ödeme durumunu güncelleme
export async function updatePaymentStatus(
  paymentIntentId: string,
  status: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Payment kaydını güncelle
    const payment = await prisma.payment.update({
      where: { stripePaymentIntentId: paymentIntentId },
      data: { status },
      include: {
        booking: {
          include: {
            student: true,
            teacher: true,
            availabilitySlot: true,
          }
        }
      },
    });

    // Ödeme başarılıysa booking durumunu güncelle
    if (status === "succeeded") {
      await prisma.booking.update({
        where: { id: payment.bookingId },
        data: {
          status: "CONFIRMED",
          commissionRate: 0.15, // %15 komisyon
          commissionFee: calculateCommission(payment.amount.toNumber()),
        },
      });

      // Availability slot'u rezerve edilmiş olarak işaretle
      await prisma.availabilitySlot.update({
        where: { id: payment.booking.availabilitySlotId },
        data: { isBooked: true },
      });

      // E-posta bildirimleri gönder
      await sendPaymentConfirmationEmails(payment);
    }

    revalidatePath("/dashboard");
    revalidatePath("/teacher");
    revalidatePath("/student");

    return { success: true };
  } catch (error) {
    console.error("Ödeme durumu güncelleme hatası:", error);
    return {
      success: false,
      error: "Ödeme durumu güncellenirken bir hata oluştu.",
    };
  }
}

// Ödeme geçmişini getirme
export async function getPaymentHistory(userId: string, userType: "student" | "teacher") {
  try {
    const whereClause = userType === "student" 
      ? { booking: { studentId: userId } }
      : { booking: { teacherId: userId } };

    const payments = await prisma.payment.findMany({
      where: whereClause,
      include: {
        booking: {
          include: {
            student: true,
            teacher: true,
          },
        },
      },
      orderBy: { created_at: "desc" },
    });

    return payments;
  } catch (error) {
    console.error("Ödeme geçmişi getirme hatası:", error);
    return [];
  }
}

// Ödeme detaylarını getirme
export async function getPaymentDetails(paymentId: string) {
  try {
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        booking: {
          include: {
            student: true,
            teacher: true,
            availabilitySlot: true,
          },
        },
      },
    });

    return payment;
  } catch (error) {
    console.error("Ödeme detayları getirme hatası:", error);
    return null;
  }
}

// Geri ödeme işlemi
export async function processRefund(
  paymentIntentId: string,
  amount?: number,
  reason?: string
): Promise<{ success: boolean; error?: string; refundId?: string }> {
  try {
    // Refund oluştur
    const refund = await stripe.refunds.create({
      payment_intent: paymentIntentId,
      amount: amount ? toStripeAmount(amount) : undefined,
      reason: reason as Stripe.RefundCreateParams.Reason,
    });

    // Payment kaydını güncelle
    await prisma.payment.update({
      where: { stripePaymentIntentId: paymentIntentId },
      data: { status: "refunded" },
    });

    // Booking durumunu güncelle
    const payment = await prisma.payment.findUnique({
      where: { stripePaymentIntentId: paymentIntentId },
      include: { booking: true },
    });

    if (payment) {
      await prisma.booking.update({
        where: { id: payment.bookingId },
        data: { status: "CANCELLED" },
      });

      // Availability slot'u tekrar müsait yap
      await prisma.availabilitySlot.update({
        where: { id: payment.booking.availabilitySlotId },
        data: { isBooked: false },
      });
    }

    revalidatePath("/dashboard");
    revalidatePath("/teacher");
    revalidatePath("/student");

    return {
      success: true,
      refundId: refund.id,
    };
  } catch (error) {
    console.error("Geri ödeme hatası:", error);
    return {
      success: false,
      error: "Geri ödeme işlemi sırasında bir hata oluştu.",
    };
  }
}

// E-posta gönderme fonksiyonları
async function sendPaymentConfirmationEmails(payment: {
  amount: { toNumber: () => number };
  currency: string;
  booking: {
    id: string;
    durationMinutes: number;
    notes?: string | null;
    student: { firstName?: string | null; name?: string | null; email?: string | null } | null;
    teacher: { firstName?: string | null; name?: string | null; email?: string | null } | null;
    availabilitySlot: { startTime: Date };
  };
}) {
  if (!process.env.RESEND_API_KEY) {
    console.log("RESEND_API_KEY ayarlanmadığı için e-postalar gönderilmedi.");
    return;
  }

  try {
    const { booking } = payment;
    const { student, teacher, availabilitySlot } = booking;

    // Öğrenciye ödeme onayı e-postası
    if (student?.email) {
      await sendPaymentConfirmationToStudent({
        studentName: student.firstName || student.name || "Değerli Öğrencimiz",
        studentEmail: student.email,
        teacherName: teacher?.firstName || teacher?.name || "Öğretmeniniz",
        bookingId: booking.id,
        amount: payment.amount.toNumber(),
        currency: payment.currency,
        lessonTime: availabilitySlot.startTime,
        durationMinutes: booking.durationMinutes,
      });
    }

    // Öğretmene rezervasyon bildirimi
    if (teacher?.email) {
      await sendBookingNotificationToTeacher({
        teacherName: teacher.firstName || teacher.name || "Değerli Öğretmenimiz",
        teacherEmail: teacher.email,
        studentName: student?.firstName || student?.name || "Öğrenciniz",
        bookingId: booking.id,
        lessonTime: availabilitySlot.startTime,
        durationMinutes: booking.durationMinutes,
        notes: booking.notes || undefined,
      });
    }
  } catch (error) {
    console.error("E-posta gönderme hatası:", error);
  }
}

async function sendPaymentConfirmationToStudent(data: {
  studentName: string;
  studentEmail: string;
  teacherName: string;
  bookingId: string;
  amount: number;
  currency: string;
  lessonTime: Date;
  durationMinutes: number;
}) {
  const subject = `AlmancaABC - Ödeme Onayı ve Ders Rezervasyonu (ID: ${data.bookingId})`;
  const htmlBody = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #2563eb;">Ödeme Başarılı! 🎉</h2>

      <p>Merhaba ${data.studentName},</p>

      <p>AlmancaABC üzerinden yapmış olduğunuz ödeme başarıyla tamamlanmıştır ve ders rezervasyonunuz onaylanmıştır.</p>

      <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="color: #374151; margin-top: 0;">Rezervasyon Detayları</h3>
        <ul style="list-style: none; padding: 0;">
          <li><strong>Rezervasyon ID:</strong> ${data.bookingId}</li>
          <li><strong>Öğretmen:</strong> ${data.teacherName}</li>
          <li><strong>Tarih ve Saat:</strong> ${data.lessonTime.toLocaleString('tr-TR')}</li>
          <li><strong>Süre:</strong> ${data.durationMinutes} dakika</li>
          <li><strong>Ödenen Tutar:</strong> ${data.amount.toFixed(2)} ${data.currency.toUpperCase()}</li>
        </ul>
      </div>

      <div style="background-color: #dbeafe; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <p style="margin: 0; color: #1e40af;">
          <strong>Önemli:</strong> Dersinize zamanında katılmayı unutmayın!
          Ders saatinden 5 dakika önce hazır olmanızı öneririz.
        </p>
      </div>

      <p>Herhangi bir sorunuz olursa bizimle iletişime geçebilirsiniz.</p>

      <p>İyi dersler!</p>
      <p><strong>AlmancaABC Ekibi</strong></p>
    </div>
  `;

  try {
    await resend.emails.send({
      from: RESEND_FROM_EMAIL,
      to: [data.studentEmail],
      subject: subject,
      html: htmlBody,
    });
    console.log(`Ödeme onay e-postası öğrenciye gönderildi: ${data.studentEmail}`);
  } catch (error) {
    console.error(`Öğrenciye ödeme onay e-postası gönderilemedi:`, error);
  }
}

async function sendBookingNotificationToTeacher(data: {
  teacherName: string;
  teacherEmail: string;
  studentName: string;
  bookingId: string;
  lessonTime: Date;
  durationMinutes: number;
  notes?: string;
}) {
  const subject = `AlmancaABC - Yeni Ders Rezervasyonu Onaylandı (ID: ${data.bookingId})`;
  const htmlBody = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #059669;">Yeni Ders Rezervasyonu! 📚</h2>

      <p>Merhaba ${data.teacherName},</p>

      <p>Yeni bir ders rezervasyonu aldınız ve ödeme başarıyla tamamlanmıştır.</p>

      <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="color: #374151; margin-top: 0;">Rezervasyon Detayları</h3>
        <ul style="list-style: none; padding: 0;">
          <li><strong>Rezervasyon ID:</strong> ${data.bookingId}</li>
          <li><strong>Öğrenci:</strong> ${data.studentName}</li>
          <li><strong>Tarih ve Saat:</strong> ${data.lessonTime.toLocaleString('tr-TR')}</li>
          <li><strong>Süre:</strong> ${data.durationMinutes} dakika</li>
          ${data.notes ? `<li><strong>Öğrenci Notu:</strong> ${data.notes}</li>` : ''}
        </ul>
      </div>

      <div style="background-color: #d1fae5; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <p style="margin: 0; color: #065f46;">
          <strong>Hatırlatma:</strong> Lütfen takviminizi kontrol edin ve ders için hazırlıklarınızı yapın.
          Dersten 5 dakika önce hazır olmanızı öneririz.
        </p>
      </div>

      <p>Dashboard'unuzdan rezervasyon detaylarını görüntüleyebilirsiniz.</p>

      <p>İyi dersler!</p>
      <p><strong>AlmancaABC Ekibi</strong></p>
    </div>
  `;

  try {
    await resend.emails.send({
      from: RESEND_FROM_EMAIL,
      to: [data.teacherEmail],
      subject: subject,
      html: htmlBody,
    });
    console.log(`Rezervasyon bildirimi öğretmene gönderildi: ${data.teacherEmail}`);
  } catch (error) {
    console.error(`Öğretmene rezervasyon bildirimi gönderilemedi:`, error);
  }
}
