import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card"
import { RecommendedTeacherCard } from "@/components/RecommendedTeacherCard"

// Teacher tip<PERSON> (Prisma schema'dan t<PERSON>)
interface SerializedTeacher {
  id: string;
  firstName?: string | null;
  lastName?: string | null;
  title?: string | null;
  shortBio?: string | null;
  hourly_rate?: number | null;
  profile_image_url?: string | null;
  country?: string | null;
  city?: string | null;
  average_rating?: number | null;
  totalReviews?: number | null;
  specializations: string[];
  levels: string[];
  spokenLanguages: string[];
  stats?: {
    avgRating: number;
    reviewCount: number;
  } | string | null;
}

interface TeacherRecommendationsProps {
  currentTeacherId: string;
  teachers: SerializedTeacher[]; // any[] yerine SerializedTeacher[]
}

export function TeacherRecommendations({ currentTeacherId, teachers }: TeacherRecommendationsProps) {
  const filteredTeachers = teachers.filter(t => t.id !== currentTeacherId);

  if (!filteredTeachers || filteredTeachers.length === 0) {
      return null;
  }

  // Helper function to parse stats JSON safely
  const parseStats = (stats: SerializedTeacher['stats']) => {
    if (!stats) return { avgRating: 0, reviewCount: 0 };
    try {
      if (typeof stats === 'object' && stats !== null) return stats;
      if (typeof stats === 'string') return JSON.parse(stats);
    } catch (e) {
      // Error handling without console
    }
    return { avgRating: 0, reviewCount: 0 };
  };

  return (
    <Card className="mt-8 border-none shadow-none bg-transparent">
      <CardHeader className="px-0">
        <CardTitle className="text-2xl font-bold text-center">Benzer Öğretmenler</CardTitle>
        <CardDescription className="text-center">Bu öğretmenleri de beğenebilirsiniz</CardDescription>
      </CardHeader>
      <CardContent className="px-0">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTeachers.map((teacher: SerializedTeacher, index: number) => (
            <RecommendedTeacherCard key={teacher.id} teacher={teacher} index={index} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
