"use client"

import React from "react"
import { motion } from "framer-motion"
// import { useLanguage } from "@/lib/i18n/LanguageContext"
import { Card, CardContent } from "@/components/ui/card"
import { Star } from "lucide-react"
import Image from "next/image"

// Örnek kullanıcı yorumları
const testimonials = [
  {
    id: 1,
    name: "<PERSON><PERSON> Yılmaz",
    role: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Mühendisi",
    image: "https://randomuser.me/api/portraits/men/41.jpg",
    rating: 5,
    text: "AlmancaABC sayesinde 6 ay gibi kısa bir sürede A2 seviyesinden B2 seviyesine ulaştım. Öğretmenim Zeynep Hanım'ın sabırlı ve detaylı anlatımı sayesinde Almanca konuşma konusunda kendime güvenim arttı.",
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    role: "<PERSON>ı<PERSON>",
    image: "https://randomuser.me/api/portraits/women/63.jpg",
    rating: 5,
    text: "Almanya'da tıp eğitimi almak için Almanca öğrenmem gerekiyordu. AlmancaABC'deki öğretmenler sayesinde TestDaF sınavından yüksek puan aldım ve hayalimi gerçekleştirdim.",
  },
  {
    id: 3,
    name: "Mehmet Demir",
    role: "İşletme Sahibi",
    image: "https://randomuser.me/api/portraits/men/22.jpg",
    rating: 4,
    text: "İş için Almanya'ya sık seyahat ediyorum ve AlmancaABC'nin İş Almancası kursu sayesinde toplantılarda kendimi daha iyi ifade edebiliyorum. Esnek ders saatleri sayesinde yoğun iş tempoma rağmen düzenli çalışabildim.",
  },
  {
    id: 4,
    name: "Ayşe Öztürk",
    role: "Öğretmen",
    image: "https://randomuser.me/api/portraits/women/90.jpg",
    rating: 5,
    text: "Çocuklarım için Almanca öğretmeni arıyordum ve AlmancaABC'de harika bir öğretmen bulduk. Çocuklara özel ders materyalleri ve eğlenceli aktivitelerle Almanca öğrenmeyi sevdiler.",
  },
  {
    id: 5,
    name: "Can Yıldız",
    role: "Üniversite Öğrencisi",
    image: "https://randomuser.me/api/portraits/men/55.jpg",
    rating: 5,
    text: "Erasmus programı için Almanca öğrenmem gerekiyordu ve AlmancaABC'nin uygun fiyatlı grup dersleri tam ihtiyacıma göre oldu. 3 ay içinde temel seviyeye ulaştım.",
  },
  {
    id: 6,
    name: "Elif Şahin",
    role: "Mimar",
    image: "https://randomuser.me/api/portraits/women/33.jpg",
    rating: 4,
    text: "Almanya'da iş bulma sürecimde AlmancaABC'nin CV hazırlama ve mülakat teknikleri dersleri çok faydalı oldu. Şimdi Berlin'de hayalini kurduğum işte çalışıyorum.",
  },
]

export function Testimonials() {
  // const { t } = useLanguage()

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl font-bold mb-4"
          >
            Öğrenci Yorumları
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-gray-600 max-w-2xl mx-auto"
          >
            Binlerce öğrenci AlmancaABC ile Almanca öğrendi. İşte onların deneyimleri ve başarı hikayeleri.
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="relative h-12 w-12 rounded-full overflow-hidden mr-4">
                      <Image
                        src={testimonial.image}
                        alt={testimonial.name}
                        fill
                        sizes="48px"
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="font-semibold">{testimonial.name}</h3>
                      <p className="text-sm text-gray-500">{testimonial.role}</p>
                    </div>
                  </div>
                  <div className="flex mb-3">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < testimonial.rating ? "text-yellow-500 fill-yellow-500" : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <p className="text-gray-600 italic">&ldquo;{testimonial.text}&rdquo;</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}