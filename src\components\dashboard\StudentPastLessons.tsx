// src/components/dashboard/StudentPastLessons.tsx
import Link from "next/link";
import { getStudentPastLessons } from "@/lib/actions/student.actions";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { BookOpenCheck } from "lucide-react"; // CalendarX2 importu kaldırıldı
import { format } from "date-fns";
import { tr } from "date-fns/locale";

// Booking tipini action'dan gelen tipe göre ayarlayalım (veya import edelim)
type PastLesson = Awaited<ReturnType<typeof getStudentPastLessons>>[number];

function getStatusBadgeVariant(status?: string): "default" | "destructive" | "secondary" | "outline" {
  if (status === "COMPLETED") return "default"; // Veya "success" temalı bir variant
  if (status === "CANCELLED_BY_STUDENT" || status === "CANCELLED_BY_TEACHER" || status === "CANCELLED_BY_ADMIN") return "destructive";
  return "secondary";
}

export async function StudentPastLessons({ limit = 3 }: { limit?: number }) {
  const lessons = await getStudentPastLessons({ limit });

  if (!lessons || lessons.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium flex items-center">
            <BookOpenCheck className="mr-2 h-5 w-5 text-muted-foreground" />
            Ders Geçmişi
          </CardTitle>
          <CardDescription>Yakın zamanda tamamlanmış veya iptal edilmiş ders bulunmamaktadır.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Tamamladığınız dersler burada görünecektir.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base font-medium flex items-center">
            <BookOpenCheck className="mr-2 h-5 w-5 text-muted-foreground" />
            Son Dersler
        </CardTitle>
        <CardDescription>Tamamlanmış veya iptal edilmiş son dersleriniz.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        {lessons.map((lesson: PastLesson) => (
          <div
            key={lesson.id}
            className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
          >
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10 hidden sm:flex">
                <AvatarImage
                  src={lesson.teacherProfileImageUrl || "/placeholder.svg"}
                  alt={lesson.teacherName}
                />
                <AvatarFallback>
                  {lesson.teacherName?.split(" ")[0]?.[0]}
                  {lesson.teacherName?.split(" ")[1]?.[0]}
                </AvatarFallback>
              </Avatar>
              <div className="flex-grow">
                <p className="text-sm font-medium leading-none">
                  {lesson.teacherName}
                </p>
                <p className="text-xs text-muted-foreground">
                  {format(new Date(lesson.lessonTime), "PPPp", { locale: tr })}
                </p>
              </div>
            </div>
            <div className="flex flex-col items-end space-y-1">
                <Badge variant={getStatusBadgeVariant(lesson.status)} className="text-xs">
                  {lesson.status === "COMPLETED" && "Tamamlandı"}
                  {(lesson.status === "CANCELLED_BY_STUDENT" || lesson.status === "CANCELLED_BY_TEACHER" || lesson.status === "CANCELLED_BY_ADMIN") && "İptal Edildi"}
                  {/* Diğer durumlar için de gösterim eklenebilir */}
                  {!(lesson.status === "COMPLETED" || lesson.status === "CANCELLED_BY_STUDENT" || lesson.status === "CANCELLED_BY_TEACHER" || lesson.status === "CANCELLED_BY_ADMIN") && lesson.status}
                </Badge>
                {/* <Link href={`/student/lessons/${lesson.id}`} passHref>
                  <Button variant="ghost" size="sm" className="text-xs h-7">
                    Detay
                  </Button>
                </Link> */}
            </div>
          </div>
        ))}
         {lessons.length >= limit && (
            <div className="mt-4 text-center">
                <Button asChild variant="outline" size="sm">
                    {/* TODO: Tüm ders geçmişi sayfasına link */}
                    <Link href="#">Tüm Ders Geçmişi</Link>
                </Button>
            </div>
        )}
      </CardContent>
    </Card>
  );
}