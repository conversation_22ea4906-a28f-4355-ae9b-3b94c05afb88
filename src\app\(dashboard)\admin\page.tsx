// src/app/(dashboard)/admin/page.tsx
import { StatCard } from "@/components/dashboard/StatCard";
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"; // Artık kullanılmıyor
import { Users, GraduationCap, CalendarCheck, UserCheck } from "lucide-react"; // Activity ikonu kaldırıldı
import {
  getTotalTeachersCount,
  getTotalStudentsCount,
  getTotalBookingsCount,
  getPendingTeachersCount
} from "@/lib/actions/admin.actions";
import { PendingApplicationsCard } from "@/components/dashboard/PendingApplicationsCard";
import { RecentActivitiesCard } from "@/components/dashboard/RecentActivitiesCard"; // Yeni bileşen import edildi

// Örnek stats ve recentActivities verileri kaldırıldı

export default async function AdminDashboardPage() { // Sayfa async yapıldı
  // Paralel veri çekme
  const [
    totalTeachers,
    totalStudents,
    totalBookings,
    pendingTeachers
  ] = await Promise.all([
    getTotalTeachersCount(),
    getTotalStudentsCount(),
    getTotalBookingsCount(),
    getPendingTeachersCount()
  ]);

  return (
    <div className="flex flex-col gap-6">
      <h1 className="text-2xl font-semibold">Admin Paneli</h1>

      {/* İstatistik Kartları */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Toplam Öğretmen"
          value={totalTeachers}
          icon={Users}
          // change="+%5" // Örnek değişim
          // changeType="increase"
        />
         <StatCard
          title="Toplam Öğrenci"
          value={totalStudents} // TODO: Gerçek veri gelince düzeltilecek (Student modeli yok)
          icon={GraduationCap}
           // change="+%12"
          // changeType="increase"
        />
         <StatCard
          title="Toplam Ders"
          value={totalBookings} // TODO: Gerçek veri gelince düzeltilecek (Booking modeli yok)
          icon={CalendarCheck}
           // change="-%2"
          // changeType="decrease"
        />
         <StatCard
          title="Onay Bekleyen Öğrt."
          value={pendingTeachers}
          icon={UserCheck}
        />
      </div>

      {/* Diğer Bölümler (Yer Tutucu) */}
      <div className="grid gap-6 md:grid-cols-2">
         {/* Bekleyen Başvurular Kartı */}
         <PendingApplicationsCard />
         {/* Son Aktiviteler Kartı */}
         <RecentActivitiesCard />
      </div>
    </div>
  );
}
