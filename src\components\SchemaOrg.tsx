"use client"

export default function SchemaOrg() {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "EducationalOrganization",
    "name": "AlmancaABC",
    "url": "https://almancaabc.com",
    "logo": "https://almancaabc.com/logo.png",
    "description": "Online Almanca dil kursları ve özel dersler sunan eğitim platformu.",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "Türkiye"
    },
    "sameAs": [
      "https://www.facebook.com/almancaabc",
      "https://www.instagram.com/almancaabc"
    ],
    "offers": {
      "@type": "Offer",
      "price": "199.99",
      "priceCurrency": "TRY",
      "availability": "https://schema.org/InStock",
    }
  };

  return (
    <script
      type="application/ld+json"
      suppressHydrationWarning
    >
      {JSON.stringify(jsonLd)}
    </script>
  );
}