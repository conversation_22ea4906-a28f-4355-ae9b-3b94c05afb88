
import React from 'react';
import { Video, PlayCircle, ChevronLeft, ChevronRight } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Teacher } from '@/types';

interface VideoModalProps {
  showVideoModal: boolean;
  onClose: () => void;
  teacher: Teacher;
  currentVideoIndex: number;
  onVideoIndexChange: (index: number) => void;
}

export const VideoModal: React.FC<VideoModalProps> = ({
  showVideoModal,
  onClose,
  teacher,
  currentVideoIndex,
  onVideoIndexChange
}) => {
  return (
    <Dialog open={showVideoModal} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Video className="w-5 h-5 text-blue-500" />
            {teacher.videoCourses[currentVideoIndex]?.title}
          </DialogTitle>
        </DialogHeader>
        
        <div className="aspect-video bg-gray-900 rounded-lg overflow-hidden">
          <div className="w-full h-full flex items-center justify-center text-white">
            <div className="text-center">
              <PlayCircle className="w-16 h-16 mx-auto mb-4" />
              <p>Video oynatıcı burada gösterilecek</p>
            </div>
          </div>
        </div>
        
        <div className="flex gap-2 justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onVideoIndexChange(Math.max(0, currentVideoIndex - 1))}
            disabled={currentVideoIndex === 0}
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Önceki
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onVideoIndexChange(Math.min(teacher.videoCourses.length - 1, currentVideoIndex + 1))}
            disabled={currentVideoIndex === teacher.videoCourses.length - 1}
          >
            Sonraki
            <ChevronRight className="w-4 h-4 ml-1" />
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
