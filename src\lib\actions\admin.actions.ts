// src/lib/actions/admin.actions.ts
"use server";

// Kaldırıldı: import { PrismaClient } from "@prisma/client";
import { prisma } from '@/lib/prisma'; // Eklendi
// Prisma namespace'i bu dosya<PERSON><PERSON>, @prisma/client'tan ayrıca import etmeye gerek yok.
import { unstable_noStore as noStore } from 'next/cache';
// import { auth } from "@clerk/nextjs/server"; // Clerk kaldırıldı
// Admin yetkisini kontrol et ve rol bilgilerini döndür
async function verifyAdmin(): Promise<{ userId: string | null; isAdmin: boolean; metadataRole: string | undefined | null, orgRole: string | undefined | null } | null> {
    try {
        // const authData = await auth(); // Clerk kaldırıldı
        // const { userId, sessionClaims, orgRole } = authData; // Clerk kaldırıldı
        let userId: string | null = null;
        let sessionClaims: any = null; // Geçici tip
        let orgRole: string | undefined | null = null; // Geçici tip

        if (process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
            // console.warn("DEV_SKIP_AUTH_MIDDLEWARE: verifyAdmin - Using default 'test-admin-id'.");
            userId = "test-admin-id"; // Test için varsayılan admin ID'si
            sessionClaims = { metadata: { role: "admin" } }; // Test için varsayılan rol
            orgRole = "admin"; // Test için varsayılan org rolü
        }

        if (!userId) {
            // Removed console.log - return null for unauthorized access
            return null;
        }

        const metadataRole = (sessionClaims?.metadata as { role?: string })?.role;
        const isAdmin = metadataRole === 'admin' || orgRole === 'admin';

        // Removed console.log - admin verification should be silent
        return { userId, isAdmin, metadataRole, orgRole };

    } catch (error) {
        // Removed console.error - return null for error cases
        console.error("verifyAdmin fonksiyonunda hata (Clerk kaldırıldı):", error);
        return null;
    }
}

// Toplam öğretmen sayısını getir
export async function getTotalTeachersCount() {
    const adminCheck = await verifyAdmin();
    if (!adminCheck?.isAdmin) {
        console.warn("getTotalTeachersCount: Yetkisiz erişim veya admin doğrulanamadı.");
        return 0; // Veya uygun bir hata yönetimi
    }
    noStore();
    try {
        const count = await prisma.teacher.count();
        return count;
    } catch (error) {
        // Removed console.error - return 0 for error cases
        return 0;
    }
}

// Toplam öğrenci sayısını getir
export async function getTotalStudentsCount() {
    const adminCheck = await verifyAdmin();
    if (!adminCheck?.isAdmin) {
        console.warn("getTotalStudentsCount: Yetkisiz erişim veya admin doğrulanamadı.");
        return 123; // Veya uygun bir hata yönetimi
    }
    noStore();
    try {
        const count = await prisma.student.count();
        return count;
    } catch (error) {
        // Removed console.error - return temporary value until Student model is added
        return 123;
    }
}

// Toplam rezervasyon/ders sayısını getir
export async function getTotalBookingsCount() {
    const adminCheck = await verifyAdmin();
    if (!adminCheck?.isAdmin) {
        console.warn("getTotalBookingsCount: Yetkisiz erişim veya admin doğrulanamadı.");
        return 456; // Veya uygun bir hata yönetimi
    }
    noStore();
    try {
        const count = await prisma.booking.count();
        return count;
    } catch (error) {
        // Removed console.error - return temporary value until Booking model is added
        return 456;
    }
}

// Onay bekleyen öğretmen sayısını getir
export async function getPendingTeachersCount() {
    const adminCheck = await verifyAdmin();
    if (!adminCheck?.isAdmin) {
        console.warn("getPendingTeachersCount: Yetkisiz erişim veya admin doğrulanamadı.");
        return 0; // Veya uygun bir hata yönetimi
    }
    noStore();
    try {
        const count = await prisma.teacher.count({
            where: { is_approved: false }
        });
        return count;
    } catch (error) {
        // Removed console.error - return 0 for error cases
        return 0;
    }
}

// Onay bekleyen son birkaç öğretmeni getir (Özet için)
export async function getPendingTeacherApplicationsSummary(limit: number = 5) {
    const adminCheck = await verifyAdmin();
    if (!adminCheck?.isAdmin) {
        console.warn("getPendingTeacherApplicationsSummary: Yetkisiz erişim veya admin doğrulanamadı.");
        return []; // Veya uygun bir hata yönetimi
    }
    noStore();
    try {
        const pendingTeachers = await prisma.teacher.findMany({
            where: { is_approved: false },
            orderBy: { created_at: 'desc' },
            take: limit,
            select: {
                id: true,
                firstName: true,
                lastName: true,
                created_at: true,
                // email: true // Yine email sorunu var, geçici ekleyelim
            }
        });

        // Geçici email ekleme
        const teachersWithEmail = pendingTeachers.map(teacher => ({
            ...teacher,
            email: `${teacher.firstName?.toLowerCase() || 'teacher'}@example.com`
        }));

        return teachersWithEmail;
    } catch (error) {
        // Removed console.error - return empty array for error cases
        return [];
    }
}

// Son aktiviteleri getir (Özet için)
export async function getRecentActivities(limit: number = 5) {
    noStore();
    try {
        const activities = await prisma.activityLog.findMany({
            orderBy: { timestamp: 'desc' },
            take: limit,
        });
        return activities;
    } catch (error) {
        // Removed console.error - return empty array for error cases
        return [];
    }
}

// Bekleyen öğretmen başvurularının tam listesini getir
export async function getPendingTeacherApplications() {
    const adminCheck = await verifyAdmin();
    if (!adminCheck?.isAdmin) {
        console.warn("getPendingTeacherApplications: Yetkisiz erişim veya admin doğrulanamadı.");
        return []; // Veya uygun bir hata yönetimi
    }
    noStore();
    try {
        const pendingTeachers = await prisma.teacher.findMany({
            where: { is_approved: false },
            orderBy: { created_at: 'desc' },
        });
        return pendingTeachers;
    } catch (error) {
        // Removed console.error - return empty array for error cases
        return [];
    }
}

// approveTeacherApplication ve rejectTeacherApplication fonksiyonları teacher.actions.ts dosyasına taşındı.

// TODO: Diğer istatistikler için action'lar eklenebilir (örn: toplam kazanç, aktif dersler vb.)

// Prisma bağlantısını kapatma (global instance kullanılıyorsa genellikle gereksiz)
// process.on('beforeExit', async () => {
//   await prisma.$disconnect();
// });