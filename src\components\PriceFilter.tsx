"use client"

import { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Slider } from '@/components/ui/slider'
import { Button } from '@/components/ui/button'

interface PriceFilterProps {
  value: [number, number]
  onChange: (value: [number, number]) => void
  onClose: () => void
}

export function PriceFilter({ value, onChange, onClose }: PriceFilterProps) {
  const [min, setMin] = useState(value[0].toString())
  const [max, setMax] = useState(value[1].toString())

  const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMin = e.target.value
    setMin(newMin)
    onChange([parseInt(newMin) || 0, parseInt(max) || 500])
  }

  const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMax = e.target.value
    setMax(newMax)
    onChange([parseInt(min) || 0, parseInt(newMax) || 500])
  }

  const handleSliderChange = (v: number[]) => {
    setMin(v[0].toString())
    setMax(v[1].toString())
    onChange([v[0], v[1]])
  }

  const handleClear = () => {
    setMin('0')
    setMax('500')
    onChange([0, 500])
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Input
          type="number"
          placeholder="Min"
          value={min}
          onChange={handleMinChange}
          className="w-20"
        />
        <span>-</span>
        <Input
          type="number"
          placeholder="Max"
          value={max}
          onChange={handleMaxChange}
          className="w-20"
        />
        <span>₺</span>
      </div>
      <Slider
        value={[parseInt(min) || 0, parseInt(max) || 500]}
        max={500}
        step={10}
        onValueChange={handleSliderChange}
      />
      <div className="flex justify-end gap-2 mt-4">
        <Button variant="outline" onClick={handleClear}>Temizle</Button>
        <Button onClick={onClose}>Uygula</Button>
      </div>
    </div>
  )
}
