"use client"

import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'

interface CountryFilterProps {
  selected: string[]
  onChange: (countries: string[]) => void
  onClose: () => void
}

const COUNTRIES = [
  { code: 'DE', name: 'Alma<PERSON>', flag: '🇩🇪' },
  { code: 'AT', name: 'Avustury<PERSON>', flag: '🇦🇹' },
  { code: 'CH', name: 'İsviçre', flag: '🇨🇭' }
]

export function CountryFilter({ selected, onChange, onClose }: CountryFilterProps) {
  const handleToggle = (code: string) => {
    if (selected.includes(code)) {
      onChange(selected.filter(c => c !== code))
    } else {
      onChange([...selected, code])
    }
  }

  const handleClear = () => onChange([])

  return (
    <div className="space-y-4">
      <div className="grid gap-2">
        {COUNTRIES.map(country => (
          <div key={country.code} className="flex items-center space-x-2">
            <Checkbox
              id={`country-${country.code}`}
              checked={selected.includes(country.code)}
              onCheckedChange={() => handleToggle(country.code)}
            />
            <Label htmlFor={`country-${country.code}`}>
              <span className="mr-2">{country.flag}</span>
              {country.name}
            </Label>
          </div>
        ))}
      </div>
      <div className="flex justify-end gap-2 mt-4">
        <Button variant="outline" onClick={handleClear}>Temizle</Button>
        <Button onClick={onClose}>Uygula</Button>
      </div>
    </div>
  )
}
