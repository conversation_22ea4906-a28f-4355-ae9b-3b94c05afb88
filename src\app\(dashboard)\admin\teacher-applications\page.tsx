// src/app/(dashboard)/admin/teacher-applications/page.tsx
import { getPendingTeacherApplications } from "@/lib/actions/admin.actions";
import { DataTable } from "@/components/dashboard/DataTable";
import { columns } from "./_components/columns";
import { Teacher } from "@prisma/client";

export default async function TeacherApplicationsPage() {
  // Bekleyen öğretmen başvurularını getir
  const applications: Teacher[] = await getPendingTeacherApplications();

  // TODO: Hata durumunu ele al (eğer applications boşsa veya hata varsa)

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-4">Bekleyen Öğretmen Başvuruları</h1>
      {/* Başvuruları DataTable bileşeni ile göster */}
      <DataTable columns={columns} data={applications} />
    </div>
  );
}
