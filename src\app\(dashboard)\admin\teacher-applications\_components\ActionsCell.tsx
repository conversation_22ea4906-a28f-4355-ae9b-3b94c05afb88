// src/app/(dashboard)/admin/teacher-applications/_components/ActionsCell.tsx
"use client";

import { Teacher } from "@prisma/client";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { approveTeacherApplication, rejectTeacherApplication } from "@/lib/actions/teacher.actions";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface ActionsCellProps {
  teacher: Teacher;
}

export const ActionsCell: React.FC<ActionsCellProps> = ({ teacher }) => {
  const router = useRouter(); // Hook'u burada kullan

  const handleApprove = async () => {
    try {
      const result = await approveTeacherApplication(teacher.id);
      if (result.success) {
        toast.success("Öğretmen başvurusu onaylandı.");
        router.refresh(); // Sayfayı yenile
      } else {
        toast.error(result.error || "Onaylama sırasında bir hata oluştu.");
      }
    } catch (error) {
      console.error("Approve error:", error);
      toast.error("Onaylama sırasında bir hata oluştu.");
    }
  };

  const handleReject = async () => {
    // TODO: Silme işlemi öncesi onay dialog'u eklenebilir.
    try {
      const result = await rejectTeacherApplication(teacher.id);
      if (result.success) {
        toast.success("Öğretmen başvurusu reddedildi ve silindi.");
        router.refresh(); // Sayfayı yenile
      } else {
        toast.error(result.error || "Reddetme sırasında bir hata oluştu.");
      }
    } catch (error) {
      console.error("Reject error:", error);
      toast.error("Reddetme sırasında bir hata oluştu.");
    }
  };

  return (
    <div className="flex space-x-2">
      <Button variant="outline" size="sm" onClick={handleApprove}>Onayla</Button>
      <Button variant="destructive" size="sm" onClick={handleReject}>Reddet</Button>
      {/* Detay sayfasına gitmek için link */}
      <Button variant="ghost" size="sm" asChild>
        {/* Öğretmen onaylandıktan sonra da detay sayfasına gidilebilmeli */}
        <Link href={`/admin/teachers/${teacher.id}`}>Detay</Link>
      </Button>
    </div>
  );
};