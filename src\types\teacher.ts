// src/types/teacher.ts

// =================================================================
// ISTEMCI TARAFI IÇIN TEK DOĞRULUK KAYNAĞI (SINGLE SOURCE OF TRUTH)
// Buradaki tüm tipler tamamen serileştirilebilir olmalıdır.
// (Date -> string, Decimal -> number, vb.)
// =================================================================

/**
 * Öğretmenin eğitim geçmişini istemci tarafında göstermek için kullanılır.
 * Tarihler ISO formatında string olarak tutulur.
 */
export interface ClientEducation {
  id: string;
  institution: string;
  university: string;
  degree: string | null;
  fieldOfStudy: string | null;
  startDate: string | undefined;
  endDate: string | undefined;
}

/**
 * Öğretmenin sertifikalarını istemci tarafında göstermek için kullanılır.
 * Tarihler ISO formatında string olarak tutulur.
 */
export interface ClientCertificate {
  id: string;
  name: string;
  issuer: string;
  issueDate: string | undefined;
}

/**
 * Öğrenci yorumlarını istemci tarafında göstermek için kullanılır.
 * Tarihler ISO formatında string olarak tutulur.
 */
export interface ClientReview {
  id: string;
  rating: number;
  comment: string | undefined;
  date: string; // Veritabanındaki 'createdAt' alanından dönüştürülür.
  student: {
    id: string;
    name: string | null;
    avatar: string | null;
  };
}

/**
 * Ders/kurs paketlerini istemci tarafında göstermek için genel bir tip.
 * Birebir, grup ve video kursları için kullanılır.
 */
export interface ClientPackage {
  id: string;
  name: string;
  description: string | undefined;
  price: number;
  lessonsInPackage: number | null;
  lessonDurationMinutes: number | null;
  pricePerLessonCalculated: number | null;
  discountPercentage: number | null;
  features: string[];
  level: string | undefined;
  isActive: boolean;
  thumbnailUrl: string | undefined;
  
  // Grup kursu özel alanları
  groupCourseStartDate?: string | null;
  enrolledStudentCount?: number | null;
  studentCapacity?: string | null;
  groupCourseSchedule?: string | null;

  // Video kursu özel alanları
  videoCourseTotalLessons?: number | null;
  videoCourseTotalHours?: number | null;
  youtubePlaylistUrl?: string | null;
  videoCourseIsPopular?: boolean | null;
  videoCourseMaterials?: string | null;

  // Zaman damgaları string olarak
  createdAt: string;
  updatedAt: string;
}

/**
 * Öğretmenin SSS girdilerini istemci tarafında göstermek için kullanılır.
 */
export interface ClientFaq {
  id: string;
  question: string;
  answer: string;
}

/**
 * Öğretmen profil sayfası için ana veri yapısı.
 * İstemci bileşenleri için gerekli tüm serileştirilmiş verileri içerir.
 */
export interface TeacherProfileClientData {
  id: string;
  name: string;
  firstName: string | null;
  lastName: string | null;
  email: string | undefined;
  avatar: string | undefined;
  bio: string | undefined;
  title: string | undefined;
  shortBio: string;
  specializations: string[];
  levels: string[];
  hourly_rate: number | null;
  intro_video_url: string | undefined;
  country: string | undefined;
  city: string | undefined;
  nativeLanguage: string | undefined;
  languages: string[];
  isVerified: boolean;
  isOnline: boolean;
  experienceYears: string | undefined;
  average_rating: number | null;
  totalReviews: number;
  activeStudentCount: number;
  completedLessons: number;
  timezone: string | null;
  
  // Zaman damgaları string olarak
  created_at: string;
  updated_at: string;

  // İlişkili veriler serileştirilmiş tipler olarak
  education: ClientEducation[];
  certificates: ClientCertificate[];
  reviews: ClientReview[];
  lessonPackages: ClientPackage[];
  oneOnOnePackages: ClientPackage[];
  groupCourses: ClientPackage[];
  videoCourses: ClientPackage[];
  teacherFaqs: ClientFaq[];
}

// Öğretmen kartları için kullanılan tip (mock data ile uyumlu)
export interface Teacher {
  id: number;
  name: string;
  avatar: string;
  rating: number;
  reviewCount: number;
  specializations: string[];
  price: number;
  badges: string[];
  levels: string[];
  country: string;
  availability: string[];
  languages: string[];
  is_verified: boolean;
  activeStudents: number;
  totalLessons: number;
  isSuperTeacher: boolean;
  spokenLanguages: Array<{
    language: string;
    level: string;
  }>;
  isOnline?: boolean;
}

// Eski tiplerle uyumluluk veya geçiş süreci için gerekebilecek diğer tipler
// Not: Bu tiplerin kullanımı zamanla azaltılmalı ve yukarıdaki standart
// Client tiplerine geçilmelidir.
export interface PackageOption {
  sessions?: number;
  duration?: string;
  durationMinutes?: number | null;
  price: number | string;
  perLesson?: number | string;
  discount?: string | null;
  name?: string;
  lessons?: string | number | null;
  popular?: boolean;
}
