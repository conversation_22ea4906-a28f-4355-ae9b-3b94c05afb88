"use server";

import { redirect } from 'next/navigation';
import { createSupabaseServerClient } from '@/utils/supabase/server';
import { ensureUserProfileExists } from './user.actions'; // ensureUserProfileExists
import { Role } from '@/types/user.types'; // Role enum'ı

export async function signUpWithEmailAndPassword(formData: FormData) {
  'use server';
  const supabase = createSupabaseServerClient();
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const fullName = formData.get('fullName') as string | null;
  const defaultRole = Role.STUDENT; // Veya Role.USER

  if (!email || !password) {
    return { error: 'E-posta ve şifre gereklidir.' };
  }

  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
        role: defaultRole, // Rolü user_metadata'ya ekle
      },
      // emailRedirectTo: `${process.env.NEXT_PUBLIC_BASE_URL}/auth/callback`, // E-posta onayı için
    },
  });

  if (error) {
    console.error('Supabase signUp error:', error.message);
    return { error: `Kayıt işlemi başarısız: ${error.message}` };
  }

  // data.user her zaman dolu olmalı eğer hata yoksa.
  if (!data.user) {
      return { error: 'Kullanıcı oluşturulamadı ancak bir hata da dönmedi. Beklenmedik durum.' };
  }

  // Kullanıcı var, şimdi profili oluşturalım/güncelleyelim
  // E-posta onayı gerekiyorsa (data.session null ise), bu işlem idealde onay callback'inde yapılmalı.
  // Şimdilik, session oluşsa da oluşmasa da ensureUserProfileExists'i çağıralım,
  // fonksiyonun kendisi Prisma entegrasyonu tamamlanana kadar sadece loglama yapacak.
  try {
    await ensureUserProfileExists(data.user.id, defaultRole, { email: data.user.email!, fullName });
  } catch (profileError: unknown) {
    // Profil oluşturma hatası olursa logla ama kullanıcıya genel bir mesaj ver.
    // Kullanıcı kaydı başarılı oldu ama profil senkronizasyonunda sorun olabilir.
    if (profileError instanceof Error) {
      console.error(`ensureUserProfileExists error for user ${data.user.id}:`, profileError.message);
    } else {
      console.error(`An unknown error occurred in ensureUserProfileExists for user ${data.user.id}:`, profileError);
    }
    // Bu hatayı kullanıcıya yansıtıp yansıtmamak duruma göre değişir.
    // Şimdilik sadece loglayalım.
  }

  if (data.session) {
    // Kullanıcı kaydoldu ve hemen bir oturum aldı (e-posta onayı kapalıysa)
    return { success: true, message: 'Kayıt başarılı! Yönlendiriliyorsunuz...', session: data.session, user: data.user };
  }
  
  if (!data.session && data.user) {
    // Kullanıcı kaydoldu ancak oturum yok (e-posta onayı bekleniyor olabilir)
    return { success: true, message: 'Kayıt başarılı! Lütfen e-postanızı kontrol ederek hesabınızı onaylayın.', user: data.user };
  }

  return { error: 'Beklenmedik bir durum oluştu signUp sonrası.' };
}

export async function signInWithEmailAndPassword(formData: FormData, redirectUrl?: string | null) {
  'use server';
  const supabase = createSupabaseServerClient();
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;

  if (!email || !password) {
    // Bu normalde client-side validasyon ile yakalanmalı
    throw new Error('E-posta ve şifre gereklidir.');
  }

  const { data: _data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    console.error('Supabase signIn error:', error.message);
    // Hata mesajını kullanıcıya göstermek için redirect yerine bir obje döndürebiliriz
    // redirect('/sign-in?error=' + encodeURIComponent(error.message));
    throw new Error(`Giriş yapılamadı: ${error.message}`); // Veya bir { error: ... } objesi döndür
  }

  // Başarılı giriş durumunda yönlendirme yapılır.
  // Middleware zaten bu yönlendirmeyi ve oturum kontrolünü yapıyor olmalı.
  // Ancak signIn sonrası özel bir yönlendirme isteniyorsa burada yapılabilir.
  // Genellikle middleware'in yönlendirmesi yeterli olur.
  // Eğer redirectUrl varsa oraya, yoksa varsayılan bir sayfaya yönlendir.
  redirect(redirectUrl || '/(dashboard)');
}

export async function signOut(redirectTo: string = '/') {
  'use server';
  const supabase = createSupabaseServerClient();
  const { error } = await supabase.auth.signOut();

  if (error) {
    console.error('Supabase signOut error:', error.message);
    // Kullanıcıya bir hata mesajı göstermek zor olabilir çünkü zaten çıkış yapıyor.
    // En iyisi loglamak ve basitçe yönlendirmek.
  }
  
  redirect(redirectTo);
}
