# Supabase MCP Sunuc<PERSON> (AlmancaABC Projesi)

## <PERSON><PERSON>u Bilgileri

- **<PERSON><PERSON><PERSON>layıcı:** `github.com/supabase-community/supabase-mcp`
- **<PERSON><PERSON><PERSON><PERSON><PERSON> (VSCode Eklentisi & Windows için Önerilen):**
  VSCode gibi bir eklenti or<PERSON>ı<PERSON> (örneğin, Cline/Roo) ve Windows işletim sisteminde Supabase MCP sunucusunu yapılandırırken, genellikle `cline_mcp_settings.json`,  `mcp.json`, `mcp_settings.json` (veya benzeri) bir ayar dosyası kullanılır. Bu dosyada sunucu aşağıdaki gibi tanımlanır:
  ```json
  {
    "mcpServers": {
      "github.com/supabase-community/supabase-mcp": {
        "command": "cmd",
        "args": [
          "/c",
          "npx",
          "-y",
          "@supabase/mcp-server-supabase@latest",
          "--access-token",
          "<erişim-jetonunuz>"
        ],
        "disabled": false,
        "autoApprove": []
      }
    }
  }
  ```
  **Not:** `<erişim-jetonunuz>` kısmını kendi Supabase erişim jetonunuzla değiştirmelisiniz.

- **Açıklama:** Projeleri, veritabanlarını, kimlik doğrulamayı ve daha fazlasını yönetmek dahil olmak üzere Supabase projeleriyle etkileşim için araçlar sağlar.
- **Alternatif Terminal Komutları (Doğrudan Çalıştırma):**
  Eğer sunucuyu doğrudan terminalden çalıştırmak isterseniz:
  ```bash
  # NPX ile çalıştırma (Windows'ta cmd /c gerekebilir)
  npx -y @supabase/mcp-server-supabase@latest --access-token=<erişim-jetonunuz>

  # Ortam değişkeni ile çalıştırma
  # PowerShell:
  $env:SUPABASE_ACCESS_TOKEN='<erişim-jetonunuz>'
  npx -y @supabase/mcp-server-supabase@latest
  # Bash/Zsh:
  export SUPABASE_ACCESS_TOKEN='<erişim-jetonunuz>'
  npx -y @supabase/mcp-server-supabase@latest

  # Bun ile çalıştırma
  bunx --bun @supabase/mcp-server-supabase@latest --access-token=<erişim-jetonunuz>

  # Proje kapsamı ile çalıştırma
  bunx --bun @supabase/mcp-server-supabase@latest --access-token=<erişim-jetonunuz> --project-ref=<proje-kimliği>

  # Salt okunur mod ile çalıştırma
  bunx --bun @supabase/mcp-server-supabase@latest --access-token=<erişim-jetonunuz> --read-only
  ```

## Bağlantı Sorunları ve Çözümleri

Eğer Supabase MCP sunucusuna bağlanırken "Error executing MCP tool: Not connected" gibi bir hata alırsanız, aşağıdaki adımları deneyebilirsiniz:

1.  **AlmancaABC Projesi için MCP Ayar Dosyası Kontrolü:**
    *   **Öncelikli Kontrol:** AlmancaABC projesinde Supabase MCP sunucusu için ayarlar öncelikle projenin kök dizinindeki **`./.roo/mcp.json`** dosyasında aranmalıdır. Bu dosya, projeye özgü MCP yapılandırmalarını içerir.
        ```json
        // Örnek .roo/mcp.json içeriği (Supabase kısmı):
        {
          "mcpServers": {
            "github.com/supabase-community/supabase-mcp": {
              "command": "cmd",
              "args": [
                "/c",
                "npx",
                "-y",
                "@supabase/mcp-server-supabase@latest",
                "--access-token",
                "YOUR_SUPABASE_ACCESS_TOKEN" // Buraya kendi jetonunuzu ekleyin
              ],
              "disabled": false,
              "autoApprove": []
            }
            // ... diğer sunucu tanımları ...
          }
        }
        ```
    *   **Alternatif Global Ayar Dosyaları:** Eğer `./.roo/mcp.json` dosyasında Supabase MCP sunucusu için bir yapılandırma bulunmuyorsa veya bu dosya mevcut değilse, VSCode eklentilerinin kullandığı genel (global) ayar dosyaları kontrol edilebilir. Bu dosyalar genellikle aşağıdaki yollarda bulunur (kullanıcı adınızı ve eklenti adını kendi sisteminize göre uyarlayın):
        *   `C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\rooveterinaryinc.roo-cline\settings\mcp_settings.json` (Roo Cline eklentisi için)
        *   `C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\saoudrizwan.claude-dev\settings\cline_mcp_settings.json` (Claude eklentisi için)
    *   **Yapılandırma Doğrulaması:** Hangi ayar dosyası kullanılırsa kullanılsın, "Başlangıç Komutu (VSCode Eklentisi & Windows için Önerilen)" başlığı altında gösterilen `cmd`, `/c`, `npx`, `-y` sıralamasının ve **doğru Supabase erişim jetonunun** (`access-token`) kullanıldığını teyit edin.
2.  **Doğru Sunucu Adı (`server_name`):**
    *   MCP aracını çağırırken kullandığınız `server_name` parametresinin, ayar dosyanızdaki Supabase sunucusu için tanımlanmış anahtarla (örneğin, `github.com/supabase-community/supabase-mcp`) tam olarak eşleştiğinden emin olun.
3.  **Erişim Jetonu (Access Token):**
    *   Kullandığınız Supabase erişim jetonunun geçerli ve doğru yetkilere sahip olduğundan emin olun.
4.  **VSCode / Eklenti Yeniden Başlatma:**
    *   VSCode'u veya ilgili eklentiyi (Cline/Roo) yeniden başlatmak, sunucuların tekrar düzgün bir şekilde yüklenmesine yardımcı olabilir.
5.  **İnternet Bağlantısı:**
    *   Supabase MCP sunucusunun çalışması için aktif bir internet bağlantısı gereklidir.

Bu adımlar, yaygın bağlantı sorunlarını çözmeye yardımcı olabilir.

## Kullanılabilir Araçlar

### `list_projects`
- **Açıklama:** Kullanıcının tüm Supabase projelerini listeler.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {},
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {}
  ```

### `get_project`
- **Açıklama:** Bir Supabase projesinin ayrıntılarını alır.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "id": { "type": "string", "description": "Proje Kimliği" }
    },
    "required": ["id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "id": "proje-kimliği"
  }
  ```

### `get_cost`
- **Açıklama:** Yeni bir proje veya şube oluşturmanın maliyetini alır. Kuruluşun maliyetleri farklı olabileceğinden, kuruluşu asla varsaymayın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "type": { "type": "string", "enum": ["project", "branch"] },
      "organization_id": { "type": "string", "description": "Kuruluş Kimliği. Her zaman kullanıcıya sorun." }
    },
    "required": ["type", "organization_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "type": "project",
    "organization_id": "kuruluş-kimliği"
  }
  ```

### `confirm_cost`
- **Açıklama:** Kullanıcıdan yeni bir proje veya şube oluşturmanın maliyetini anladığını onaylamasını isteyin. Önce `get_cost`'u çağırın. Bu onay için benzersiz bir kimlik döndürür ve bu kimlik `create_project` veya `create_branch`'e aktarılmalıdır.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "type": { "type": "string", "enum": ["project", "branch"] },
      "recurrence": { "type": "string", "enum": ["hourly", "monthly"] },
      "amount": { "type": "number" }
    },
    "required": ["type", "recurrence", "amount"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "type": "project",
    "recurrence": "monthly",
    "amount": 10
  }
  ```

### `create_project`
- **Açıklama:** Yeni bir Supabase projesi oluşturur. Her zaman kullanıcının projeyi hangi kuruluşta oluşturacağını sorun. Projenin başlatılması birkaç dakika sürebilir - durumu kontrol etmek için `get_project`'i kullanın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "name": { "type": "string", "description": "Projenin adı" },
      "region": {
        "type": "string",
        "enum": [
          "us-west-1", "us-east-1", "us-east-2", "ca-central-1",
          "eu-west-1", "eu-west-2", "eu-west-3", "eu-central-1", "eu-central-2", "eu-north-1",
          "ap-south-1", "ap-southeast-1", "ap-northeast-1", "ap-northeast-2", "ap-southeast-2",
          "sa-east-1"
        ],
        "description": "Projeyi oluşturmak için bölge. Varsayılan olarak en yakın bölgeye ayarlanır."
      },
      "organization_id": { "type": "string" },
      "confirm_cost_id": { "type": "string", "description": "Maliyet onay kimliği. Önce `confirm_cost`'u çağırın." }
    },
    "required": ["name", "organization_id", "confirm_cost_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "name": "yeni-proje",
    "region": "eu-central-1",
    "organization_id": "kuruluş-kimliği",
    "confirm_cost_id": "onay-kimliği"
  }
  ```

### `pause_project`
- **Açıklama:** Bir Supabase projesini duraklatır.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" }
    },
    "required": ["project_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği"
  }
  ```

### `restore_project`
- **Açıklama:** Bir Supabase projesini geri yükler.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" }
    },
    "required": ["project_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği"
  }
  ```

### `list_organizations`
- **Açıklama:** Kullanıcının üyesi olduğu tüm kuruluşları listeler.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {},
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {}
  ```

### `get_organization`
- **Açıklama:** Bir kuruluşun ayrıntılarını alır.
  ```json
  {
    "type": "object",
    "properties": {
      "id": { "type": "string" }
    },
    "required": ["id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "id": "kuruluş-kimliği"
  }
  ```

### `list_tables`
- **Açıklama:** Belirtilen şemalardaki tüm tabloları listeler.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" },
      "schemas": { 
        "type": "array",
        "items": { "type": "string" },
        "description": "Eklenecek şemaların listesi (varsayılan olarak ['public'])"
      }
    },
    "required": ["project_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği",
    "schemas": ["public", "auth"]
  }
  ```

### `list_extensions`
- **Açıklama:** Veritabanındaki tüm uzantıları listeler.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" }
    },
    "required": ["project_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği"
  }
  ```

### `list_migrations`
- **Açıklama:** Veritabanındaki tüm geçişleri listeler.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" }
    },
    "required": ["project_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği"
  }
  ```

### `apply_migration`
- **Açıklama:** Veritabanına bir SQL geçişi uygular. SQL passed to this tool will be tracked within the database, so LLMs should use this for DDL operations (schema changes).
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" },
      "sql": { "type": "string", "description": "The SQL to execute" }
    },
    "required": ["project_id", "sql"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği",
    "sql": "CREATE TABLE yeni_tablo (id UUID PRIMARY KEY);"
  }
  ```

### `execute_sql`
- **Açıklama:** Veritabanında ham SQL yürütür. LLMs should use this for regular queries that don't change the schema.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" },
      "sql": { "type": "string", "description": "The SQL to execute" }
    },
    "required": ["project_id", "sql"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği",
    "sql": "SELECT * FROM kullanıcılar;"
  }
  ```

### `get_logs`
- **Açıklama:** Bir Supabase projesi için hizmet türüne göre günlükleri alır. Uygulamanızla ilgili sorunları gidermeye yardımcı olması için bunu kullanın. Bu yalnızca son dakika içindeki günlükleri döndürür. Aradığınız günlükler 1 dakikadan eski ise, bunları yeniden üretmek için testinizi tekrar çalıştırın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" },
      "service": {
        "type": "string",
        "enum": [
          "api", "branch-action", "postgres", "edge-function",
          "auth", "storage", "realtime"
        ],
        "description": "Günlükleri getirmek için hizmet"
      }
    },
    "required": ["project_id", "service"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği",
    "service": "api"
  }
  ```

### `get_project_url`
- **Açıklama:** Bir proje için API URL'sini alır.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" }
    },
    "required": ["project_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği"
  }
  ```

### `get_anon_key`
- **Açıklama:** Bir proje için anonim API anahtarını alır.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" }
    },
    "required": ["project_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği"
  }
  ```

### `generate_typescript_types`
- **Açıklama:** Bir proje için TypeScript türleri oluşturur.
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" }
    },
    "required": ["project_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği"
  }
  ```

### `create_branch`
- **Açıklama:** Bir Supabase projesinde bir geliştirme şubesi oluşturur. Ana daldan geçişleri uygular, ancak verileri uygulamaz. Şube işlemleri için sonuçtaki `project_ref`'i `project_id` olarak kullanın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" },
      "name": { "type": "string", "default": "develop", "description": "Oluşturulacak şubenin adı" },
      "confirm_cost_id": { "type": "string", "description": "Maliyet onay kimliği. Önce `confirm_cost`'u çağırın." }
    },
    "required": ["project_id", "confirm_cost_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği",
    "name": "geliştirme",
    "confirm_cost_id": "onay-kimliği"
  }
  ```

### `list_branches`
- **Açıklama:** Bir Supabase projesinin tüm geliştirme şubelerini listeler. İşlem tamamlanmasını kontrol etmek için durumu içerir.
  ```json
  {
    "type": "object",
    "properties": {
      "project_id": { "type": "string" }
    },
    "required": ["project_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "project_id": "proje-kimliği"
  }
  ```

### `delete_branch`
- **Açıklama:** Bir geliştirme şubesini siler.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "branch_id": { "type": "string" }
    },
    "required": ["branch_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "branch_id": "şube-kimliği"
  }
  ```

### `merge_branch`
- **Açıklama:** Bir geliştirme şubesinden üretime geçişleri ve uç işlevleri birleştirir.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "branch_id": { "type": "string" }
    },
    "required": ["branch_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "branch_id": "şube-kimliği"
  }
  ```

### `reset_branch`
- **Açıklama:** Bir geliştirme şubesinin geçişlerini sıfırlar. İzlenmeyen değişiklikler kaybolacaktır.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "branch_id": { "type": "string" },
      "migration_version": { "type": "string", "description": "Geliştirme şubenizi belirli bir geçiş sürümüne sıfırlayın." }
    },
    "required": ["branch_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "branch_id": "şube-kimliği",
    "migration_version": "geçiş-sürümü"
  }
  ```

### `rebase_branch`
- **Açıklama:** Bir geliştirme şubesini üretimde yeniden temellendirir. Üretimden daha yeni geçişleri bu şubeye uygular.
  ```json
  {
    "type": "object",
    "properties": {
      "branch_id": { "type": "string" }
    },
    "required": ["branch_id"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "branch_id": "şube-kimliği"
  }
  ```

## Özel Modlar

### Proje Kapsamlı Mod

Varsayılan olarak, MCP sunucusu Supabase hesabınızdaki tüm kuruluşlara ve projelere erişebilir. Sunucuyu belirli bir projeyle sınırlamak istiyorsanız, CLI komutunda `--project-ref` bayrağını ayarlayın:

```bash
bunx --bun @supabase/mcp-server-supabase@latest --access-token=<erişim-jetonunuz> --project-ref=<proje-kimliği>
```

`<proje-kimliği>`'ni projenizin ID'si ile değiştirin. Bunu Supabase [proje ayarlarınızda](https://supabase.com/dashboard/project/_/settings/general) Project ID altında bulabilirsiniz.

Sunucuyu bir projeye kapsamlandırdıktan sonra, `list_projects` ve `list_organizations` gibi hesap düzeyindeki araçlar artık kullanılamaz olacaktır. Sunucu yalnızca belirtilen projeye ve kaynaklarına erişebilecektir.

### Salt Okunur Mod

Supabase MCP sunucusunu salt okunur sorgularla kısıtlamak istiyorsanız, CLI komutunda `--read-only` bayrağını ayarlayın:

```bash
bunx --bun @supabase/mcp-server-supabase@latest --access-token=<erişim-jetonunuz> --read-only
```

Bu, veritabanlarınızda herhangi bir yazma işlemini, SQL'i salt okunur bir Postgres kullanıcısı olarak yürüterek önler. Bu bayrağın yalnızca veritabanı araçları (`execute_sql` ve `apply_migration`) için geçerli olduğunu ve `create_project` veya `create_branch` gibi diğer araçlar için geçerli olmadığını unutmayın.

## Diğer MCP Sunucuları

### `@supabase/mcp-server-postgrest`

PostgREST MCP sunucusu, kendi kullanıcılarınızı REST API aracılığıyla uygulamanıza bağlamanıza olanak tanır. Daha fazla detay için [proje README](https://github.com/supabase-community/supabase-mcp/blob/main/packages/mcp-server-postgrest) sayfasına bakın.

## Kaynaklar

- [Model Context Protocol](https://modelcontextprotocol.io/introduction): MCP ve yetenekleri hakkında daha fazla bilgi edinin
- [Geliştirmeden üretime](https://github.com/supabase-community/supabase-mcp/blob/main/docs/production.md): Değişiklikleri üretim ortamlarına güvenli bir şekilde nasıl yükselteceğinizi öğrenin
- [Supabase Dokümantasyonu](https://supabase.com/docs): Supabase'in resmi dokümantasyonu
- [MCP Paket Sayfası](https://github.com/supabase-community/supabase-mcp): Supabase MCP'nin GitHub deposu

## Geliştirici Notları

Bu repo paket yönetimi için npm ve Node.js'in en son LTS sürümünü kullanır.

Yeni bir geliştirme ortamı kurmak için:

```bash
# Repoyu klonla
git clone https://github.com/supabase-community/supabase-mcp.git

# Bağımlılıkları yükle
npm install --ignore-scripts

# Not: MacOS'un son sürümlerinde, --ignore-scripts bayrağı olmadan libpg-query geçiş bağımlılığını yüklemekte sorun yaşayabilirsiniz
```

## Lisans

Bu proje Apache 2.0 altında lisanslanmıştır. Detaylar için [LICENSE](https://github.com/supabase-community/supabase-mcp/blob/main/LICENSE) dosyasına bakın.
