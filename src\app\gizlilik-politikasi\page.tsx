import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { ChevronLeft, ShieldCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const metadata: Metadata = {
  title: 'Gizlilik Politikası | AlmancaABC',
  description: 'AlmancaABC olarak kişisel verilerinizi nasıl topladığımızı, kullandığımızı ve koruduğumuzu açıklayan gizlilik politikamız.',
};

const GizlilikPolitikasiPage = () => {
  return (
    (<div className="container mx-auto py-8 px-4 md:px-6">
      <div className="mb-8">
        <Button variant="outline" asChild className="text-sm">
          <Link href="/yardim">
            <ChevronLeft className="mr-2 h-4 w-4" />
            <PERSON>ım Merkez<PERSON> Dön
          </Link>
        </Button>
      </div>
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center space-x-3 mb-3">
            <ShieldCheck className="h-8 w-8 text-red-600" />
            <CardTitle className="text-3xl font-bold">Gizlilik Politikası</CardTitle>
          </div>
          <p className="text-muted-foreground">Son Güncelleme: 18 Mayıs 2025</p>
        </CardHeader>
        <CardContent className="prose prose-sm sm:prose lg:prose-lg xl:prose-xl dark:prose-invert max-w-none">
          <h2>1. Giriş</h2>
          <p>
            AlmancaABC olarak, kullanıcılarımızın gizliliğine büyük önem veriyoruz. Bu Gizlilik Politikası,
            Platformumuzu ziyaret ettiğinizde veya hizmetlerimizi kullandığınızda kişisel bilgilerinizi nasıl
            topladığımızı, kullandığımızı, paylaştığımızı ve koruduğumuzu açıklamaktadır.
          </p>
          <h2>2. Topladığımız Bilgiler</h2>
          <p>
            Platformumuza kayıt olurken veya hizmetlerimizi kullanırken adınız, soyadınız, e-posta adresiniz,
            telefon numaranız gibi kişisel bilgilerinizi toplayabiliriz. Ayrıca ödeme işlemleri için gerekli
            finansal bilgileri (doğrudan toplamıyoruz, güvenli ödeme işlemcileri aracılığıyla işlenir) ve
            derslerle ilgili bilgileri (ders kayıtları, ilerleme durumu vb.) toplayabiliriz.
          </p>
          <h2>3. Bilgilerin Kullanımı</h2>
          <p>
            Topladığımız bilgileri hizmetlerimizi sunmak, geliştirmek, kişiselleştirmek, ödemeleri işlemek,
            sizinle iletişim kurmak, yasal yükümlülüklerimizi yerine getirmek ve platformumuzun güvenliğini
            sağlamak amacıyla kullanırız.
          </p>
          <h2>4. Bilgilerin Paylaşımı</h2>
          <p>
            Kişisel bilgilerinizi, yasal zorunluluklar olmadıkça veya açık rızanız bulunmadıkça üçüncü
            taraflarla paylaşmayız. Hizmetlerimizi sunmak için çalıştığımız iş ortaklarımız
            (örneğin ödeme işlemcileri, video konferans sağlayıcıları) ile yalnızca gerekli bilgileri,
            gizlilik anlaşmaları çerçevesinde paylaşabiliriz.
          </p>
          <h2>5. Çerezler (Cookies)</h2>
          <p>
            Platformumuz, kullanıcı deneyimini geliştirmek amacıyla çerezler kullanabilir. Çerez tercihlerinizi
            tarayıcı ayarlarınızdan yönetebilirsiniz.
          </p>
          <h2>6. Veri Güvenliği</h2>
          <p>
            Kişisel bilgilerinizin güvenliğini sağlamak için endüstri standardı güvenlik önlemleri alıyoruz.
            Ancak internet üzerinden yapılan hiçbir veri aktarımının %100 güvenli olmadığını unutmamanız
            önemlidir.
          </p>
          <h2>7. Çocukların Gizliliği</h2>
          <p>
            Platformumuz 18 yaş altı bireylerin yasal vasilerinin onayı ile kullanımına açıktır.
            Ebeveynlerin çocuklarının online aktivitelerini denetlemelerini teşvik ediyoruz.
          </p>
          <h2>8. Politika Değişiklikleri</h2>
          <p>
            Bu Gizlilik Politikası zaman zaman güncellenebilir. Önemli değişiklikler olduğunda sizi
            Platform üzerinden bilgilendireceğiz.
          </p>
          <h2>9. İletişim</h2>
          <p>
            Gizlilik Politikamızla ilgili sorularınız için lütfen <Link href="/iletisim" className="text-blue-600 hover:underline">iletişim sayfamızdan</Link> bize ulaşın.
          </p>
          <p>
            {/* Buraya daha fazla detay ve yasal metin eklenecektir. */}
            Bu belge genel bir taslaktır ve yasal geçerliliği için bir hukuk danışmanına
            başvurulması önerilir.
          </p>
        </CardContent>
      </Card>
    </div>)
  );
};

export default GizlilikPolitikasiPage;