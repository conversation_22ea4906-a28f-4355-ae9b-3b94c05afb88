// src/utils/supabase/server.ts
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';

export const createSupabaseServerClient = () => {
  const cookieStore = cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          // @ts-expect-error - TypeScript tip hatasını şimdilik bastırıyoruz
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          // @ts-expect-error - TypeScript tip hatasını şimdilik bastırıyoruz
          cookieStore.set({ name, value, ...options });
        },
        remove(name: string, options: CookieOptions) {
          // @ts-expect-error - TypeScript tip hatasını şimdilik bastırıyoruz
          cookieStore.set({ name, value: '', ...options });
        },
      },
      // global.fetch yöntemine geri dönüldü
      global: {
        fetch: async (input: RequestInfo | URL, init?: RequestInit | undefined) => {
          const headers = new Headers(init?.headers);
          headers.set('apikey', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!);

          return fetch(input, { ...init, headers });
        },
      },
      // auth bloğu kaldırıldı
      // auth: { ... }
    }
  );
};