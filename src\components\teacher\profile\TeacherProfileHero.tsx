import React, { useState, useCallback } from 'react';
import { MessageCircle, Calendar, Copy, Check } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { VerifiedBadge } from '@/components/VerifiedBadge';
import { ProfilePhotoModal } from '@/components/modals/ProfilePhotoModal';
import { TeacherProfileClientData } from '@/types/teacher';

interface HeaderProps {
  teacher: TeacherProfileClientData;
  onShowCalendar: () => void;
}

export const TeacherProfileHero: React.FC<HeaderProps> = ({ teacher, onShowCalendar }) => {
  const [copied, setCopied] = useState(false);
  const [showPhotoModal, setShowPhotoModal] = useState(false);

  const handleShare = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Kopyalama başarısız:', err);
    }
  }, []);

  const shareToWhatsApp = useCallback(() => {
    const text = `${teacher.name} ile Almanca öğrenin! ${window.location.href}`;
    window.open(`https://wa.me/?text=${encodeURIComponent(text)}`, '_blank');
  }, [teacher.name]);

  const shareToX = useCallback(() => {
    const text = `${teacher.name} ile Almanca öğrenin!`;
    window.open(`https://x.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(window.location.href)}`, '_blank');
  }, [teacher.name]);

  const shareToFacebook = useCallback(() => {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`, '_blank');
  }, []);

  const handlePhotoClick = useCallback(() => {
    setShowPhotoModal(true);
  }, []);

  const handlePhotoModalClose = useCallback(() => {
    setShowPhotoModal(false);
  }, []);

  return (
    <>
      <header className="bg-white/90 backdrop-blur-sm border-b border-white/20 sticky top-0 left-0 right-0 z-50 shadow-sm w-full overflow-x-hidden">
        <div className="w-full lg:max-w-7xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8 box-border relative">
          {/* Share Buttons - Top Right */}
          <div className="absolute top-4 right-4 sm:right-6 lg:right-8 z-20">
            <div className="flex gap-1 sm:gap-2">
              <Button
                onClick={handleShare}
                variant="outline"
                size="sm"
                className="bg-white/80 hover:bg-white border-gray-200 text-gray-700 hover:text-gray-900 h-8 w-8 sm:h-9 sm:w-9 p-0 backdrop-blur-sm transition-all duration-200"
                title="Linki kopyala"
              >
                {copied ? <Check className="w-3 h-3 sm:w-4 sm:h-4 text-green-600" /> : <Copy className="w-3 h-3 sm:w-4 sm:h-4" />}
              </Button>
              <Button
                onClick={shareToWhatsApp}
                variant="outline"
                size="sm"
                className="bg-white/80 hover:bg-green-50 border-gray-200 text-green-600 hover:text-green-700 h-8 w-8 sm:h-9 sm:w-9 p-0 backdrop-blur-sm transition-all duration-200"
                title="WhatsApp'ta paylaş"
              >
                <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.52 3.48A11.94 11.94 0 0012 0C5.373 0 0 5.373 0 12a11.94 11.94 0 001.64 6.04L0 24l5.96-1.56A11.94 11.94 0 0012 24c6.627 0 12-5.373 12-12 0-3.2-1.25-6.2-3.48-8.52zM12 21.5a9.5 9.5 0 01-4.8-1.3l-.34-.2-3.54.93.94-3.45-.22-.35A9.5 9.5 0 1121.5 12 9.5 9.5 0 0112 21.5z"/>
                  <path d="M17.5 14.5c-.3 0-1.7-.8-2-1-.3-.2-.5-.2-.7 0-.2.2-.8 1-.9 1.2-.1.2-.2.3-.5.1-.3-.2-1.3-.5-2.5-1.5-.9-.8-1.5-1.8-1.7-2-.2-.2 0-.3.1-.5.1-.1.2-.3.3-.5.1-.2.1-.3 0-.5-.1-.2-.7-1.7-1-2.3-.3-.6-.6-.5-.7-.5-.2 0-.4 0-.6 0-.2 0-.5.2-.7.5-.2.3-.7.7-.7 1.7 0 1 .7 2 1 2.3.3.3 2 3 4.8 4.3 2.7 1.3 2.7.9 3.2.8.5-.1 1.7-.7 1.9-1.3.2-.6.2-1.1.1-1.3-.1-.2-.3-.3-.6-.4z"/>
                </svg>
              </Button>
              <Button
                onClick={shareToX}
                variant="outline"
                size="sm"
                className="bg-white/80 hover:bg-gray-100 border-gray-200 text-gray-700 hover:text-gray-900 h-8 w-8 sm:h-9 sm:w-9 p-0 backdrop-blur-sm transition-all duration-200"
                title="X'te paylaş"
              >
                <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                </svg>
              </Button>
              <Button
                onClick={shareToFacebook}
                variant="outline"
                size="sm"
                className="bg-white/80 hover:bg-blue-50 border-gray-200 text-blue-700 hover:text-blue-800 h-8 w-8 sm:h-9 sm:w-9 p-0 backdrop-blur-sm transition-all duration-200"
                title="Facebook'ta paylaş"
              >
                <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.675 0h-21.35C.6 0 0 .6 0 1.325v21.351C0 23.4.6 24 1.325 24H12.82v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.894-4.788 4.659-4.788 1.325 0 2.466.099 2.797.143v3.24l-1.918.001c-1.504 0-1.796.715-1.796 1.763v2.312h3.59l-.467 3.622h-3.123V24h6.116C23.4 24 24 23.4 24 22.675V1.325C24 .6 23.4 0 22.675 0z"/>
                </svg>
              </Button>
            </div>
          </div>

          <div className="flex flex-col lg:flex-row items-center lg:items-start gap-6 sm:gap-8 lg:gap-10 w-full">
            {/* Avatar Section with Premium Design */}
            <div className="flex-shrink-0 relative self-center lg:self-start mt-20 sm:mt-16 lg:mt-0">
              {/* Floating Premium Badge */}
              <div className="absolute -top-2 -left-8 sm:-top-3 sm:-left-12 lg:-left-16 z-30">
                <div className="bg-gradient-to-r from-amber-400 to-orange-500 text-white px-2 py-1 sm:px-3 sm:py-1.5 lg:px-4 lg:py-2 rounded-full shadow-lg transform -rotate-12 text-xs sm:text-sm lg:text-base font-bold">
                  ⭐ Premium
                </div>
              </div>

              {/* Animated Gradient Background */}
              <div className="absolute -inset-3 sm:-inset-4 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 rounded-full blur-xl animate-pulse"></div>

              <div className="relative bg-white rounded-full p-1.5 sm:p-2 shadow-xl border border-white/30">
                <button
                  onClick={handlePhotoClick}
                  className="block rounded-full transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-500/30"
                  title="Profil fotoğrafını büyüt"
                >
                  <Avatar className="w-48 h-48 sm:w-56 sm:h-56 lg:w-64 lg:h-64 border-3 sm:border-4 border-gradient-to-br from-blue-500 to-purple-600 cursor-pointer">
                    <AvatarImage src={teacher.avatar} alt={`${teacher.name} - Almanca Öğretmeni`} />
                    <AvatarFallback className="text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-br from-blue-600 to-purple-600 text-white">
                      {teacher.name.split(' ').map((n: string) => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                </button>

                {/* Online Status Badge */}
                <div className="absolute bottom-2 right-2 sm:bottom-3 sm:right-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-white rounded-full shadow-lg flex items-center justify-center border-3 border-gray-300">
                    <div className={`w-4 h-4 sm:w-5 sm:h-5 rounded-full relative ${teacher.isOnline ? 'bg-green-500' : 'bg-gray-400'}`}>
                      {teacher.isOnline && <div className="absolute inset-0 rounded-full bg-green-400 animate-ping"></div>}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Content Section */}
            <div className="flex-1 min-w-0 flex flex-col items-center lg:items-start w-full">
              {/* Title and Badges */}
              <div className="text-center lg:text-left w-full mb-4 sm:mb-6">                <h1 className="text-3xl sm:text-4xl lg:text-5xl font-black text-gray-900 leading-tight mb-3 sm:mb-4 flex items-center justify-center lg:justify-start flex-wrap">
                  <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                    {teacher.name}
                  </span>                  <VerifiedBadge 
                    isVerified={true} 
                    className="ml-2" 
                    size="lg" 
                    variant="simple"
                  />
                </h1>
                <div className="flex flex-wrap justify-center lg:justify-start gap-2 sm:gap-3">
                  <Badge className={`w-fit border-0 shadow-md text-sm transition-colors duration-200 ${teacher.isOnline ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'}`}>
                    <div className={`w-2 h-2 rounded-full mr-1 ${teacher.isOnline ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                    {teacher.isOnline ? 'Çevrimiçi' : 'Çevrimdışı'}
                  </Badge>
                </div>
              </div>              {/* Main Action Buttons - Mobil uyumlu ve ekrana tam sığacak şekilde */}
              <div className="flex flex-col sm:flex-row gap-3 w-full px-4 sm:px-0">
                <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] px-4 sm:px-6 py-3 text-base font-semibold">
                  <MessageCircle className="w-5 h-5 mr-2 flex-shrink-0" />
                  <span className="truncate">Mesaj Gönder</span>
                </Button>
                <Button
                  onClick={onShowCalendar}
                  variant="secondary"
                  className="w-full bg-gray-900 hover:bg-gray-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] px-4 sm:px-6 py-3 text-base font-semibold"
                >
                  <Calendar className="w-5 h-5 mr-2 flex-shrink-0" />
                  <span className="truncate">Randevu Al</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>      {/* Profile Photo Modal */}
      <ProfilePhotoModal 
        isOpen={showPhotoModal}
        onClose={handlePhotoModalClose}
        teacher={teacher}
      />
    </>
  );
};
