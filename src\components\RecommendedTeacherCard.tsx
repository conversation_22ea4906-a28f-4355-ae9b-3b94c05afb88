"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Star, MessageCircle, Globe2 } from "lucide-react" // CheckCircle kaldırıldı
import Link from "next/link"
import { VerifiedBadge } from "@/components/VerifiedBadge" // VerifiedBadge import edildi

// Bu kart için daha basit bir Teacher tipi kullanabiliriz veya TeacherProfile'dan seçebiliriz
// Şimdilik ayrı bir tip tanımlayalım
interface RecommendedTeacher {
  id: string;
  name: string;
  avatar: string;
  rating: number;
  reviewCount: number;
  specialties: string[];
  price: number;
  badges: string[];
  languages: string[];
  verified?: boolean;
}

interface RecommendedTeacherCardProps {
  teacher: RecommendedTeacher;
  index: number;
}

export function RecommendedTeacherCard({ teacher, index }: RecommendedTeacherCardProps) {
  return (
    (<Card className="h-full hover:shadow-lg transition-all duration-300 relative overflow-visible">
      {/* Sıra Rozeti */}
      <span
        className="absolute left-2 top-2 z-[50] rounded-full border-4 border-white bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-4 py-2 text-lg font-extrabold shadow-lg select-none"
        style={{ minWidth: 40, minHeight: 40, display: 'flex', alignItems: 'center', justifyContent: 'center', boxShadow: '0 2px 12px 0 rgba(0,0,0,0.25)' }}
      >
        Sıra: {index + 1}
      </span>
      <CardContent className="p-6 flex flex-col items-center text-center">
        <Avatar className="h-20 w-20 mb-4">
          <AvatarImage src={teacher.avatar} alt={teacher.name} />
          <AvatarFallback>{teacher.name[0]}</AvatarFallback>
        </Avatar>

        <div className="space-y-2 mb-4">
          <div className="flex items-center justify-center gap-1">
            <h3 className="text-lg font-semibold">{teacher.name}</h3>
            {/* VerifiedBadge bileşeni kullanıldı */}
            <VerifiedBadge isVerified={teacher.verified} />
          </div>

          <div className="flex items-center justify-center gap-1">
            <Star className="h-4 w-4 text-yellow-400 fill-current" />
            <span className="font-medium">{teacher.rating.toFixed(1)}</span>
            <span className="text-sm text-muted-foreground">({teacher.reviewCount})</span>
          </div>
        </div>

        <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground mb-4">
          <Globe2 className="h-4 w-4" />
          <span>{teacher.languages.join(", ")}</span>
        </div>

        <div className="flex flex-wrap gap-1 justify-center mb-4">
          {/* İlk 2 rozeti göster */}
          {teacher.badges.slice(0, 2).map((badge) => (
            <Badge key={badge} variant="secondary" className="text-xs">
              {badge}
            </Badge>
          ))}
        </div>

        <div className="text-lg font-bold text-primary mb-4">
          {teacher.price}₺<span className="text-sm font-normal text-muted-foreground">/ders</span>
        </div>

        <div className="flex gap-2 w-full mt-auto">
          <Button variant="outline" size="sm" className="flex-1">
            <MessageCircle className="h-4 w-4 mr-2" />
            Mesaj
          </Button>
          <Button asChild size="sm" className="w-full flex-1">
            <Link href={`/teachers/${teacher.id}`}>
              Profil
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>)
  );
}
