"use client";

import React, { useState } from 'react';
import { Calendar, Clock, CheckCircle, AlertCircle, CalendarDays } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TeacherProfileData, CoursePackage } from '@/components/teacher/TeacherProfileClient'; // Yolu TeacherProfileClient'ın bulunduğu doğru dizine güncelledim.

interface ScheduleTabProps { // ScheduleTabProps -> ScheduleTabProps
  teacher: TeacherProfileData;
  onOpenCalendarModal: (pkg: CoursePackage) => void;
}

export const ScheduleTab: React.FC<ScheduleTabProps> = ({ teacher: _teacher, onOpenCalendarModal }) => { // ScheduleTab -> ScheduleTab
  const [selectedDay, setSelectedDay] = useState<string | null>(null);

  // Örnek müsaitlik verileri (gerçek uygulamada API'den gelecek)
  const weekDays = [
    { id: 'monday', name: '<PERSON><PERSON><PERSON>', date: '25 Mar', available: true },
    { id: 'tuesday', name: 'Salı', date: '26 Mar', available: true },
    { id: 'wednesday', name: 'Çarşamba', date: '27 Mar', available: false },
    { id: 'thursday', name: 'Perşembe', date: '28 Mar', available: true },
    { id: 'friday', name: 'Cuma', date: '29 Mar', available: true },
    { id: 'saturday', name: 'Cumartesi', date: '30 Mar', available: true },
    { id: 'sunday', name: 'Pazar', date: '31 Mar', available: false }
  ];

  const timeSlots = [
    { time: '09:00', available: true, type: 'Birebir Ders' },
    { time: '10:30', available: false, type: 'Grup Dersi' },
    { time: '12:00', available: true, type: 'Birebir Ders' },
    { time: '14:00', available: true, type: 'Birebir Ders' },
    { time: '15:30', available: true, type: 'Grup Dersi' },
    { time: '17:00', available: false, type: 'Birebir Ders' },
    { time: '19:00', available: true, type: 'Birebir Ders' },
    { time: '20:30', available: true, type: 'Birebir Ders' }
  ];

  const handleTimeSlotClick = (time: string, type: string) => {
    const mockPackage: CoursePackage = {
      id: 'schedule-booking',
      name: `${type} - ${time}`,
      type: type,
      price: type === 'Birebir Ders' ? 150 : 75,
      sessions: 1,
      durationMinutes: 60
    };
    onOpenCalendarModal(mockPackage);
  };

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-6 bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 rounded-3xl p-8 md:p-12 border border-emerald-200">
        <div className="inline-flex items-center gap-2 bg-emerald-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
          <CalendarDays className="w-4 h-4" />
          Ders Programı
        </div>
        <h2 className="text-4xl md:text-5xl font-black text-gray-900 leading-tight">
          <span className="bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 bg-clip-text text-transparent">
            Müsait Zamanlarım
          </span>
        </h2>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          Size uygun bir zaman dilimi seçin ve hemen randevu alın!
        </p>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Sol Taraf - Haftalık Takvim */}
        <div className="lg:col-span-2">
          <Card className="bg-white border border-gray-200 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-emerald-500 to-green-500 rounded-lg">
                  <Calendar className="w-6 h-6 text-white" />
                </div>
                <span className="text-2xl font-bold text-gray-900">Bu Hafta</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Günler */}
              <div className="grid grid-cols-7 gap-2 mb-6">
                {weekDays.map((day) => (
                  <button
                    key={day.id}
                    onClick={() => setSelectedDay(day.id)}
                    className={`p-3 rounded-xl text-center transition-all duration-300 ${
                      selectedDay === day.id
                        ? 'bg-gradient-to-br from-emerald-500 to-green-500 text-white shadow-lg'
                        : day.available
                        ? 'bg-emerald-50 hover:bg-emerald-100 text-emerald-700 border border-emerald-200'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    }`}
                    disabled={!day.available}
                  >
                    <div className="text-xs font-medium">{day.name}</div>
                    <div className="text-sm font-bold">{day.date}</div>
                    {day.available ? (
                      <CheckCircle className="w-4 h-4 mx-auto mt-1" />
                    ) : (
                      <AlertCircle className="w-4 h-4 mx-auto mt-1" />
                    )}
                  </button>
                ))}
              </div>

              {/* Saat Dilimleri */}
              {selectedDay && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <Clock className="w-5 h-5 text-emerald-500" />
                    Müsait Saatler - {weekDays.find(d => d.id === selectedDay)?.name}
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {timeSlots.map((slot) => (
                      <button
                        key={slot.time}
                        onClick={() => slot.available && handleTimeSlotClick(slot.time, slot.type)}
                        className={`p-3 rounded-lg text-center transition-all duration-300 ${
                          slot.available
                            ? 'bg-white border-2 border-emerald-200 hover:border-emerald-400 hover:shadow-md text-gray-900'
                            : 'bg-gray-100 border border-gray-200 text-gray-400 cursor-not-allowed'
                        }`}
                        disabled={!slot.available}
                      >
                        <div className="font-bold text-lg">{slot.time}</div>
                        <div className="text-xs text-gray-600">{slot.type}</div>
                        {slot.available ? (
                          <Badge className="mt-1 bg-emerald-100 text-emerald-700 text-xs">
                            Müsait
                          </Badge>
                        ) : (
                          <Badge className="mt-1 bg-gray-100 text-gray-500 text-xs">
                            Dolu
                          </Badge>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {!selectedDay && (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>Müsait saatleri görmek için bir gün seçin</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sağ Taraf - Bilgi ve Hızlı Randevu */}
        <div className="space-y-6">
          {/* Müsaitlik Bilgisi */}
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg">
                  <Clock className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-gray-900">Müsaitlik Durumu</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3 p-3 bg-white/70 rounded-lg">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-gray-700">Şu anda çevrimiçi</span>
              </div>
              <div className="text-sm text-gray-600 space-y-2">
                <p>• Genellikle 2 saat içinde yanıt verir</p>
                <p>• Hafta içi 09:00-21:00 arası müsait</p>
                <p>• Hafta sonu 10:00-18:00 arası müsait</p>
              </div>
            </CardContent>
          </Card>

          {/* Hızlı Randevu */}
          <Card className="bg-gradient-to-br from-emerald-50 to-green-50 border border-emerald-200 shadow-lg">
            <CardContent className="p-6">
              <div className="text-center space-y-4">
                <h3 className="text-xl font-bold text-gray-900">Hızlı Randevu</h3>
                <p className="text-gray-600 text-sm">
                  Hemen bir deneme dersi almak ister misiniz?
                </p>
                <Button 
                  className="w-full bg-gradient-to-r from-emerald-500 to-green-500 hover:from-emerald-600 hover:to-green-600 text-white py-3"
                  onClick={() => handleTimeSlotClick('En yakın müsait saat', 'Deneme Dersi')}
                >
                  <Calendar className="w-5 h-5 mr-2" />
                  Deneme Dersi Al
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Ders Türleri */}
          <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-200 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg font-bold text-gray-900">Ders Türleri</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                <span className="text-gray-700">Birebir Ders</span>
                <Badge className="bg-blue-100 text-blue-700">150₺/saat</Badge>
              </div>
              <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                <span className="text-gray-700">Grup Dersi</span>
                <Badge className="bg-green-100 text-green-700">75₺/saat</Badge>
              </div>
              <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                <span className="text-gray-700">Deneme Dersi</span>
                <Badge className="bg-purple-100 text-purple-700">Ücretsiz</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};