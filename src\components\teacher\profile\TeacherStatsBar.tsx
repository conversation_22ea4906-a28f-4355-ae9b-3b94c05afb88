import React from 'react';
import { Trophy, Star, Users, Clock } from 'lucide-react';
import { Teacher } from '@/types/teacher';

interface TeacherStatsBarProps {
  teacher: Teacher & {
    totalLessons: number;
    reviewCount: number;
  };
}

export const TeacherStatsBar: React.FC<TeacherStatsBarProps> = ({ teacher }) => {
  const achievements = [
    { 
      icon: Trophy, 
      number: teacher.totalLessons, 
      label: "Toplam Ders", 
      color: "from-amber-500 to-orange-500",
      bgColor: "bg-amber-50",
      borderColor: "border-amber-200"
    },
    { 
      icon: Star, 
      number: teacher.average_rating?.toFixed(1) || 'N/A', 
      label: "Ortalama Puan", 
      color: "from-blue-500 to-indigo-500",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200"
    },
    { 
      icon: Users, 
      number: `${teacher.reviewCount}+`, 
      label: "<PERSON><PERSON><PERSON>", 
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-green-50",
      borderColor: "border-green-200"
    },
    { 
      icon: Clock, 
      number: `${teacher.experienceYears || 0}+`, 
      label: "<PERSON><PERSON><PERSON>", 
      color: "from-purple-500 to-pink-500",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200"
    }
  ];

  return (
    <div className="bg-white border-b border-gray-200 w-full overflow-x-hidden">
      <div className="w-full max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-2 lg:py-3">
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 w-full">
          {achievements.map((achievement, index) => {
            const Icon = achievement.icon;
            return (
              <div
                key={index}
                className={`${achievement.bgColor} ${achievement.borderColor} border rounded-lg p-2 sm:p-3 text-center hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1`}
              >
                <div className="flex items-center justify-center mb-1 sm:mb-1.5">
                  <div className={`w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br ${achievement.color} rounded-lg flex items-center justify-center shadow-md`}>
                    <Icon className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                  </div>
                </div>
                <div className="text-sm sm:text-lg font-bold text-gray-900 mb-0.5">{achievement.number}</div>
                <div className="text-xs sm:text-sm text-gray-600 font-medium leading-tight">{achievement.label}</div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
