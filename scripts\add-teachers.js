// scripts/add-teachers.js
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Rastgele 8 basamaklı sayısal ID üreten fonksiyon
function generateRandomId() {
  return Math.floor(10000000 + Math.random() * 90000000).toString();
}

// Öğretmen verileri
const teachers = [
  {
    firstName: 'Ayşe',
    lastName: 'Yılma<PERSON>',
    title: '<PERSON><PERSON><PERSON><PERSON> Almanca Öğretmeni',
    bio: '10 yıllık deneyim<PERSON>, sabırlı ve öğrenci odaklı bir öğretmenim. Her seviyeden öğrenciye özel ders planları hazırlıyorum.',
    specializations: ['İş Almancası', 'Günlük Konuşma'],
    levels: ['A1', 'A2', 'B1', 'B2'],
    hourly_rate: 150,
    profile_image_url: 'https://randomuser.me/api/portraits/women/1.jpg',
    country: 'T<PERSON>rk<PERSON><PERSON>',
    city: 'İstanbul',
    languages: ['<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>nc<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>'],
    is_approved: true,
    is_verified: true,
    experience_years: 10,
    average_rating: 4.9,
    completedLessons: 127,

  },
  {
    firstName: 'Ahmet',
    lastName: 'Kaya',
    title: 'Sınav Odaklı Almanca Uzmanı',
    bio: "Goethe Enstitüsü'nde 5 yıl çalıştım. TestDaF ve telc sınavlarında uzmanlığım var. Hedef odaklı derslerle başarıyı garantileyin.",
    specializations: ['Sınav Hazırlık (TestDaF, telc)', 'Akademik Almanca'],
    levels: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],
    hourly_rate: 180,
    profile_image_url: 'https://randomuser.me/api/portraits/men/2.jpg',
    country: 'Türkiye',
    city: 'Ankara',
    languages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: true,
    experience_years: 7,
    average_rating: 4.8,
    completedLessons: 98,

  },
  {
    firstName: 'Zeynep',
    lastName: 'Demir',
    title: 'Anadil Seviyesinde Konuşma Pratiği',
    bio: "Almanya'da doğdum ve büyüdüm. Doğal bir Almanca konuşma pratiği sunuyorum. Günlük hayatta kullanılan ifadeler ve kültürel nüanslar üzerine odaklanıyorum.",
    specializations: ['Konuşma Pratiği', 'Günlük Almanca', 'Alman Kültürü'],
    levels: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],
    hourly_rate: 140,
    profile_image_url: 'https://randomuser.me/api/portraits/women/3.jpg',
    country: 'Almanya',
    city: 'Berlin',
    languages: ['Almanca', 'Türkçe'],
    is_approved: true,
    is_verified: false,
    experience_years: 4,
    average_rating: 5.0,
    completedLessons: 56,

  },
  {
    firstName: 'Can',
    lastName: 'Öztürk',
    title: 'Çocuklar İçin Eğlenceli Almanca',
    bio: 'Çocuklara ve gençlere Almanca öğretme konusunda 8 yıllık deneyime sahibim. Oyunlar ve interaktif aktivitelerle öğrenmeyi keyifli hale getiriyorum.',
    specializations: ['Çocuklar için Almanca', 'Gençler için Almanca'],
    levels: ['A1', 'A2', 'B1', 'B2', 'C1'],
    hourly_rate: 160,
    profile_image_url: 'https://randomuser.me/api/portraits/men/4.jpg',
    country: 'Türkiye',
    city: 'İzmir',
    languages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: true,
    experience_years: 8,
    average_rating: 4.7,
    completedLessons: 84,

  },
  {
    firstName: 'Fatma',
    lastName: 'Şahin',
    title: 'Akademik Almanca ve YDS Uzmanı',
    bio: 'Akademik Almanca ve YDS hazırlığı konusunda uzmanım. Üniversite öğrencileri ve akademisyenlere yönelik özel programlar sunuyorum.',
    specializations: ['Akademik Almanca', 'YDS Hazırlık', 'İleri Seviye Gramer'],
    levels: ['A1', 'A2', 'B1', 'B2', 'C1'],
    hourly_rate: 170,
    profile_image_url: 'https://randomuser.me/api/portraits/women/5.jpg',
    country: 'Türkiye',
    city: 'İstanbul',
    languages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: true,
    experience_years: 9,
    average_rating: 4.9,
    completedLessons: 110,

  },
  {
    firstName: 'Ali',
    lastName: 'Vural',
    title: 'Tıbbi ve Mesleki Almanca Eğitmeni',
    bio: 'Sağlık sektöründe çalışanlar için özel Almanca dersleri veriyorum. Tıbbi terminoloji ve mesleki iletişim becerileri üzerine yoğunlaşıyorum.',
    specializations: ['Tıbbi Almanca', 'Mesleki Almanca (Sağlık)'],
    levels: ['B1', 'B2', 'C1', 'C2'],
    hourly_rate: 190,
    profile_image_url: 'https://randomuser.me/api/portraits/men/6.jpg',
    country: 'Almanya',
    city: 'Münih',
    languages: ['Türkçe', 'Almanca', 'İngilizce'],
    is_approved: true,
    is_verified: true,
    experience_years: 12,
    average_rating: 4.6,
    completedLessons: 75,

  },
  {
    firstName: 'Elif',
    lastName: 'Aksoy',
    title: 'Çeviri Teknikleri ve İleri Dilbilgisi',
    bio: 'Almanca-Türkçe çeviri ve dilbilgisi konularında yardımcı olabilirim. İleri seviye öğrenciler ve çevirmen adayları için ideal.',
    specializations: ['Çeviri Teknikleri', 'İleri Gramer', 'Akademik Yazım'],
    levels: ['B2', 'C1', 'C2'],
    hourly_rate: 165,
    profile_image_url: 'https://randomuser.me/api/portraits/women/7.jpg',
    country: 'Türkiye',
    city: 'Bursa',
    languages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: true,
    experience_years: 7,
    average_rating: 4.8,
    completedLessons: 92,

  },
  {
    firstName: 'Mustafa',
    lastName: 'Yıldız',
    title: 'Günlük Konuşma ve Kültür Rehberi',
    bio: 'Almanya kültürü ve günlük yaşam hakkında konuşarak pratik yapalım. Rahat bir ortamda Almanca konuşma becerilerinizi geliştirin.',
    specializations: ['Günlük Konuşma', 'Alman Kültürü', 'Seyahat Almancası'],
    levels: ['A1', 'A2', 'B1', 'B2'],
    hourly_rate: 155,
    profile_image_url: 'https://randomuser.me/api/portraits/men/8.jpg',
    country: 'Almanya',
    city: 'Hamburg',
    languages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: false,
    experience_years: 6,
    average_rating: 4.7,
    completedLessons: 88,

  },
];

// Öğretmenleri veritabanına ekleyen fonksiyon
async function addTeachers() {
  console.log('Öğretmenler ekleniyor...');
  
  for (const teacher of teachers) {
    const teacherId = generateRandomId();
    try {
      // Öğretmeni ekle
      const createdTeacher = await prisma.teacher.create({
        data: {
          id: teacherId,
          ...teacher,
        },
      });
      console.log(`Öğretmen eklendi: ${createdTeacher.firstName} ${createdTeacher.lastName} (ID: ${createdTeacher.id})`);
    } catch (error) {
      console.error(`Öğretmen eklenirken hata oluştu: ${error.message}`);
    }
  }
  
  console.log('İşlem tamamlandı!');
}

// Fonksiyonu çalıştır ve bağlantıyı kapat
addTeachers()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (error) => {
    console.error('Hata:', error);
    await prisma.$disconnect();
    process.exit(1);
  });
