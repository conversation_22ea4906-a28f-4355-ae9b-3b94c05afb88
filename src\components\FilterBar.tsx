// src/components/FilterBar.tsx
"use client"

import React, { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { X, Search, GraduationCap, Clock, BookOpen, Globe, Euro, Languages, UserCheck, ArrowUpDown, LayoutGrid, List } from 'lucide-react' // LayoutGrid ve List eklendi
import { cn } from '@/lib/utils'
import { ScrollArea } from '@/components/ui/scroll-area'
import { LevelFilter } from './LevelFilter'
import { PriceFilter } from './PriceFilter'
import { SpecialtyFilter } from './SpecialtyFilter'
import { CountryFilter } from './CountryFilter'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import type { TeacherFilters } from '@/types/filters'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select" // Select component'i eklendi

interface FilterBarProps {
  filters: TeacherFilters;
  setFilters: React.Dispatch<React.SetStateAction<TeacherFilters>>;
  totalTeachers: number;
  viewMode: 'grid' | 'list';
  setViewMode: React.Dispatch<React.SetStateAction<'grid' | 'list'>>;
}

// Sabit dil seçenekleri (gerçek uygulamada API'den veya sabitlerden gelebilir)
const allLanguages = ["İngilizce", "İspanyolca", "Fransızca", "Türkçe", "İtalyanca", "Rusça"];
const sortOptions = [
  { value: 'relevance', label: 'İlgiye Göre (Önerilen)' },
  { value: 'price_asc', label: 'Fiyat (Artan)' },
  { value: 'price_desc', label: 'Fiyat (Azalan)' },
  { value: 'rating_desc', label: 'Puan (Azalan)' },
  { value: 'newest', label: 'En Yeni' },
];


const FilterMenu = ({ title, isOpen, onClose, children, widthClass = "max-w-md" }: { title: string, isOpen: boolean, onClose: () => void, children: React.ReactNode, widthClass?: string }) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 z-[999] flex items-center justify-center bg-gradient-to-br from-black/30 via-zinc-900/40 to-black/20 backdrop-blur-[1px] animate-fade-in" onClick={onClose}> {/* z-index artırıldı */}
      <div
        className={cn(
          "bg-white/90 dark:bg-zinc-900/90 rounded-2xl shadow-2xl p-6 sm:p-8 w-full border border-zinc-200 dark:border-zinc-800 drop-shadow-xl animate-modal-pop",
          widthClass
        )}
        onClick={e => e.stopPropagation()}
        style={{ boxShadow: '0 8px 40px 0 rgba(0,0,0,0.13)' }}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-bold tracking-tight text-zinc-800 dark:text-zinc-100">{title}</h3>
          <Button variant="ghost" size="sm" onClick={onClose} className="rounded-full hover:bg-zinc-100 dark:hover:bg-zinc-800">
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="pb-2">{children}</div>
      </div>
    </div>
  );
};

export function FilterBar({ filters, setFilters, totalTeachers, viewMode, setViewMode }: FilterBarProps) {
  const [searchQuery, setSearchQuery] = useState(filters.search || "")
  const [activeMenu, setActiveMenu] = useState<string | null>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  const handleFilterChange = (filterType: keyof TeacherFilters, value: string | [number, number] | boolean | null | TeacherFilters['sortBy']) => {
    setFilters(prev => {
      const newFilters = { ...prev } as TeacherFilters; // Daha kesin bir tip ataması

      if (filterType === 'price' && Array.isArray(value) && value.length === 2 && typeof value[0] === 'number' && typeof value[1] === 'number') {
        newFilters.price = value as [number, number];
      } else if (filterType === 'isNativeSpeaker') {
        newFilters.isNativeSpeaker = value as boolean | null;
      } else if (filterType === 'sortBy' && (typeof value === 'string' || typeof value === 'undefined')) {
        // sortBy değeri undefined olabilir, bu durumda 'relevance' gibi bir varsayılan atanabilir veya olduğu gibi bırakılabilir.
        // Şimdilik gelen değeri doğrudan atıyoruz, TeacherFilters tipi zaten optional olduğunu belirtiyor.
        newFilters.sortBy = value as TeacherFilters['sortBy'];
      } else if (
        (filterType === 'level' ||
          filterType === 'availability' ||
          filterType === 'specialties' ||
          filterType === 'country' ||
          filterType === 'spokenLanguages') &&
        typeof value === 'string'
      ) {
        // Bu alanlar string[] tipinde olmalı
        const currentArray = (newFilters[filterType] as string[] | undefined) || [];
        if (currentArray.includes(value)) {
          (newFilters[filterType] as string[]) = currentArray.filter((item: string) => item !== value);
        } else {
          (newFilters[filterType] as string[]) = [...currentArray, value];
        }
      } else if (filterType === 'search' && typeof value === 'string') {
        newFilters.search = value;
      }
      return newFilters;
    });
  }

  const clearAllFilters = () => {
    setFilters({
      level: [],
      price: [0, 500],
      availability: [],
      specialties: [],
      country: [],
      search: '',
      spokenLanguages: [],
      isNativeSpeaker: null,
      sortBy: 'relevance'
    })
    setSearchQuery("")
  }

  useEffect(() => {
    const searchTerm = searchQuery.toLowerCase().trim()
    // Arama sorgusu değiştiğinde anlık filtreleme yerine,
    // kullanıcı Enter'a bastığında veya bir süre yazmayı bıraktığında filtreleme yapmak daha iyi olabilir.
    // Şimdilik basit tutuyoruz.
    setFilters(prevFilters => ({
      ...prevFilters,
      search: searchTerm
    }))
  }, [searchQuery, setFilters])

  const activeFiltersCount = Object.entries(filters).reduce((count, [key, value]) => {
    if (key === 'search' && value === '') return count;
    if (key === 'search' && value !== '') { count++; return count;}
    if (key === 'sortBy' && value === 'relevance') return count;
    if (key === 'sortBy' && value !== 'relevance') { count++; return count;}
    if (key === 'isNativeSpeaker' && value !== null) { count++; return count;}
    if (Array.isArray(value) && key !== 'price' && value.length > 0) { count++; return count;}
    if (key === 'price' && (value[0] > 0 || value[1] < 500)) { count++; return count;}
    return count;
  }, 0);

  const getFilterButtonVariant = (menuName: string) => {
    if (activeMenu === menuName) return 'default';
    if (menuName === 'level' && filters.level.length > 0) return 'secondary';
    if (menuName === 'price' && (filters.price[0] > 0 || filters.price[1] < 500)) return 'secondary';
    if (menuName === 'availability' && filters.availability.length > 0) return 'secondary';
    if (menuName === 'specialties' && filters.specialties.length > 0) return 'secondary';
    if (menuName === 'country' && filters.country.length > 0) return 'secondary';
    if (menuName === 'spokenLanguages' && filters.spokenLanguages && filters.spokenLanguages.length > 0) return 'secondary';
    if (menuName === 'isNativeSpeaker' && filters.isNativeSpeaker !== null) return 'secondary';
    return 'outline';
  };


  return (
    <Card className="mb-6 sm:mb-8 shadow-sm border-zinc-200 dark:border-zinc-800 bg-white/70 dark:bg-zinc-900/70 backdrop-blur-sm">
      <CardContent className="p-3 sm:p-4">
        <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4 mb-3 sm:mb-4">
          <div className="relative flex-grow w-full sm:w-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Öğretmen adı, uzmanlık alanı veya anahtar kelime ile ara..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 rounded-lg text-sm sm:text-base"
              type="search"
            />
          </div>
          {activeFiltersCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="whitespace-nowrap text-sky-600 dark:text-sky-400 hover:text-sky-700 dark:hover:text-sky-300"
            >
              <X className="w-3.5 h-3.5 mr-1.5" />
              Filtreleri Temizle {activeFiltersCount > 0 ? `(${activeFiltersCount})` : ''}
            </Button>
          )}
        </div>
        <div className="flex flex-col lg:flex-row items-center gap-3"> {/* sm:flex-row yerine lg:flex-row yapıldı, mobil için alt alta */}
          {/* Görünüm Değiştirme Butonları */}
          <div className="flex items-center gap-1 flex-shrink-0 mb-2 lg:mb-0 self-start lg:self-center"> {/* self-start ve lg:self-center eklendi */}
            <Button
              variant={viewMode === 'grid' ? 'secondary' : 'ghost'}
              size="icon"
              onClick={() => setViewMode('grid')}
              aria-label="Izgara Görünümü"
              className="h-9 w-9 rounded-md data-[state=active]:bg-primary/10 dark:data-[state=active]:bg-primary/20 data-[state=active]:text-primary"
              data-state={viewMode === 'grid' ? 'active' : 'inactive'}
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'secondary' : 'ghost'}
              size="icon"
              onClick={() => setViewMode('list')}
              aria-label="Liste Görünümü"
              className="h-9 w-9 rounded-md data-[state=active]:bg-primary/10 dark:data-[state=active]:bg-primary/20 data-[state=active]:text-primary"
              data-state={viewMode === 'list' ? 'active' : 'inactive'}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
          <div
            ref={scrollContainerRef}
            className="flex gap-2 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-zinc-300 dark:scrollbar-thumb-zinc-700 scrollbar-track-transparent w-full lg:flex-grow" // sm:pb-0 kaldırıldı, w-full ve lg:flex-grow eklendi
          >
            {/* Mevcut Filtre Butonları */}
            <Button
              variant={getFilterButtonVariant('level')}
              size="sm"
              className="flex items-center whitespace-nowrap rounded-full"
              onClick={() => setActiveMenu(activeMenu === 'level' ? null : 'level')}
            >
              <GraduationCap className="w-4 h-4 mr-1.5" />
              Seviye
              {filters.level.length > 0 && (
                <Badge variant="default" className="ml-1.5 px-1.5 py-0.5 text-xs rounded-full bg-sky-500 text-white">
                  {filters.level.length}
                </Badge>
              )}
            </Button>
            <Button
              variant={getFilterButtonVariant('price')}
              size="sm"
              className="flex items-center whitespace-nowrap rounded-full"
              onClick={() => setActiveMenu(activeMenu === 'price' ? null : 'price')}
            >
              <Euro className="w-4 h-4 mr-1.5" />
              Fiyat
              {(filters.price[0] > 0 || filters.price[1] < 500) && (
                <Badge variant="default" className="ml-1.5 px-1.5 py-0.5 text-xs rounded-full bg-sky-500 text-white">✓</Badge>
              )}
            </Button>
            <Button
              variant={getFilterButtonVariant('availability')}
              size="sm"
              className="flex items-center whitespace-nowrap rounded-full"
              onClick={() => setActiveMenu(activeMenu === 'availability' ? null : 'availability')}
            >
              <Clock className="w-4 h-4 mr-1.5" />
              Müsaitlik
              {filters.availability.length > 0 && (
                <Badge variant="default" className="ml-1.5 px-1.5 py-0.5 text-xs rounded-full bg-sky-500 text-white">
                  {filters.availability.length}
                </Badge>
              )}
            </Button>
            <Button
              variant={getFilterButtonVariant('specialties')}
              size="sm"
              className="flex items-center whitespace-nowrap rounded-full"
              onClick={() => setActiveMenu(activeMenu === 'specialties' ? null : 'specialties')}
            >
              <BookOpen className="w-4 h-4 mr-1.5" />
              Uzmanlık
              {filters.specialties.length > 0 && (
                <Badge variant="default" className="ml-1.5 px-1.5 py-0.5 text-xs rounded-full bg-sky-500 text-white">
                  {filters.specialties.length}
                </Badge>
              )}
            </Button>
            <Button
              variant={getFilterButtonVariant('country')}
              size="sm"
              className="flex items-center whitespace-nowrap rounded-full"
              onClick={() => setActiveMenu(activeMenu === 'country' ? null : 'country')}
            >
              <Globe className="w-4 h-4 mr-1.5" />
              Ülke
              {filters.country.length > 0 && (
                <Badge variant="default" className="ml-1.5 px-1.5 py-0.5 text-xs rounded-full bg-sky-500 text-white">
                  {filters.country.length}
                </Badge>
              )}
            </Button>

            {/* Yeni Filtre Butonları */}
            <Button
              variant={getFilterButtonVariant('spokenLanguages')}
              size="sm"
              className="flex items-center whitespace-nowrap rounded-full"
              onClick={() => setActiveMenu(activeMenu === 'spokenLanguages' ? null : 'spokenLanguages')}
            >
              <Languages className="w-4 h-4 mr-1.5" />
              Konuştuğu Diller
              {filters.spokenLanguages && filters.spokenLanguages.length > 0 && (
                <Badge variant="default" className="ml-1.5 px-1.5 py-0.5 text-xs rounded-full bg-sky-500 text-white">
                  {filters.spokenLanguages.length}
                </Badge>
              )}
            </Button>
            <Button
              variant={getFilterButtonVariant('isNativeSpeaker')}
              size="sm"
              className="flex items-center whitespace-nowrap rounded-full"
              onClick={() => setActiveMenu(activeMenu === 'isNativeSpeaker' ? null : 'isNativeSpeaker')}
            >
              <UserCheck className="w-4 h-4 mr-1.5" />
              Ana Dili
              {filters.isNativeSpeaker !== null && (
                <Badge variant="default" className="ml-1.5 px-1.5 py-0.5 text-xs rounded-full bg-sky-500 text-white">
                  {filters.isNativeSpeaker ? 'Evet' : 'Hayır'}
                </Badge>
              )}
            </Button>
          </div>
          {/* Sıralama Select Component'i buraya, filtre butonlarının dışına taşındı */}
          <div className="mt-3 lg:mt-0 lg:ml-auto flex-shrink-0 w-full lg:w-auto self-start lg:self-center"> {/* sm yerine lg kullanıldı, self-start ve lg:self-center eklendi */}
             <Select
              value={filters.sortBy || 'relevance'}
              onValueChange={(value) => {
                if (value === undefined || typeof value === 'string') {
                   handleFilterChange('sortBy', value as TeacherFilters['sortBy']);
                }
              }}
            >
              <SelectTrigger className="h-9 rounded-md text-xs sm:text-sm w-full sm:w-auto min-w-[180px] bg-white dark:bg-zinc-800 border-zinc-300 dark:border-zinc-700 focus:ring-sky-500">
                <ArrowUpDown className="w-3.5 h-3.5 mr-1.5 text-muted-foreground" />
                <SelectValue placeholder="Sırala" />
              </SelectTrigger>
              <SelectContent className="bg-white dark:bg-zinc-800 border-zinc-300 dark:border-zinc-700">
                {sortOptions.map(option => (
                  <SelectItem key={option.value} value={option.value} className="text-xs sm:text-sm hover:bg-zinc-100 dark:hover:bg-zinc-700">
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Mevcut Filtre Menüleri */}
        <FilterMenu title="Seviye" isOpen={activeMenu === 'level'} onClose={() => setActiveMenu(null)}>
          <LevelFilter selected={filters.level} onChange={levels => setFilters(prev => ({ ...prev, level: levels }))} onClose={() => setActiveMenu(null)} />
        </FilterMenu>
        <FilterMenu title="Fiyat Aralığı" isOpen={activeMenu === 'price'} onClose={() => setActiveMenu(null)}>
          <PriceFilter value={filters.price} onChange={value => setFilters(prev => ({ ...prev, price: value }))} onClose={() => setActiveMenu(null)} />
        </FilterMenu>
        <FilterMenu title="Müsaitlik" isOpen={activeMenu === 'availability'} onClose={() => setActiveMenu(null)}>
          <ScrollArea className="h-[200px] sm:h-[250px] pr-3">
            <div className="grid grid-cols-2 gap-x-4 gap-y-2">
              {['Sabah (08:00-12:00)', 'Öğleden Sonra (12:00-17:00)', 'Akşam (17:00-21:00)', 'Gece (21:00-00:00)', 'Hafta İçi', 'Hafta Sonu'].map(option => (
                <div key={option} className="flex items-center space-x-2">
                  <Checkbox id={`availability-${option}`} checked={filters.availability.includes(option)} onCheckedChange={() => handleFilterChange('availability', option)} />
                  <Label htmlFor={`availability-${option}`} className="text-xs sm:text-sm font-normal">{option}</Label>
                </div>
              ))}
            </div>
          </ScrollArea>
          <div className="flex justify-end gap-2 mt-4 pt-3 border-t border-zinc-200 dark:border-zinc-700">
            <Button variant="ghost" size="sm" onClick={() => setFilters(prev => ({ ...prev, availability: [] }))}>Temizle</Button>
            <Button size="sm" onClick={() => setActiveMenu(null)}>Uygula</Button>
          </div>
        </FilterMenu>
        <FilterMenu title="Uzmanlık Alanları" isOpen={activeMenu === 'specialties'} onClose={() => setActiveMenu(null)} widthClass="max-w-lg">
          <SpecialtyFilter selected={filters.specialties} onChange={specialties => setFilters(prev => ({ ...prev, specialties }))} onClose={() => setActiveMenu(null)} />
        </FilterMenu>
        <FilterMenu title="Öğretmenin Ülkesi" isOpen={activeMenu === 'country'} onClose={() => setActiveMenu(null)} widthClass="max-w-lg">
          <CountryFilter selected={filters.country} onChange={countries => setFilters(prev => ({ ...prev, country: countries }))} onClose={() => setActiveMenu(null)} />
        </FilterMenu>

        {/* Yeni Filtre Menüleri */}
        <FilterMenu title="Konuştuğu Diller" isOpen={activeMenu === 'spokenLanguages'} onClose={() => setActiveMenu(null)}>
          <ScrollArea className="h-[200px] sm:h-[250px] pr-3">
            <div className="grid grid-cols-2 gap-x-4 gap-y-2">
              {allLanguages.map(lang => (
                <div key={lang} className="flex items-center space-x-2">
                  <Checkbox
                    id={`lang-${lang}`}
                    checked={filters.spokenLanguages?.includes(lang)}
                    onCheckedChange={() => handleFilterChange('spokenLanguages', lang)}
                  />
                  <Label htmlFor={`lang-${lang}`} className="text-xs sm:text-sm font-normal">{lang}</Label>
                </div>
              ))}
            </div>
          </ScrollArea>
           <div className="flex justify-end gap-2 mt-4 pt-3 border-t border-zinc-200 dark:border-zinc-700">
            <Button variant="ghost" size="sm" onClick={() => setFilters(prev => ({ ...prev, spokenLanguages: [] }))}>Temizle</Button>
            <Button size="sm" onClick={() => setActiveMenu(null)}>Uygula</Button>
          </div>
        </FilterMenu>

        <FilterMenu title="Ana Dili Durumu" isOpen={activeMenu === 'isNativeSpeaker'} onClose={() => setActiveMenu(null)} widthClass="max-w-xs">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox id="native-yes" checked={filters.isNativeSpeaker === true} onCheckedChange={(checked) => handleFilterChange('isNativeSpeaker', checked ? true : (filters.isNativeSpeaker === true ? null : true) )} />
              <Label htmlFor="native-yes" className="text-xs sm:text-sm font-normal">Sadece Ana Dili Almanca Olanlar</Label>
            </div>
             <div className="flex items-center space-x-2">
              <Checkbox id="native-no" checked={filters.isNativeSpeaker === false} onCheckedChange={(checked) => handleFilterChange('isNativeSpeaker', checked ? false : (filters.isNativeSpeaker === false ? null : false) )} />
              <Label htmlFor="native-no" className="text-xs sm:text-sm font-normal">Ana Dili Almanca Olmayanlar</Label>
            </div>
          </div>
          <div className="flex justify-end gap-2 mt-4 pt-3 border-t border-zinc-200 dark:border-zinc-700">
            <Button variant="ghost" size="sm" onClick={() => handleFilterChange('isNativeSpeaker', null)}>Temizle</Button>
            <Button size="sm" onClick={() => setActiveMenu(null)}>Uygula</Button>
          </div>
        </FilterMenu>

        <div className="text-xs sm:text-sm text-muted-foreground mt-3 sm:mt-4 text-left">
          {totalTeachers} öğretmen bulundu
        </div>
      </CardContent>
    </Card>
  )
}