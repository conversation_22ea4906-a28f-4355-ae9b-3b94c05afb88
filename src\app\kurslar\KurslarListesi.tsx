"use client";
import React, { useState, useMemo, useRef, useEffect } from 'react';
import { Star, Clock, Users, BookOpen, ChevronLeft, ChevronRight, FilterX, LayoutList, LayoutGrid, Search, ArrowDown, CheckCircle, Zap, UsersRound, GraduationCap } from 'lucide-react'; // İkonlar eklendi
import { motion, AnimatePresence, PanInfo } from 'framer-motion';
import { Kurs, kurslarData } from '@/lib/data/courses';
import { Button } from '@/components/ui/button';

const filtreKategorileri = ["Tüm Kurslar", "A1", "A2", "B1", "B2", "C1", "C2", "<PERSON>ş Almancası", "Yoğunlaştırılmış"];

const CARD_WIDTH = 320; 
const SCALE_ACTIVE = 1;
const SCALE_NEIGHBOR_1 = 0.9; 
const SCALE_NEIGHBOR_2 = 0.8; 
const SCALE_OTHER = 0.7; 
const OPACITY_ACTIVE = 1;
const OPACITY_NEIGHBOR_1 = 0.8; 
const OPACITY_NEIGHBOR_2 = 0.6; 
const VISIBLE_CARDS_TOTAL = 5; 

const KurslarListesi = () => {
  const [selectedKurs, setSelectedKurs] = useState<Kurs | null>(null);
  const [activeFilter, setActiveFilter] = useState("Tüm Kurslar");
  const [activeIndex, setActiveIndex] = useState(0);
  const carouselContainerRef = useRef<HTMLDivElement>(null);
  const [viewMode, setViewMode] = useState<'carousel' | 'grid'>('carousel');

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  const calculateDiscount = (original: number, discounted: number) => {
    if (original === 0) return 0;
    return Math.round(((original - discounted) / original) * 100);
  };

  const handleDetayGoster = (kurs: Kurs) => {
    setSelectedKurs(kurs);
  };

  const handleDetayKapat = () => {
    setSelectedKurs(null);
  };

  const filteredCourses = useMemo(() => {
    if (activeFilter === "Tüm Kurslar") {
      return kurslarData;
    }
    return kurslarData.filter(kurs => 
      kurs.kategoriler && kurs.kategoriler.some(kategori => 
        kategori.toLowerCase().includes(activeFilter.toLowerCase()) || 
        activeFilter.toLowerCase().includes(kategori.toLowerCase())
      )
    );
  }, [activeFilter]);

  const scrollPrev = () => {
    setActiveIndex((prev) => (prev === 0 ? Math.max(0, filteredCourses.length - 1) : prev - 1));
  };

  const scrollNext = () => {
    setActiveIndex((prev) => (prev === filteredCourses.length - 1 ? 0 : prev + 1));
  };
  
  useEffect(() => {
    setActiveIndex(0);
  }, [activeFilter, filteredCourses.length]);

  const detailVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { opacity: 1, scale: 1, transition: { duration: 0.3, ease: "easeOut" } },
    exit: { opacity: 0, scale: 0.95, transition: { duration: 0.2, ease: "easeIn" } }
  };

  const gridCardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.05,
        duration: 0.4,
        ease: "easeOut"
      }
    })
  };
  
  return (
    <div className="font-sans max-w-full bg-slate-50 dark:bg-slate-950 min-h-screen">
      {/* Yenilenmiş Hero Bölümü */}
      <div className="relative text-center pt-12 pb-12 md:pt-16 md:pb-16 px-6 overflow-hidden">
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-br from-sky-500 via-cyan-400 to-emerald-500 dark:from-sky-800 dark:via-cyan-700 dark:to-emerald-700 opacity-90"></div>
          <div className="absolute -top-1/4 -left-1/4 w-1/2 h-1/2 bg-white/10 dark:bg-white/5 rounded-full filter blur-3xl animate-pulse-slow"></div>
          <div className="absolute -bottom-1/4 -right-1/4 w-1/2 h-1/2 bg-white/10 dark:bg-white/5 rounded-full filter blur-3xl animate-pulse-slower"></div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto">
          <motion.h1 
            initial={{ opacity: 0, y: -40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, ease: [0.6, -0.05, 0.01, 0.99], delay: 0.1 }}
            className="text-4xl sm:text-5xl md:text-5xl font-black mb-6 text-white tracking-tight drop-shadow-xl"
            style={{ textShadow: '0 2px 10px rgba(0,0,0,0.3)' }}
          >
            En İyi Almanca Kursu: A1&apos;den C2&apos;ye Online ve Uzman Eğitmenlerle Almanca Öğren
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.3, ease: [0.6, -0.05, 0.01, 0.99] }}
            className="text-md md:text-lg max-w-3xl mx-auto text-sky-100 dark:text-sky-200 mb-12 drop-shadow-md"
          >
            AlmancaABC ile hedeflerinize ulaşın! İster başlangıç (A1) ister ileri seviye (C2) olsun, online Almanca kurslarımızla dilbilgisi, kelime bilgisi ve akıcı konuşma becerileri kazanın. Goethe standartlarına yakın, esnek ve interaktif derslerle Almanca öğrenmek artık çok kolay. İstanbul, Ankara, İzmir veya dünyanın her yerinden AlmancaABC kalitesine erişin.
          </motion.p>
          <motion.div
            initial={{ opacity: 0, scale: 0.7 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.5, type: "spring", stiffness: 120, damping: 10 }}
          >
            <Button 
              size="lg" 
              className="bg-white text-sky-600 hover:bg-sky-100 dark:bg-sky-400 dark:text-sky-900 dark:hover:bg-sky-300 px-12 py-7 text-xl font-bold rounded-xl shadow-2xl hover:shadow-sky-500/40 transition-all duration-300 transform hover:scale-105 group"
              onClick={() => document.getElementById('kurs-filtreleri')?.scrollIntoView({ behavior: 'smooth' })}
            >
              Kursları Keşfet
              <ArrowDown size={24} className="ml-3 group-hover:translate-y-1 transition-transform duration-300" />
            </Button>
          </motion.div>
        </div>
      </div>

      {/* Filtre ve Görünüm Butonları Bölümü */}
      <div id="kurs-filtreleri" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative py-10 -mt-10 md:-mt-12 z-20"> 
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          className="p-4 md:p-6 bg-white dark:bg-slate-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200 dark:border-slate-700/80"
        >
          <div className="flex flex-col md:flex-row justify-between items-center gap-4 md:gap-6">
            <div className="flex flex-wrap gap-2.5 justify-center">
              {filtreKategorileri.map((kategori) => (
                <Button
                  key={kategori}
                  variant={activeFilter === kategori ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveFilter(kategori)}
                  className={`font-semibold transition-all duration-200 ease-in-out rounded-lg px-4 py-2 text-sm
                    ${activeFilter === kategori 
                      ? 'bg-gradient-to-r from-sky-500 to-cyan-500 text-white hover:from-sky-600 hover:to-cyan-600 dark:from-sky-600 dark:to-cyan-600 dark:hover:from-sky-700 dark:hover:to-cyan-700 ring-2 ring-sky-500/50 dark:ring-sky-400/50 shadow-lg transform scale-105' 
                      : 'bg-slate-100 text-slate-700 hover:bg-slate-200/70 dark:bg-slate-700/80 dark:text-slate-200 dark:hover:bg-slate-600/80 border-slate-300 dark:border-slate-600/80 hover:border-slate-400 dark:hover:border-slate-500'
                    }`}
                >
                  {kategori}
                </Button>
              ))}
            </div>
            <div className="flex items-center gap-1 mt-4 md:mt-0 p-1 bg-slate-200/70 dark:bg-slate-700/70 rounded-xl shadow-inner">
              <Button
                variant={viewMode === 'carousel' ? "default" : "ghost"}
                size="sm" 
                onClick={() => setViewMode('carousel')}
                className={`flex items-center gap-2 transition-all duration-200 rounded-lg px-3 h-9
                  ${viewMode === 'carousel' ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white hover:from-indigo-600 hover:to-purple-600 dark:from-indigo-600 dark:to-purple-600 dark:hover:from-indigo-700 dark:hover:to-purple-700 shadow-md' : 'text-slate-500 dark:text-slate-400 hover:bg-slate-300/50 dark:hover:bg-slate-600/50'}`}
                aria-label="Kaydırmalı Görünüm"
              >
                <LayoutList size={18} />
                Kaydırmalı
              </Button>
              <Button
                variant={viewMode === 'grid' ? "default" : "ghost"}
                size="sm" 
                onClick={() => setViewMode('grid')}
                className={`flex items-center gap-2 transition-all duration-200 rounded-lg px-3 h-9
                  ${viewMode === 'grid' ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white hover:from-indigo-600 hover:to-purple-600 dark:from-indigo-600 dark:to-purple-600 dark:hover:from-indigo-700 dark:hover:to-purple-700 shadow-md' : 'text-slate-500 dark:text-slate-400 hover:bg-slate-300/50 dark:hover:bg-slate-600/50'}`}
                aria-label="Izgara Görünüm"
              >
                <LayoutGrid size={18} />
                Izgara
              </Button>
            </div>
          </div>
        </motion.div>
        
        {/* Kurs Listesi (Carousel veya Grid) */}
        {filteredCourses.length > 0 ? (
          viewMode === 'carousel' ? (
            <div id="kaydirmali-kurs-listesi" className="relative mb-8 pt-6">
              {filteredCourses.length > 1 && (
                <>
                  <button 
                    onClick={scrollPrev} 
                    className="absolute left-0 md:-left-4 top-1/2 -translate-y-1/2 z-30 bg-white dark:bg-slate-700/80 backdrop-blur-sm rounded-full p-2.5 shadow-xl border border-gray-300 dark:border-slate-600 transition-all hover:bg-gray-100 dark:hover:bg-slate-600 hover:scale-110 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Önceki kurs"
                    disabled={activeIndex === 0}
                  >
                    <ChevronLeft size={28} className="text-sky-600 dark:text-sky-400" />
                  </button>
                  <button 
                    onClick={scrollNext} 
                    className="absolute right-0 md:-right-4 top-1/2 -translate-y-1/2 z-30 bg-white dark:bg-slate-700/80 backdrop-blur-sm rounded-full p-2.5 shadow-xl border border-gray-300 dark:border-slate-600 transition-all hover:bg-gray-100 dark:hover:bg-slate-600 hover:scale-110 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Sonraki kurs"
                    disabled={activeIndex === filteredCourses.length - 1}
                  >
                    <ChevronRight size={28} className="text-sky-600 dark:text-sky-400" />
                  </button>
                </>
              )}

              <div 
                ref={carouselContainerRef} 
                className="w-full overflow-hidden py-8 relative h-[550px] sm:h-[600px]"
                style={{ perspective: '1200px', perspectiveOrigin: 'center' }} 
              >
                <AnimatePresence initial={false} custom={activeIndex}>
                  {filteredCourses.map((kurs, i) => {
                    const distance = i - activeIndex;
                    const absDistance = Math.abs(distance);
                    
                    let scale, opacity, zIndex, xOffsetPercentage, rotateY;

                    if (distance === 0) { 
                      scale = SCALE_ACTIVE;
                      opacity = OPACITY_ACTIVE;
                      zIndex = 10;
                      xOffsetPercentage = 0;
                      rotateY = 0;
                    } else if (absDistance === 1) { 
                      scale = SCALE_NEIGHBOR_1;
                      opacity = OPACITY_NEIGHBOR_1;
                      zIndex = 5;
                      xOffsetPercentage = Math.sign(distance) * 50; 
                      rotateY = Math.sign(distance) * -10; 
                    } else if (absDistance === 2) { 
                      scale = SCALE_NEIGHBOR_2;
                      opacity = OPACITY_NEIGHBOR_2;
                      zIndex = 3;
                      xOffsetPercentage = Math.sign(distance) * 85; 
                      rotateY = Math.sign(distance) * -20;
                    } else { 
                      scale = SCALE_OTHER;
                      opacity = 0; 
                      zIndex = 1;
                      xOffsetPercentage = Math.sign(distance) * 110; 
                      rotateY = Math.sign(distance) * -30;
                    }
                    
                    if (absDistance > Math.floor(VISIBLE_CARDS_TOTAL / 2) && filteredCourses.length > VISIBLE_CARDS_TOTAL) {
                       opacity = 0; 
                       scale = SCALE_OTHER * 0.8; 
                    }
                    
                    return (
                      <motion.div
                        key={kurs.id + "-" + activeFilter + "-" + i} 
                        custom={distance}
                        initial={{ x: `calc(-50% + ${Math.sign(distance) * 200}%)`, opacity: 0, scale: 0.5, rotateY: Math.sign(distance) * 60 }}
                        animate={{
                          x: `calc(-50% + ${xOffsetPercentage}%)`,
                          opacity: opacity,
                          scale: scale,
                          zIndex: zIndex,
                          rotateY: rotateY,
                        }}
                        exit={{
                          x: `calc(-50% + ${Math.sign(distance) * -200}%)`,
                          opacity: 0,
                          scale: 0.5,
                          rotateY: Math.sign(distance) * -60,
                        }}
                        transition={{ type: "spring", stiffness: 180, damping: 22, mass:0.6 }}
                        className={`absolute top-0 left-1/2 cursor-pointer`}
                        style={{ width: `${CARD_WIDTH}px`, transformStyle: 'preserve-3d' }}
                        onClick={() => { if (distance !== 0) setActiveIndex(i); }}
                        drag="x"
                        dragConstraints={carouselContainerRef}
                        onDragEnd={(event, info: PanInfo) => { 
                          const offsetThreshold = CARD_WIDTH / 3;
                          const velocityThreshold = 200;
                          if (info.offset.x < -offsetThreshold || info.velocity.x < -velocityThreshold) {
                            scrollNext();
                          } else if (info.offset.x > offsetThreshold || info.velocity.x > velocityThreshold) {
                            scrollPrev();
                          }
                        }}
                      >
                        <div className={`rounded-xl shadow-2xl overflow-hidden flex flex-col justify-between h-full transition-shadow duration-300 ${kurs.renk} dark:bg-slate-800 border-2 ${distance === 0 ? 'border-sky-500 dark:border-sky-400 shadow-sky-500/40 dark:shadow-sky-400/30' : 'border-transparent hover:border-sky-300 dark:hover:border-sky-500'}`}>
                          <div>
                              <div className="p-6 relative">
                                {kurs.indirimli_fiyat < kurs.fiyat && (
                                  <div className={`absolute top-0 right-0 px-3 py-1 text-xs font-bold rounded-bl-lg shadow-md ${distance === 0 ? 'bg-amber-400 text-black' : 'bg-amber-500 text-white'}`}>
                                    %{calculateDiscount(kurs.fiyat, kurs.indirimli_fiyat)} İNDİRİM
                                  </div>
                                )}
                                <h3 className={`text-xs font-semibold mb-1 ${kurs.icon_renk} dark:text-sky-400 uppercase tracking-wider`}>{kurs.seviye}</h3>
                                <h2 className={`text-xl lg:text-2xl font-bold mb-2 h-16 line-clamp-2 ${distance === 0 ? 'text-sky-700 dark:text-sky-100' : 'text-gray-800 dark:text-slate-100'}`}>{kurs.baslik}</h2>
                                <p className="text-gray-700 dark:text-slate-300 text-sm mb-4 h-20 line-clamp-3">{kurs.aciklama}</p>
                                
                                <div className="mb-4 flex items-center">
                                  <div className="flex mr-2">
                                    {[...Array(5)].map((_, starIndex) => (
                                      <Star 
                                        key={starIndex} 
                                        size={16} 
                                        className={starIndex < Math.floor(kurs.puan) ? (distance === 0 ? "text-amber-500 fill-amber-500 dark:text-amber-400 dark:fill-amber-400" : "text-amber-400 fill-amber-400 dark:text-amber-500 dark:fill-amber-500") : "text-gray-300 dark:text-slate-600"}
                                      />
                                    ))}
                                  </div>
                                  <span className="text-xs font-medium text-gray-600 dark:text-slate-400">{kurs.puan}/5.0</span>
                                </div>
                                
                                <div className="grid grid-cols-1 gap-2 text-sm text-gray-700 dark:text-slate-300">
                                  <div className="flex items-center">
                                    <Clock size={14} className={`${kurs.icon_renk} dark:text-sky-400 mr-2 flex-shrink-0`} />
                                    <span>{kurs.sure}</span>
                                  </div>
                                  <div className="flex items-center">
                                    <Users size={14} className={`${kurs.icon_renk} dark:text-sky-400 mr-2 flex-shrink-0`} />
                                    <span>{kurs.ogrenci_sayisi}</span>
                                  </div>
                                  <div className="flex items-center">
                                    <BookOpen size={14} className={`${kurs.icon_renk} dark:text-sky-400 mr-2 flex-shrink-0`} />
                                    <span>{kurs.ders_sayisi}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm p-6 mt-auto border-t border-slate-200 dark:border-slate-700">
                              <div className="flex items-end justify-between mb-4">
                                <div>
                                  {kurs.indirimli_fiyat < kurs.fiyat && (
                                    <span className="text-gray-500 dark:text-slate-400 line-through text-xs block">{formatPrice(kurs.fiyat)}</span>
                                  )}
                                  <p className={`text-2xl lg:text-3xl font-bold ${distance === 0 ? 'text-sky-600 dark:text-sky-400' : 'text-gray-800 dark:text-slate-100'}`}>{formatPrice(kurs.indirimli_fiyat)}</p>
                                </div>
                                <Button 
                                  variant="default"
                                  size="sm"
                                  onClick={(e) => { e.stopPropagation(); handleDetayGoster(kurs);}}
                                  className={`font-semibold transition-all duration-300 shadow-md hover:shadow-lg
                                    ${distance === 0 ? 'bg-sky-600 hover:bg-sky-700 focus:ring-sky-400 dark:bg-sky-500 dark:hover:bg-sky-400 dark:focus:ring-sky-600' : 'bg-slate-600 hover:bg-slate-700 focus:ring-slate-300 dark:bg-slate-500 dark:hover:bg-slate-400 dark:focus:ring-slate-600'} text-white`}
                                >
                                  Detaylar
                                </Button>
                              </div>
                               <Button variant={distance === 0 ? "default" : "outline"} className={`w-full font-semibold transition-all duration-300 focus:outline-none focus:ring-2 
                                ${distance === 0 ? 'bg-sky-600 text-white hover:bg-sky-700 focus:ring-sky-400 dark:bg-sky-500 dark:hover:bg-sky-400 dark:focus:ring-sky-600' : 'border-sky-600 text-sky-600 hover:bg-sky-600 hover:text-white focus:ring-sky-300 dark:border-sky-400 dark:text-sky-300 dark:hover:bg-sky-400 dark:hover:text-white dark:focus:ring-sky-500'}`}>
                                Hemen Kayıt Ol
                              </Button>
                            </div>
                        </div>
                      </motion.div>
                    )
                  })}
                </AnimatePresence>
              </div>

              {filteredCourses.length > 1 && (
                <div className="flex justify-center mt-6 mb-8">
                  {filteredCourses.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveIndex(index)}
                      className={`h-2.5 w-2.5 mx-1.5 rounded-full transition-all duration-300 ease-in-out focus:outline-none
                        ${activeIndex === index ? 'bg-sky-600 dark:bg-sky-400 scale-125 w-4' : 'bg-gray-300 dark:bg-slate-600 hover:bg-gray-400 dark:hover:bg-slate-500'
                        }`}
                      aria-label={`Kursa git ${index + 1}`}
                    />
                  ))}
                </div>
              )}
            </div>
          ) : ( 
            <motion.div 
              layout
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12 pt-6"
            >
              {filteredCourses.map((kurs, i) => (
                <motion.div
                  key={kurs.id + "-grid"}
                  custom={i}
                  initial="hidden"
                  animate="visible"
                  variants={gridCardVariants} 
                  className={`rounded-xl shadow-2xl overflow-hidden flex flex-col justify-between transition-all duration-300 hover:shadow-sky-400/50 dark:hover:shadow-sky-500/30 ${kurs.renk} dark:bg-slate-800 border-2 border-transparent hover:border-sky-500 dark:hover:border-sky-400`}
                >
                  <div>
                    <div className="p-6 relative">
                      {kurs.indirimli_fiyat < kurs.fiyat && (
                        <div className="absolute top-0 right-0 bg-amber-500 text-white px-3 py-1 text-xs font-bold rounded-bl-lg shadow-md">
                          %{calculateDiscount(kurs.fiyat, kurs.indirimli_fiyat)} İNDİRİM
                        </div>
                      )}
                      <h3 className={`text-xs font-semibold mb-1 ${kurs.icon_renk} dark:text-sky-400 uppercase tracking-wider`}>{kurs.seviye}</h3>
                      <h2 className="text-xl lg:text-2xl font-bold mb-2 text-gray-800 dark:text-slate-100 h-16 line-clamp-2">{kurs.baslik}</h2>
                      <p className="text-gray-700 dark:text-slate-300 text-sm mb-4 h-20 line-clamp-3">{kurs.aciklama}</p>
                      
                      <div className="mb-4 flex items-center">
                        <div className="flex mr-2">
                          {[...Array(5)].map((_, starIndex) => (
                            <Star 
                              key={starIndex} 
                              size={16} 
                              className={starIndex < Math.floor(kurs.puan) ? "text-amber-400 fill-amber-400 dark:text-amber-500 dark:fill-amber-500" : "text-gray-300 dark:text-slate-600"}
                            />
                          ))}
                        </div>
                        <span className="text-xs font-medium text-gray-600 dark:text-slate-400">{kurs.puan}/5.0</span>
                      </div>
                      
                      <div className="grid grid-cols-1 gap-2 text-sm text-gray-700 dark:text-slate-300">
                        <div className="flex items-center">
                          <Clock size={14} className={`${kurs.icon_renk} dark:text-sky-400 mr-2 flex-shrink-0`} />
                          <span>{kurs.sure}</span>
                        </div>
                        <div className="flex items-center">
                          <Users size={14} className={`${kurs.icon_renk} dark:text-sky-400 mr-2 flex-shrink-0`} />
                          <span>{kurs.ogrenci_sayisi}</span>
                        </div>
                        <div className="flex items-center">
                          <BookOpen size={14} className={`${kurs.icon_renk} dark:text-sky-400 mr-2 flex-shrink-0`} />
                          <span>{kurs.ders_sayisi}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm p-6 mt-auto border-t border-slate-200 dark:border-slate-700">
                    <div className="flex items-end justify-between mb-4">
                      <div>
                        {kurs.indirimli_fiyat < kurs.fiyat && (
                          <span className="text-gray-500 dark:text-slate-400 line-through text-xs block">{formatPrice(kurs.fiyat)}</span>
                        )}
                        <p className="text-2xl lg:text-3xl font-bold text-gray-800 dark:text-slate-100">{formatPrice(kurs.indirimli_fiyat)}</p>
                      </div>
                      <Button 
                        variant="default"
                        size="sm"
                        onClick={() => handleDetayGoster(kurs)}
                        className={`font-semibold transition-all duration-300 
                          bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-opacity-75 shadow-md hover:shadow-lg dark:bg-sky-500 dark:hover:bg-sky-400 dark:focus:ring-sky-600 text-white`}
                      >
                        Detaylar
                      </Button>
                    </div>
                     <Button variant="outline" className="w-full font-semibold transition-all duration-300 focus:outline-none focus:ring-2 border-sky-600 text-sky-600 hover:bg-sky-600 hover:text-white focus:ring-sky-300 dark:border-sky-400 dark:text-sky-300 dark:hover:bg-sky-400 dark:hover:text-white dark:focus:ring-sky-500">
                      Hemen Kayıt Ol
                    </Button>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-16"
          >
            <FilterX size={64} className="mx-auto text-gray-400 dark:text-slate-500 mb-4" />
            <h3 className="text-2xl font-semibold text-gray-700 dark:text-slate-200 mb-2">Sonuç Bulunamadı</h3>
            <p className="text-gray-500 dark:text-slate-400">Filtrelerinizi değiştirmeyi veya arama teriminizi kontrol etmeyi deneyin.</p>
          </motion.div>
        )}

        {/* Yeni Eklenen İçerik Bölümü */}
        <section className="py-8 md:py-12 bg-white dark:bg-slate-900/70 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 dark:border-slate-700/80 mt-8">
          <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12 md:mb-16">
              <motion.h2 
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="text-2xl md:text-3xl lg:text-4xl font-bold text-slate-800 dark:text-slate-100 mb-4 tracking-tight"
              >
                AlmancaABC ile Almanca Öğrenmenin Farkını Yaşayın
              </motion.h2>
              <motion.p 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="text-base text-slate-600 dark:text-slate-300 max-w-2xl mx-auto"
              >
                Kişisel hedeflerinize ve öğrenme stilinize uygun, modern ve etkileşimli Almanca kurslarımızla tanışın.
              </motion.p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mb-12 md:mb-16">
              <motion.div 
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 0.3 }}
                className="bg-slate-100 dark:bg-slate-800 p-8 rounded-xl shadow-lg hover:shadow-sky-500/30 dark:hover:shadow-sky-400/20 transition-shadow duration-300"
              >
                <div className="flex justify-center md:justify-start mb-4">
                  <div className="p-3 bg-sky-500/20 dark:bg-sky-400/20 rounded-full">
                    <Zap size={28} className="text-sky-600 dark:text-sky-400" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-sky-700 dark:text-sky-300 mb-3 text-center md:text-left">Online Almanca Kursları</h3>
                <p className="text-slate-700 dark:text-slate-300 text-sm leading-relaxed text-center md:text-left">
                  Esnek programlarla, evinizin rahatlığında, ana dili Almanca olan uzman öğretmenlerimizle interaktif ve canlı derslere katılın.
                </p>
              </motion.div>
              
              <motion.div 
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 0.4 }}
                className="bg-slate-100 dark:bg-slate-800 p-8 rounded-xl shadow-lg hover:shadow-cyan-500/30 dark:hover:shadow-cyan-400/20 transition-shadow duration-300"
              >
                <div className="flex justify-center md:justify-start mb-4">
                  <div className="p-3 bg-cyan-500/20 dark:bg-cyan-400/20 rounded-full">
                     <UsersRound size={28} className="text-cyan-600 dark:text-cyan-400" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-cyan-700 dark:text-cyan-300 mb-3 text-center md:text-left">Kişiye Özel Almanca Dersleri</h3>
                <p className="text-slate-700 dark:text-slate-300 text-sm leading-relaxed text-center md:text-left">
                  Hedeflerinize, ilgi alanlarınıza ve öğrenme hızınıza göre tamamen size özel hazırlanan ders programlarıyla Almanca&apos;yı en etkili şekilde öğrenin.
                </p>
              </motion.div>

              <motion.div 
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 0.5 }}
                className="bg-slate-100 dark:bg-slate-800 p-8 rounded-xl shadow-lg hover:shadow-emerald-500/30 dark:hover:shadow-emerald-400/20 transition-shadow duration-300"
              >
                 <div className="flex justify-center md:justify-start mb-4">
                  <div className="p-3 bg-emerald-500/20 dark:bg-emerald-400/20 rounded-full">
                    <GraduationCap size={28} className="text-emerald-600 dark:text-emerald-400" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-emerald-700 dark:text-emerald-300 mb-3 text-center md:text-left">Çocuklar İçin Almanca</h3>
                <p className="text-slate-700 dark:text-slate-300 text-sm leading-relaxed text-center md:text-left">
                  5-17 yaş arası çocuklar ve gençler için özel olarak tasarlanmış, oyun tabanlı, eğlenceli ve interaktif Almanca programlarımızla geleceğe güvenle hazırlanın.
                </p>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <h3 className="text-2xl md:text-3xl font-bold text-slate-800 dark:text-slate-100 mb-6 text-center">
                Almanca Kurslarımızda Neler Sunuyoruz?
              </h3>
              <p className="text-slate-700 dark:text-slate-300 mb-4 leading-relaxed text-base">
                AlmancaABC olarak, her seviyeye ve bütçeye uygun, esnek Almanca kurs seçenekleri sunuyoruz. Uzun yıllara dayanan deneyimimizle, öğrencilerimizin Almanca konuşma hedeflerine ulaşmalarını sağlıyoruz. Kurslarımızda, akademik mükemmeliyeti, birebir etkileşimi ve başarısı kanıtlanmış modern öğrenme metotlarını bir araya getiriyoruz.
              </p>
              <p className="text-slate-700 dark:text-slate-300 mb-4 leading-relaxed text-base">
                Öğrencilerimizin öğrenme tercihleri, kişisel hedefleri ve yaş gruplarına göre çeşitlendirilmiş Almanca kursu alternatiflerimiz bulunmaktadır. Uzman eğitim danışmanlarımız, size en uygun Almanca eğitim programını oluşturmak ve öğrenme süreciniz boyunca size destek olmak için her zaman hazırdır. Ayırdığınız bütçe ve belirlediğiniz hedeflere göre seçtiğiniz eğitim programı, danışmanlarımız tarafından yakından takip edilir ve Almanca konuşma becerilerinizi en üst seviyeye taşımanız hedeflenir.
              </p>
              <h4 className="text-xl md:text-2xl font-semibold text-sky-600 dark:text-sky-400 mt-10 mb-5 text-center">Almanca Eğitim Seçeneklerimiz:</h4>
              <ul className="space-y-4 text-slate-700 dark:text-slate-300 mb-6 text-base">
                <li className="flex items-start p-4 bg-slate-100/50 dark:bg-slate-800/50 rounded-lg shadow">
                  <CheckCircle className="h-6 w-6 text-green-500 dark:text-green-400 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <span className="font-semibold text-slate-800 dark:text-slate-100">Esnek Online Grup Dersleri:</span> Ana dili Almanca olan deneyimli öğretmenlerle canlı ve interaktif grup dersleri.
                  </div>
                </li>
                <li className="flex items-start p-4 bg-slate-100/50 dark:bg-slate-800/50 rounded-lg shadow">
                  <CheckCircle className="h-6 w-6 text-green-500 dark:text-green-400 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <span className="font-semibold text-slate-800 dark:text-slate-100">Premium Özel Dersler:</span> Size özel atanan öğretmenle, tamamen hedeflerinize ve öğrenme hızınıza göre kişiselleştirilmiş ders planı.
                  </div>
                </li>
                <li className="flex items-start p-4 bg-slate-100/50 dark:bg-slate-800/50 rounded-lg shadow">
                  <CheckCircle className="h-6 w-6 text-green-500 dark:text-green-400 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <span className="font-semibold text-slate-800 dark:text-slate-100">İş Almancası Programları:</span> Kariyer hedeflerinize yönelik, sektörel terminoloji, profesyonel yazışma ve sunum becerileri.
                  </div>
                </li>
                <li className="flex items-start p-4 bg-slate-100/50 dark:bg-slate-800/50 rounded-lg shadow">
                  <CheckCircle className="h-6 w-6 text-green-500 dark:text-green-400 mr-3 mt-1 flex-shrink-0" />
                  <div>
                    <span className="font-semibold text-slate-800 dark:text-slate-100">Çocuklar ve Gençler İçin Almanca:</span> Yaş gruplarına özel, oyunlaştırılmış, eğlenceli ve motive edici programlarla dil sevgisi aşılıyoruz.
                  </div>
                </li>
              </ul>
              <p className="text-slate-700 dark:text-slate-300 leading-relaxed text-base text-center mt-8">
                AlmancaABC ile Almanca öğrenmek düşündüğünüzden daha kolay! Doğru destek, etkili iletişim ve modern öğrenme yöntemlerimizle Almanca&apos;yı akıcı bir şekilde konuşmaya başlayın. Her bütçeye ve her hedefe uygun, konuşma odaklı Almanca eğitimlerimizle tanışın.
              </p>
            </motion.div>
          </div>
        </section>

        <AnimatePresence>
          {selectedKurs && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50 backdrop-blur-lg"
              onClick={handleDetayKapat}
            >
              <motion.div
                variants={detailVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className={`bg-white dark:bg-slate-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto ${selectedKurs.renk} dark:bg-opacity-90 relative flex flex-col border border-slate-300 dark:border-slate-700`}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6 sm:p-8 flex-grow">
                  <button 
                    onClick={handleDetayKapat} 
                    className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 dark:text-slate-400 dark:hover:text-slate-100 transition-colors z-10 p-2 bg-white/60 dark:bg-slate-700/60 rounded-full hover:bg-white/90 dark:hover:bg-slate-600/90"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-6 h-6">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                  
                  <div className="mb-6">
                    <h3 className={`text-sm font-semibold mb-1 ${selectedKurs.icon_renk} dark:text-sky-400 uppercase tracking-wider`}>{selectedKurs.seviye}</h3>
                    <h2 className="text-3xl sm:text-4xl font-bold mb-3 text-gray-800 dark:text-slate-100">{selectedKurs.baslik}</h2>
                  </div>

                  <div className="prose prose-sm sm:prose-base max-w-none text-gray-700 dark:text-slate-300 mb-6">
                    <p className="lead text-gray-800 dark:text-slate-200">{selectedKurs.detayliAciklama || selectedKurs.aciklama}</p>
                    
                    {selectedKurs.icerik && selectedKurs.icerik.length > 0 && (
                      <>
                        <h4 className="font-semibold text-gray-800 dark:text-slate-100 mt-6 mb-2">Kurs İçeriği:</h4>
                        <ul className="list-disc list-inside space-y-1 pl-1">
                          {selectedKurs.icerik.map((item, index) => <li key={index}>{item}</li>)}
                        </ul>
                      </>
                    )}

                    {selectedKurs.hedefler && selectedKurs.hedefler.length > 0 && (
                      <>
                        <h4 className="font-semibold text-gray-800 dark:text-slate-100 mt-6 mb-2">Kurs Hedefleri:</h4>
                        <ul className="list-disc list-inside space-y-1 pl-1">
                          {selectedKurs.hedefler.map((item, index) => <li key={index}>{item}</li>)}
                        </ul>
                      </>
                    )}
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8 text-sm">
                    {[
                      { icon: Clock, label: "Süre", value: selectedKurs.sure },
                      { icon: Users, label: "Sınıf Mevcudu", value: selectedKurs.ogrenci_sayisi },
                      { icon: BookOpen, label: "Ders Saati", value: selectedKurs.ders_sayisi }
                    ].map(item => (
                      <div key={item.label} className="bg-white/60 dark:bg-slate-700/50 p-4 rounded-lg shadow-md text-center sm:text-left">
                        <item.icon size={24} className={`${selectedKurs.icon_renk} dark:text-sky-400 mb-2 mx-auto sm:mx-0`} />
                        <p className="font-semibold text-gray-700 dark:text-slate-200">{item.label}</p>
                        <p className="text-gray-600 dark:text-slate-300">{item.value}</p>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="sticky bottom-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm p-6 border-t border-gray-200 dark:border-slate-700 shadow-top-lg">
                  <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                    <div className="text-center sm:text-left">
                      {selectedKurs.indirimli_fiyat < selectedKurs.fiyat && (
                        <span className="text-gray-500 dark:text-slate-400 line-through text-sm block">{formatPrice(selectedKurs.fiyat)}</span>
                      )}
                      <p className="text-3xl font-bold text-sky-600 dark:text-sky-400">{formatPrice(selectedKurs.indirimli_fiyat)}</p>
                    </div>
                    <Button 
                      size="lg"
                      className={`w-full sm:w-auto font-semibold transition-colors duration-300
                      bg-gradient-to-r from-sky-500 to-cyan-500 text-white hover:from-sky-600 hover:to-cyan-600 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-opacity-75 shadow-lg hover:shadow-xl transform hover:scale-105 dark:from-sky-600 dark:to-cyan-600 dark:hover:from-sky-700 dark:hover:to-cyan-700 dark:focus:ring-sky-400`}>
                      Bu Kursa Kayıt Ol
                    </Button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* "Almanca Öğrenmeye Hazır Mısınız?" Bölümü */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="bg-gradient-to-br from-slate-100 via-gray-50 to-slate-100 dark:from-slate-800 dark:via-slate-800/70 dark:to-slate-900 rounded-2xl p-6 md:p-8 mt-8 shadow-xl border border-gray-200 dark:border-slate-700"
        >
          <div className="text-center max-w-3xl mx-auto">
            <Search size={48} className="mx-auto text-sky-500 dark:text-sky-400 mb-6" />
            <h2 className="text-2xl md:text-3xl font-bold mb-4 text-gray-800 dark:text-slate-100">Almanca Öğrenmeye Hazır Mısınız?</h2>
            <p className="text-gray-600 dark:text-slate-300 mb-8 text-base">
              Ücretsiz seviye tespit sınavımızla Almanca seviyenizi öğrenin ve size özel danışmanlık hizmetimizle en uygun kursu birlikte belirleyelim.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-gradient-to-r from-sky-500 to-cyan-500 text-white hover:from-sky-600 hover:to-cyan-600 font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 dark:from-sky-600 dark:to-cyan-600 dark:hover:from-sky-700 dark:hover:to-cyan-700">
                Ücretsiz Seviye Testi
              </Button>
              <Button variant="outline" size="lg" className="text-sky-600 font-semibold hover:bg-sky-500/10 transition-all duration-300 border-2 border-sky-500 shadow-lg hover:shadow-xl transform hover:scale-105 dark:text-sky-400 dark:border-sky-500 dark:hover:bg-sky-500/20">
                Bize Ulaşın
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
      
      <style jsx global>{`
        .hide-scrollbar::-webkit-scrollbar { display: none; }
        .hide-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
        .prose { color: #374151; }
        .dark .prose { color: #d1d5db; }
        .prose h4 { color: #1f2937; font-weight: 600; margin-top: 1.5rem; margin-bottom: 0.5rem; }
        .dark .prose h4 { color: #f3f4f6; }
        .prose ul { list-style-type: disc; padding-left: 1.25rem; }
        .prose ul li { margin-bottom: 0.25rem; }
        .prose p.lead { font-size: 1.125rem; line-height: 1.75rem; margin-bottom: 1rem; }
        .dark .prose p.lead { color: #e5e7eb; }
        .line-clamp-2 {
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
        .line-clamp-3 {
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
        }
        .shadow-top {
          box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .dark .shadow-top {
           box-shadow: 0 -4px 8px -1px rgba(0,0,0,0.4), 0 -2px 6px -1px rgba(0,0,0,0.3);
        }
        .shadow-top-lg {
           box-shadow: 0 -6px 12px -1px rgba(0,0,0,0.1), 0 -4px 8px -1px rgba(0,0,0,0.07);
        }
        .dark .shadow-top-lg {
           box-shadow: 0 -6px 15px -1px rgba(0,0,0,0.5), 0 -4px 10px -1px rgba(0,0,0,0.4);
        }
        .shadow-text {
          text-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }
        .shadow-text-sm {
          text-shadow: 0 1px 4px rgba(0,0,0,0.2);
        }
        .animate-pulse-slow {
          animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .animate-pulse-slower {
          animation: pulse 5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.3; transform: scale(0.95); }
          50% { opacity: 0.6; transform: scale(1.05); }
        }
      `}</style>
    </div>
  );
};

export default KurslarListesi;