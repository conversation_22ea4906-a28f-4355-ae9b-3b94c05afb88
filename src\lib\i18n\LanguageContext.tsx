"use client"

import React, { create<PERSON>ontext, use<PERSON>ontext, ReactNode } from "react"

interface Translations {
  [key: string]: string
}

const translations: Translations = {
  "hero.fullName": "Ad Soyad",
  "hero.email": "E-posta Adresiniz",
  "hero.startNow": "<PERSON><PERSON>",
  "nav.home": "<PERSON> Sayfa",
  "nav.teachers": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "nav.myLessons": "<PERSON>sler<PERSON>",
  "nav.about": "Hakkımı<PERSON>",
  "nav.contact": "İletişim",
  "nav.login": "<PERSON><PERSON><PERSON> Ya<PERSON>",
  "nav.register": "<PERSON><PERSON><PERSON> Ol",
}

type Language = "tr" | "de"

interface LanguageContextType {
  t: (key: string) => string
  language: Language
  setLanguage: (lang: Language) => void
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = React.useState<Language>("tr")
  const t = (key: string): string => {
    return translations[key] || key
  }

  return (
    <LanguageContext.Provider value={{ t, language, setLanguage }}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}