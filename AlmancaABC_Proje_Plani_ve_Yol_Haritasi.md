# AlmancaABC - <PERSON>ps<PERSON>l<PERSON> Proje <PERSON>, Stratejisi ve Yol Haritası

**Son Güncelleme:** 31 Mart 2025

## 0. Proje <PERSON>

- **Proje Adı:** AlmancaABC
- **Programlama Dilleri:** React, TypeScript, Tailwind CSS
- **Çerçeve:** Next.js 15+
- **Veritabanı:** Supabase
- **Kullanıcı Yetkilendirme:** Clerk 
- **Video SDK:** Zoom Video SDK
- **Title:** "AlmancaABC: Online Almanca Kursu & Özel Ders Öğretmeni Seç" veya En İyi Başlık Önerilerim: Kişiye Özel Almanca Dersleri | Online Kurs | almancaabc.com Neden: "Kişiye Özel" vurgusu, büyük platformlardan ayrışmanızı sağlar. Hem "Almanca Dersleri" hem de "Online Kurs" anahtar kelimelerini kapsar. Site adınızla markalaşmayı destekler. Kısa, öz ve etkili. (<PERSON>kt<PERSON> sayısı da ideal.) Almanca Özel Ders | Online Almanca Kursu | almancaabc.com Neden: En temel ve en çok aranan anahtar kelimeleri doğrudan içerir. Net ve anlaşılırdır. Site adınızla birlikte güçlü bir ilk izlenim bırakır. (Bu, bir önceki cevabımda da önerdiğim ve hala güçlü bulduğum bir seçenek.) Online Almanca Kursu ve Özel Ders | Uzman Eğitmen | almancaabc.com Neden: Hem kurs hem de özel ders hizmetini belirtir. "Uzman Eğitmen" ifadesi kalite algısı yaratır. Dikkat: Eğer birden fazla eğitmeniniz yoksa, "Uzman Eğitmeninizle" gibi tekil bir ifade daha doğru olabilir, ancak başlıkta kısa tutmak adına genel bir ifade de kullanılabilir. Almanca Öğren: Online Özel Ders ve Kurs Fırsatları | almancaabc.com Neden: "Almanca Öğren" gibi genel bir arama terimini hedefler. "Fırsatları" kelimesi ilgi çekebilir.
- **Meta Description** "AlmancaABC ile online Almanca dil kursları ve özel derslerle Almanca öğren! Uzman öğretmenler, esnek programlar ve uygun fiyatlar. Hemen başla!" 

## 1. Yönetici Özeti, Vizyon ve Hedefler

**Proje Adı:** AlmancaABC

**Vizyon:** Almanca diline odaklanarak, Türkiye başta olmak üzere ve Almanya'daki Türk ve Alman pazarlarındaki öğretmenler ile öğrencileri buluşturan lider online eğitim platformu olmak. Modern, kullanıcı dostu ve etkili bir deneyim sunarak, birebir ve grup dersleri, ek hizmetler (çeviri, danışmanlık, kurumsal eğitimler) ve güçlü bir topluluk ile Türk-Alman iş ve kültür dünyasına köprü kuran bir ekosistem yaratmak. **Temel amaç, sadece Almanca diline odaklanarak niş bir alanda pazar lideri olmaktır.**

**Orijinal Gereksinimler Özeti:** Almanca öğretmenlerini öğrencilerle buluşturan, canlı ders imkanı sunan, öğretmenlere gelir sağlayan bir platform. Mevcut blog trafiği (aylık 110-120 bin, %80 TR, %20 DE) değerlendirilecek. Gelir modeli başlangıçta öğretmenlerden %10 komisyon (ayarlanabilir yapı).

**Mevcut Güç:** Almanya'da yaşayan, iki kız babası bir girişimci tarafından yönetilen, aylık **120 bin** ziyaretçisi (%80 Türkiye, %20 Almanya) olan mevcut bir AlmancaABC blog sitesi bulunmaktadır. Bu organik trafik, platformun en büyük başlangıç avantajıdır.

**Temel Strateji:** Mevcut blog trafiğini kaldıraç olarak kullanarak, **düşük maliyetle hızlı bir MVP (Minimum Uygulanabilir Ürün)** oluşturmak. Pazara giriş yaptıktan sonra, kullanıcı geri bildirimleriyle platformu sürekli geliştirmek ve zamanla ek hizmetlerle ekosistemi genişleterek büyümek. **Nihai hedef, Almanca öğrenimi ve ilgili hizmetlerde pazar lideri olmaktır.**

**Ürün Hedefleri:**
1.  **Etkili Eşleştirme:** Almanca öğretmenlerini öğrencilerle buluşturan, kullanımı kolay, modern, sezgisel ve yüksek kullanıcı deneyimine sahip bir platform oluşturmak.
2.  **Kesintisiz Öğrenme:** Canlı dersler, takvim yönetimi, ders materyalleri ile eksiksiz bir öğrenme deneyimi sunmak.
3.  **Ticari Sürdürülebilirlik:** Komisyonlar, abonelikler ve ek hizmetlerle sürdürülebilir bir gelir modeli oluşturmak.

## 2. Pazar Analizi ve Konumlandırma

### 2.1. Pazar Fırsatı
*   **Türkiye-Almanya Ticaret Hacmi:** ~40 Milyar Euro (2022), Türk şirketleri ve profesyonelleri için Almanca ihtiyacını artırıyor.
*   **Göç ve Eğitim:** Almanya'daki göçmenler (özellikle aile birleşimi) ve öğrenciler için Almanca öğrenme kritik bir ihtiyaç.
*   **Niş Pazar:** Sadece Almanca'ya odaklanmak, genel dil platformlarına karşı uzmanlaşma ve derinleşme imkanı sunuyor.

### 2.2. Hedef Kitle
*   **Birincil:**
    *   Türkiye ve Almanya'daki Almanca öğrenmek isteyen bireyler (her seviye).
    *   Almanya'ya göç eden aileler (Aile Birleşimi).
    *   Türk-Alman ticaretiyle uğraşan profesyoneller ve şirket çalışanları.
*   **İkincil (Uzun Vadeli):** Avusturya, İsviçre gibi diğer Almanca konuşulan pazarlar.

### 2.3. Rakip Analizi

*Not: Kapsamlı rakip analizi (Preply, Özel Ders Alanı, Armut vb.) ve AlmancaABC için çıkarımlar `rakipler/rakip_site_karsilastirmali_analiz.md` dosyasına eklenmiştir. Aşağıdaki tablo genel bir özet sunmaktadır, detaylar için lütfen ilgili dosyayı inceleyiniz.*

#### Genel Bakış Tablosu

| Platform          | Güçlü Yönler                                     | Zayıf Yönler                                       | Tahmini Ciro   | Komisyon/Model                                  | AlmancaABC'nin Avantajı                                                                                                |
| :---------------- | :----------------------------------------------- | :------------------------------------------------- | :------------- | :---------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------- |
| **Preply.com**    | Geniş dil yelpazesi, Küresel erişim, Agresif Pazar | Yüksek Komisyon (%18-33), Ders paketi zorunluluğu   | $50-100M USD   | %18-33 Komisyon                                 | **Düşük Komisyon (%10-15)**, **Niş Uzmanlık (Almanca)**, **Aile/İş Odaklı Paketler**                                  |
| **italki.com**    | 150+ dil, Topluluk odaklı, %15 Komisyon          | İşlem ücretleri (%4), Genel odak                   | $10-20M USD    | %15 Komisyon + %4 İşlem Ücreti                  | **İşlem Ücretsiz Doğrudan Ödeme (Hedef)**, **Türk-Alman İş Odaklı Hizmetler**, **Niş Uzmanlık**                       |
| **Superprof.com.tr** | 1M+ öğretmen, 1.2k+ konu, Online/Yüzyüze, Deneme dersi | Sanal sınıf yok, Aylık abonelik, Dar konu kapsamı, Dış araçlar | -              | Aylık Abonelik                                  | **Niş Odak**, **Sanal Sınıf**, **Komisyon Modeli**                                                                     |
| **ozelders.com**  | Türkiye pazarı, Yerel ödeme, Türkçe arayüz       | Sınırlı öğretmen, Temel altyapı, Sınırlı dil desteği | $1.5-2M USD ?  | ?                                               | **Modern Teknoloji**, **Küresel Potensiyel**, **Niş Odak**                                                             |
| **ozeldersalani** | Türkiye pazarı, Basit arayüz                   | Modern değil, Sınırlı özellikler, Düşük UX         | $1.5-2M USD ?  | Öğretmenden Üyelik Ücreti                       | **Modern Teknoloji**, **Geniş Özellik Seti**, **Yüksek UX Hedefi**, **Küresel Potensiyel**                             |
| **AlmancaABC**    | **Niş Odak (Sadece Almanca)**, **Mevcut Blog Trafiği (120k)**, **Düşük Komisyon Hedefi (%10-15)**, **Türk-Alman İş Odaklı Potansiyel**, **Aile Odaklı Yaklaşım Potansiyeli**, **Almanya Merkezli Güvenilirlik (GDPR)**, **Modern Teknoloji** | Yeni platform, Başlangıçta sınırlı öğretmen havuzu | -              | %10-15 Komisyon (Ayarlanabilir, Kademeli Model) | -                                                                                                                      |

#### Rakip Detayları (Liste Formatı)

*   **Preply.com:**
    *   *Güçlü Yönler:* 35.000+ dil öğretmeni, Özelleştirilmiş 1-1 dersler, Konuşma takip araçları, Kişiselleştirilmiş öğrenme materyalleri.
    *   *Zayıf Yönler:* Yüksek komisyon oranları (%33-%18), Fiyatlandırma tutarsızlığı, Sadece seçili dillerde seviye değerlendirmesi.
    *   *Tahmini Ciro:* $50-100M USD.
    *   *Model:* %18-33 Komisyon.
    *   *AlmancaABC Avantajı:* Düşük Komisyon (%10-15), Niş Uzmanlık (Almanca), Aile/İş Odaklı Paketler.
*   **italki.com:**
    *   *Güçlü Yönler:* Sadece dil öğrenmeye odaklanma, Düşük komisyon oranı (%15), Grup dersi seçenekleri, Özel sanal sınıf.
    *   *Zayıf Yönler:* Minimum $30 para çekme limiti, Sadece dersler için kullanılabilir, Öğretmenlerden derece/yeterlilik talep edilmesi, İşlem ücretleri (%4).
    *   *Tahmini Ciro:* $10-20M USD.
    *   *Model:* %15 Komisyon + %4 İşlem Ücreti.
    *   *AlmancaABC Avantajı:* İşlem Ücretsiz Doğrudan Ödeme (Hedef), Türk-Alman İş Odaklı Hizmetler, Niş Uzmanlık.
*   **Superprof.com.tr:**
    *   *Güçlü Yönler:* 1 milyon+ öğretmen, 1.200+ konu seçeneği, Hem online hem yüz yüze ders seçeneği, Ücretsiz deneme dersleri.
    *   *Zayıf Yönler:* Sanal sınıf olmaması, Aylık abonelik gerektirmesi, Dar konu kapsamı, Dış araçlar gerektirmesi.
    *   *Model:* Aylık Abonelik.
    *   *AlmancaABC Avantajı:* Niş Odak, Sanal Sınıf, Komisyon Modeli.
*   **ozelders.com:**
    *   *Güçlü Yönler:* Türkiye pazarına özel, Yerel ödeme seçenekleri, Türkçe arayüz.
    *   *Zayıf Yönler:* Sınırlı öğretmen sayısı, Temel teknoloji altyapısı, Sınırlı yabancı dil desteği.
    *   *Tahmini Ciro:* $1.5-2M USD ?
    *   *AlmancaABC Avantajı:* Modern Teknoloji, Küresel Potensiyel, Niş Odak.
*   **ozeldersalani.com:**
    *   *Güçlü Yönler:* Birçok konuda özel ders, Türkiye pazarına özel, Basit kullanıcı arayüzü.
    *   *Zayıf Yönler:* Modern olmayan tasarım, Sınırlı özellikler, Düşük kullanıcı deneyimi.
    *   *Tahmini Ciro:* $1.5-2M USD ?
    *   *Model:* Öğretmenden Üyelik Ücreti.
    *   *AlmancaABC Avantajı:* Modern Teknoloji, Geniş Özellik Seti, Yüksek UX Hedefi, Küresel Potensiyel.
*   **AlmancaABC:**
    *   *Güçlü Yönler:* Niş Odak (Sadece Almanca), Mevcut Blog Trafiği (120k), Düşük Komisyon Hedefi (%10-15), Türk-Alman İş Odaklı Potansiyel, Aile Odaklı Yaklaşım Potansiyeli, Almanya Merkezli Güvenilirlik (GDPR), Modern Teknoloji.
    *   *Zayıf Yönler:* Yeni platform, Başlangıçta sınırlı öğretmen havuzu, İlk gelir elde etme zorluğu.
    *   *Model:* %10-15 Komisyon (Ayarlanabilir, Kademeli Model).

#### Rekabet Kuadrant Grafiği

```mermaid
quadrantChart
    title "Online Dil Öğrenme Platformları Karşılaştırması"
    x-axis "Düşük Özelleştirme" --> "Yüksek Özelleştirme"
    y-axis "Düşük Kullanıcı Deneyimi" --> "Yüksek Kullanıcı Deneyimi"
    quadrant-1 "Lider Platformlar"
    quadrant-2 "Özelleştirilmiş Nişler"
    quadrant-3 "Temel Hizmetler"
    quadrant-4 "Kullanıcı Dostu Temel Platformlar"
    "Preply": [0.75, 0.80]
    "italki": [0.70, 0.75]
    "Superprof": [0.55, 0.65]
    "ozelders.com": [0.40, 0.45]
    "ozeldersalani.com": [0.30, 0.35]
    "AlmancaABC Hedefi": [0.65, 0.85]
```

### 2.4. Farklılaşma Stratejisi ve Benzersiz Satış Noktaları (USP)
*   **Niş Uzmanlık:** Sadece Almanca'ya odaklanarak rakiplerin genel hizmetlerine karşı derinlemesine uzmanlık sunmak.
*   **Akıllı Fiyatlandırma:** 
    * Öğretmenin müsaitliğine ve talebe göre dinamik fiyatlandırma (€25-50/saat)
    * Öğretmenin tercihi ve durumuna göre esnek komisyon oranları (%10-50)
    * Şeffaf ve kullanıcı dostu fiyat politikası
*   **Üstün Kullanıcı Deneyimi:** Modern, hızlı, SEO uyumlu, kullanıcı dostu arayüz ve mükemmel müşteri desteği (<30dk yanıt hedefi).
*   **Ekosistem Yaklaşımı:** Ders platformunun ötesinde çeviri, danışmanlık, iş bağlantıları ve etkinliklerle değer yaratmak.
*   **Mevcut Trafik:** 120 binlik blog trafiğini platforma entegre etme.
*   **Yerel Avantaj:** Almanya merkezli olmak (güvenilirlik, GDPR).

### 2.5. Kullanıcı Hikayeleri
*   **Öğretmen Olarak:** Almanca öğretmeni olarak, uygun olduğum saatleri belirtebilmek ve öğrencilerle canlı dersler yaparak gelir elde edebilmek istiyorum.
*   **Öğrenci Olarak:** Almanca öğrenmek isteyen biri olarak, kendime uygun fiyat ve seviyede öğretmen bulabilmek, ders saatleri planlayabilmek ve canlı dersler alabilmek istiyorum.
*   **Öğretmen Olarak:** Öğrencilerimin gelişimini takip edebilmek, ders materyalleri paylaşabilmek ve düzenli gelir elde edebilmek istiyorum.
*   **Öğrenci Olarak:** Aldığım dersleri değerlendirebilmek, öğretmenleri değerlendirebilmek, öğretmenlerle iletişim kurabilmek ve öğrenme sürecimi takip edebilmek istiyorum.
*   **Platform Sahibi Olarak:** Platformun kullanımını izleyebilmek, öğretmen-öğrenci eşleşmelerinden komisyon alabilmek ve kullanıcı deneyimini sürekli iyileştirebilmek istiyorum.

## 3. Ürün ve Hizmet Yelpazesi (Detaylı)

### 3.1. Çevrimiçi Almanca Dersleri (MVP ve Sonrası)
*   **Birebir Dersler:** €15-€20/saat (Öğretmen belirler, platform komisyon alır). Ücretsiz veya düşük ücretli deneme dersleri.
*   **Grup Dersleri:** €10-€15/saat/kişi (MVP sonrası).
*   **İş Almancası Paketleri:** Haftalık veya ders bazlı özel içerik (€50-€200).
*   **Aile Paketleri:** Çocuklar ve ebeveynler için kombine/indirimli dersler (€25/saat hedefi).

### 3.2. Kurumsal Eğitim Paketleri (MVP Sonrası)
*   Türk ve Alman şirketlere özel toplu eğitimler.
*   Fiyatlandırma: Çalışan sayısına göre (€1.000-€1.500/ay hedefi).
*   İçerik: Haftalık grup dersleri, bireysel takip, raporlama.

### 3.3. Çeviri ve Tercüme Hizmetleri (MVP Sonrası)
*   **Belge Çevirisi:** €0,10/kelime (Yeminli tercüman ağı kurulabilir).
*   **Simultane Tercüme:** €100/saat.
*   **Web Sitesi/Yazılım Lokalizasyonu:** €500-€2.000/proje.
*   **Yapay Zeka Destekli Çeviri:** Daha uygun fiyatlı, hızlı çeviriler (Reklam veya küçük ücretlerle gelir modeli).

### 3.4. Danışmanlık Hizmetleri (MVP Sonrası)
*   **İş Almancası Danışmanlığı:** €50/saat.
*   **Almanya Pazarına Giriş Stratejileri:** €75/saat.

### 3.5. Mobil Uygulamalar (MVP Sonrası)
*   **Ücretsiz Uygulama:** Kelime öğrenme, alıştırmalar, temel dersler (Reklam gelirli - Yıllık $120k potansiyel).
*   **Premium Uygulama:** Reklamsız, offline erişim, ek içerik (€5/ay).
*   *Teknoloji: React Native/Expo ile mevcut kod tabanından yararlanma.*

### 3.6. Eğitim Materyalleri (MVP Sonrası)
*   **Dijital Ürünler:** E-kitaplar (€10), PDF notlar (€5), Flashcard setleri (€3).
*   **Satış Modeli:** Platform içi mağaza, Stripe/Paypal/iyzico ile ödeme.
*   **Ücretsiz Kaynaklar:** Reklam izleme veya mikro-ödeme (€1.99) karşılığı erişim.

### 3.7. Ekosistem Hizmetleri (Uzun Vadeli)
*   **Türk-Alman İş Bağlantı Platformu:** Şirketleri dil uzmanlarıyla buluşturma (%10 komisyon).
*   **Konferanslar ve Workshoplar:** Almanca öğrenimi, iş kültürü üzerine etkinlikler (€50/katılım - Yıllık $50k potansiyel).
*   **Almanca-Türkçe Sözlük:** Mevcut 750k kelimelik SQLite veritabanını entegre etme (Supabase'e aktarım veya ayrı servis). Modern arama arayüzü (Fuse.js veya PostgreSQL Full-Text Search).

## 4. Gelir Modeli ve Finansal Projeksiyonlar

### 4.1. Gelir Akışları ve Fiyatlandırma Stratejisi
*   **Ders Komisyonları:**
    * **Standart Oran:** %25 (€25/saat baz fiyat)
    * **Müsaitlik/Talep Bazlı Dinamik Oran:** 
      * Müsait olmayan/yoğun öğretmenler için: %50 (€50/saat)
      * Normal dönemler için: %25 (€25/saat)
      * Düşük talep dönemleri için: %10-15 (öğretmen tercihi)
*   **Diğer Gelir Kaynakları:**
    * Kurumsal Paket Satışları
    * Çeviri ve Danışmanlık Hizmetleri
    * Mobil Uygulama (Reklam + Premium)
    * Eğitim Materyali Satışı
    * İş Bağlantı Komisyonları
    * Etkinlik Biletleri
    * (Opsiyonel) Öne Çıkan Öğretmen Ücretleri
    * (Opsiyonel) Affiliate Marketing

*Not: Fiyatlandırma stratejisi öğretmenin müsaitliği ve talebi göz önünde bulundurularak dinamik olarak belirlenir. Yoğun/müsait olmayan öğretmenler için yüksek fiyat (€50/saat), normal dönemlerde standart fiyat (€25/saat), düşük talep dönemlerinde ise öğretmenin tercihine bağlı olarak %10-15 komisyon uygulanır.*

### 4.2. Finansal Projeksiyonlar (Tahmini)

| Senaryo         | Aktif Öğrenci | Yıllık Ders Ciro ($) | Ek Hizmetler Ciro ($) | Toplam Ciro ($) | Net Kâr ($) (%50 Maliyet Oranıyla) |
| :-------------- | :------------ | :------------------- | :-------------------- | :-------------- | :--------------------------------- |
| Düşük Senaryo   | 1.000         | 104.000              | 234.000               | 338.000         | 169.000                            |
| Orta Senaryo    | 7.200         | 748.800              | 800.000               | 1.548.800       | 774.400                            |
| Yüksek Senaryo  | 10.000        | 1.040.000            | 1.000.000             | 2.040.000       | 1.020.000                          |
| **Domine Etme** | **10.000+**   | **1.04M+**           | **1.5M+**             | **2.5M - 5M+**  | **1.25M - 2.5M+**                  |

*Not: Ek hizmetler cirosu çeviri ($180k), danışmanlık ($60k), mobil uygulama ($120k), konferans ($50k) vb. gelirleri içerir. Net kâr, operasyonel maliyetlerin cironun yaklaşık %50'si olduğu varsayımıyla hesaplanmıştır.*

#### Finansal Projeksiyon Detayları (Liste Formatı)

*   **Düşük Senaryo:**
    *   *Aktif Öğrenci:* 1.000
    *   *Yıllık Ders Ciro:* $104.000
    *   *Ek Hizmetler Ciro:* $234.000
    *   *Toplam Ciro:* $338.000
    *   *Net Kâr (%50 Maliyet):* $169.000
*   **Orta Senaryo:**
    *   *Aktif Öğrenci:* 7.200
    *   *Yıllık Ders Ciro:* $748.800
    *   *Ek Hizmetler Ciro:* $800.000
    *   *Toplam Ciro:* $1.548.800
    *   *Net Kâr (%50 Maliyet):* $774.400
*   **Yüksek Senaryo:**
    *   *Aktif Öğrenci:* 10.000
    *   *Yıllık Ders Ciro:* $1.040.000
    *   *Ek Hizmetler Ciro:* $1.000.000
    *   *Toplam Ciro:* $2.040.000
    *   *Net Kâr (%50 Maliyet):* $1.020.000
*   **Domine Etme Senaryosu:**
    *   *Aktif Öğrenci:* 10.000+
    *   *Yıllık Ders Ciro:* $1.04M+
    *   *Ek Hizmetler Ciro:* $1.5M+
    *   *Toplam Ciro:* $2.5M - $5M+
    *   *Net Kâr (%50 Maliyet):* $1.25M - $2.5M+

### 4.3. Tahmini Giderler (Başlangıç)
*   Platform (Hosting, BaaS): €50-€200/ay
*   Pazarlama: €1.000/ay (Başlangıç)
*   Müşteri Desteği: €500/ay (Başlangıç)
*   Ödeme İşlem Ücretleri: ~%2 (Stripe/Paypal/iyzico)
*   Yasal/Kurulum: €1.500-€2.500 (Tek seferlik)
*   **Toplam İlk Yıl Gider (Tahmini):** ~€24.000 ($26.000)

## 5. Pazarlama ve Büyüme Stratejisi

*   **Temel:** Mevcut 120k blog trafiğini platforma yönlendirme (CTA'lar, linkler, duyurular, ücretsiz deneme dersleri).
*   **Öğretmen Edinme:** Düşük komisyon (%10-15), ilk X öğretmene %0 komisyon, kalite kontrol/onay süreci, Türk-Alman Ticaret Odası işbirliği, blog yazarlarına teklif.
*   **Öğrenci Edinme:**
    *   **Almanya:** Sosyal medya (Instagram, LinkedIn), göçmen forumları, dil okulu ortaklıkları.
    *   **Türkiye:** Blog/Sosyal medya (Mevcut hesapları kullanarak: Facebook 15k, Instagram 21k, TikTok 22k, YouTube 1.1k, X 200, Telegram 650), Türk-Alman Ticaret Odası bağlantıları, şirketlere özel tanıtımlar, e-posta pazarlaması.
*   **Hedefli Kampanyalar:** Aile paketleri, iş dünyasına özel indirimler.
*   **SEO:** Platform sayfaları için de optimizasyon, anahtar kelime odaklı ve kullanıcı deneyimi üzerine kaliteli içerik üretimi (Almanca Konuları, Almanya'da yaşam vb.).
*   **Diğer:** Affiliate sistemleri, influencer marketing, küçük bütçeli ücretli reklam testleri (Google Ads, Facebook Ads).
*   **Geri Bildirim ve İyileştirme:** Sürekli geri bildirim toplama (anketler, sosyal medya takibi, rakip analizi) ve platformu geliştirme.
*   **Büyüme Hedefleri:**
    *   Yıl 1: Almanya ve Türkiye'de liderlik ($1M ciro).
    *   Yıl 3: Avusturya/İsviçre'ye açılma ($5M ciro).
    *   Yıl 5: Tüm Almanca konuşulan pazarlarda dominasyon ($10M ciro).

## 6. Yasal Konular ve Şirket Kurulumu (Almanya)

*   **Şirket Türü:** UG ile başlanması önerilir (Mali Müşavir ile görüşülecek).
*   **Yasal Metinler:** Impressum, Datenschutzerklärung, AGB (Avukat ile hazırlanacak). İlgili sistemler/altyapılar buna göre kurulacak.
*   **GDPR Uyumluluğu:** Çok önemli. Veri işleme, çerez onayı, veri silme talepleri mekanizmaları kurulacak. 3. parti servislerin uyumu kontrol edilecek.
*   **Vergilendirme:** KDV (Umsatzsteuer) vb. için Mali Müşavir ile çalışılacak. Faturalandırma sistemi buna uygun olacak.
*   **Türkiye Operasyonları:** Gelecekte şube veya iş ortaklığı düşünülebilir.

## 7. Teknoloji Yığını (Kararlaştırılan ve Seçim Bekleyenler)

*   **Frontend:** Next.js 15+ (App Router), React 19, TypeScript - ✅
*   **UI Kütüphanesi:** Shadcn/ui + Tailwind CSS - ✅
*   **Backend/Veritabanı:** Supabase (PostgreSQL) - ✅
*   **Kimlik Doğrulama:** Clerk - ✅ (Mevcut entegrasyon nedeniyle)
*   **ORM:** Prisma - ✅ (Tip güvenliği, DX, migration)
*   **Video Konferans SDK:** **Zoom SDK** - ✅ (Tercihiniz. VideoSDK/Dyte alternatif)
*   **Ödeme Sistemi:** **Stripe (Stripe Connect ile)** - ✅ (MVP için. Paypal, iyzico MVP sonrası eklenebilir)
*   **Takvim Kütüphanesi:** **FullCalendar** - ✅ (Gelişmiş özellikler ve Google Takvim senkronizasyon potansiyeli nedeniyle)
*   **State Management:** **Başlangıçta Gerek Yok** - ✅ (React'in kendi araçları yeterli. Gerekirse Zustand/Jotai)
*   **Dil Desteği (i18n):** **next-intl veya benzeri** - 🟡 (Türkçe öncelikli. WordPress PO/MO benzeri kolay çeviri sistemi hedefi)
*   **Hosting:** Vercel - ✅
*   **Hata Takibi:** Sentry - 🟡 (MVP sonrası)
*   **Geliştirme Ortamı:** Node.js, **bun**, Git - ✅
*   **Video Hosting (Offline Kurslar):** **YouTube Linkleri** - ✅ (MVP sonrası başlangıç için. Sonra Mux/Cloudinary)

## 8. Geliştirme Yol Haritası ve Checklist (Detaylı)

*(Bu doküman, projenin teknik brief'i olarak da kullanılabilir.)*

### Aşama 0: Hazırlık ve Yasal Kurulum (Geliştirmeden Önce / Paralel)
☐ 0.1. Yasal Danışmanlık Alınması (Şirket yapısı, Yasal metinler - Avukat/Mali Müşavir)
☐ 0.2. Geliştirici/Ajans Seçimi - *Şu an Roo Code ile çalışılıyor.*

### Aşama 1: Planlama ve Temel Kurulum (Tamamlandı)
*   ✅ Platform Özellikleri Netleştirildi (Bu doküman)
*   ✅ Teknoloji Yığını Seçildi (Bölüm 7)
*   ✅ Geliştirme Ortamı Kuruldu (Node.js, bun, Git)
*   ✅ Repo Oluşturuldu (GitHub)
*   ✅ Proje Başlangıç Ayarları Yapıldı (Next.js, Supabase, Clerk)
*   ✅ Supabase MCP Bağlantısı Kuruldu

### Aşama 2: Temel Özelliklerin Geliştirilmesi (MVP Odaklı - Devam Ediyor)
*   **2.1. Kullanıcı Kimlik Doğrulama ve Rol Yönetimi**
    *   ✅ Clerk Entegrasyonu Tamamlandı
    *   ✅ Middleware ile Temel Rota Koruması Eklendi (/teacher/**, /student/**)
    *   ✅ `/unauthorized` Sayfası Oluşturuldu
    *   ⚙️ Rol Atama Mekanizması (Kayıt sırasında, Clerk Custom Attributes ile - *Clerk ayarı bekleniyor*)
    *   ✅ Temel Profil Sayfaları Oluşturuldu (`/teacher/profile`, `/student/profile`)
*   **2.2. Öğretmen Profili ve Yönetimi**
    *   ⚙️ Öğretmen Başvuru ve Onay Süreci (Basit form + Admin onayı)
    *   ⚙️ Onaylı Öğretmen Profil Yönetimi (Biyo, uzmanlık, ücret, fotoğraf, YouTube video linki vb. formları)
    *   ⚙️ Veritabanı: `teachers` tablosu (Supabase), ORM ile CRUD işlemleri (*Prisma kurulumu yapıldı, tablo şeması eklendi, migrate bekleniyor*)
    *   ⚙️ Öğretmen Takvim Yönetimi (Müsaitlik seçme arayüzü - FullCalendar ile)
*   **2.3. Öğrenci Ders Arama ve Öğretmen Listeleme**
    *   ⚙️ Öğretmen Listeleme Sayfası (Dinamik veri + Temel filtreler)
    *   ⚙️ Öğretmen Detay Sayfası (Dinamik veri, müsaitlik gösterimi, rezervasyon butonu)
*   **2.4. Temel Ders Rezervasyon ve Ödeme Akışı**
    *   ⚙️ Öğrencinin Müsait Zamandan Ders Seçimi Arayüzü (FullCalendar ile)
    *   ⚙️ Ödeme Entegrasyonu (Stripe - Ders satın alma)
    *   ⚙️ Komisyon Hesaplama ve Aktarım (Stripe Connect - Temel kurulum)
    *   ⚙️ Rezervasyon Onayı (Bildirim/E-posta - Temel)
    *   ⚙️ Veritabanı: `bookings`, `payments` tabloları
*   **2.5. Canlı Ders Entegrasyonu**
    *   ⚙️ Video SDK Seçimi ve Entegrasyonu (Zoom SDK)
    *   ⚙️ Otomatik Ders Odası Oluşturma (Rezervasyon sonrası)
    *   ⚙️ Temel Ders Odası Arayüzü (Video/ses, paylaşım, sohbet)
*   **2.6. Temel UI ve Stil**
    *   ✅ Temel Layout ve Sayfa Yapısı (Kısmen)
    *   ✅ Stil ve Temalandırma (Tailwind CSS)
    *   ✅ Shadcn/ui Bileşenleri Entegrasyonu (Kısmen)
    *   ⚙️ Modern Takvim Kütüphanesi Entegrasyonu (FullCalendar)
    *   ⚙️ İskelet Yükleyiciler (Skeleton Loaders)
    *   ⚙️ Temel Hata Yönetimi (Formlar, API çağrıları)

### Aşama 3: Test, Yayınlama ve Geri Bildirim Toplama (MVP)
☐ 3.1. Test Aşaması (Fonksiyonel Testler, Kullanıcı Testleri - Beta)
☐ 3.2. Yayınlama (Deployment - Vercel, Environment Variables, Domain)
☐ 3.3. Geri Bildirim Toplama ve Lansman Sonrası İyileştirme (Geri Bildirim Formu, Analitik, Hata Düzeltme)

### Aşama 4: Platformu Genişletme ve Büyütme (MVP Sonrası)
☐ 4.1. Ödeme Sistemi Geliştirmeleri (Öğretmen ödemeleri otomasyonu, faturalandırma, Paypal/iyzico ekleme)
☐ 4.2. Offline Video Kurslar (YouTube linkleri ile başlanacak, sonra Mux/Cloudinary)
☐ 4.3. Diğer Satış Kanalları (Materyal, Mikro-ödeme, Reklam)
☐ 4.4. Gelişmiş Takvim (Google/Outlook Senkronizasyonu)
☐ 4.5. Almanca-Türkçe Sözlük Entegrasyonu (Mevcut SQLite -> Supabase)
☐ 4.6. Pazarlama ve Büyüme (SEO, Sosyal Medya, Blog, Tavsiye/Affiliate Programları)
☐ **Ek Özellikler (Rakip Analizinden Esinlenerek - Öğretmenler Sayfası ve Genel Platform):**
    ☐ 4.7.1. Detaylı Sınav Türü Filtreleri (Goethe, TestDaF, TELC vb.) Ekleme
    ☐ 4.7.2. Öğretmen Kartlarına (Liste/Izgara) Deneyim Yılı Bilgisi Ekleme
    ☐ 4.7.3. Gelişmiş Sıralama Seçenekleri Ekleme (En Çok Ders Veren, En Yeni Katılanlar vb.)
    ☐ 4.7.4. Öğretmen Kartlarına Video Tanıtım Linki/İkonu Ekleme
    ☐ 4.7.5. Kullanıcıya Özel Öğretmen Öneri Sistemi Geliştirme (Basit bir anket veya tercih formu ile başlayabilir)
    ☐ 4.7.6. Platform Güven Artırıcı Unsurları (Memnuniyet Garantisi, Ücretsiz Öğretmen Değişikliği Politikası vb.) Sayfalarda Vurgulama
    ☐ 4.7.7. Öğretmenler Sayfasına ve Genel SSS Bölümüne Detaylı Bilgiler Ekleme
    ☐ 4.7.8. "Neden AlmancaABC?" (Avantajlarımız) Bölümü Oluşturma
    ☐ 4.7.9. Öğretmen Profillerinde Sertifika Yükleme ve Görüntüleme Alanı
☐ Ek Özellikler (Mevcut): Grup Dersleri, Seviye Tespit Sınavları, Değerlendirme Sistemi, Bildirim Sistemi, Mobil Uygulama (React Native/Expo), Gelişmiş Paneller.

### Aşama 5: Sürdürülebilirlik ve Güvenlik (Uzun Vadeli)
☐ 5.1. Güvenlik İyileştirmeleri (Denetim, Şifreleme, Yedekleme, GDPR)
☐ 5.2. Ölçeklenebilirlik İyileştirmeleri (Performans testleri, Altyapı optimizasyonu, Caching - Redis/Vercel Edge)
☐ 5.3. Müşteri Desteği (SSS, Canlı destek/E-posta, WhatsApp, LinkedIn - Hızlı yanıt hedefi <30dk)
☐ 5.4. Yeni Özellikler (Geri bildirimlere göre)
☐ 5.5. İş Modeli Geliştirme (Farklı gelir modelleri, Ortaklıklar, Uluslararası Pazar)

## 9. Geliştirme Süreci ve Notlar

*   **İteratif Yaklaşım:** MVP odaklı, küçük adımlarla ilerlenecek, kullanıcı geri bildirimleri dikkate alınacaktır.
*   **İletişim:** Sürekli ve net iletişim esastır.
*   **Bütçe:** Ücretsiz katmanlar ve açık kaynaklı çözümler önceliklendirilecektir.
*   **Mükemmeliyetçilikten Kaçınma:** Hızlıca piyasaya çıkıp test etmek önemlidir.

## 10. Ölçüm ve Başarı Kriterleri (PRD'den)

### 10.1. Temel Metrikler
*   **Kayıt Oranı:** Blog ziyaretçilerinden platforma kayıt olan kullanıcı oranı
*   **Dönüşüm Oranı:** Kayıt olan öğrencilerden ders alan öğrenci oranı
*   **Öğretmen Aktivasyonu:** Kayıt olan öğretmenlerden aktif ders veren öğretmen oranı
*   **Ders Tamamlama:** Planlanan derslerin tamamlanma oranı
*   **Kullanıcı Memnuniyeti:** Öğretmen ve öğrenci memnuniyet puanı (Değerlendirme sistemi sonrası)
*   **Tekrarlanan Dersler:** Aynı öğretmenle birden fazla ders alan öğrenci oranı

### 10.2. Büyüme Hedefleri (PRD'den)
*   **İlk 3 Ay:** 100+ öğretmen, 500+ öğrenci, 300+ ders
*   **İlk 6 Ay:** 250+ öğretmen, 1.500+ öğrenci, 1.000+ ders
*   **İlk Yıl:** 500+ öğretmen, 5.000+ öğrenci, 10.000+ ders

### 10.3. Gelir Hedefleri (PRD'den)
*   **İlk 3 Ay:** 5.000€ brüt işlem hacmi
*   **İlk 6 Ay:** 20.000€ brüt işlem hacmi
*   **İlk Yıl:** 100.000€ brüt işlem hacmi

## 11. SEO İyileştirme Planı (PRD'den)

### 11.1. Acil SEO İyileştirmeleri (MVP Öncesi/Sırası)
*   **Metadata Optimizasyonu:** Her sayfa için özelleştirilmiş `title` ve `description`. Open Graph ve Twitter Card etiketleri. Canonical URL'ler.
*   **Yapılandırılmış Veri (Schema.org):** `EducationalOrganization`, `Course`, `Person` (öğretmenler), `LocalBusiness` (varsa) schema markup.
*   **Çoklu Dil SEO:** `hreflang` etiketleri (tr, de), dil bazlı URL yapısı (örn: `/tr/...`, `/de/...`).
*   **Teknik SEO:** `robots.txt`, `sitemap.xml` (next-sitemap ile). Görsel optimizasyonu (`next/image`), `alt` etiketleri.

### 11.2. Orta Vadeli SEO İyileştirmeleri (MVP Sonrası 1-3 Ay)
*   **İçerik Optimizasyonu:** Blog entegrasyonu (varsa), düzenli içerik üretimi, anahtar kelime stratejisi, FAQ bölümü (schema ile).
*   **Performans Optimizasyonu:** Sayfa yükleme hızı (Core Web Vitals), görsel/video optimizasyonu, kod bölme, önbellekleme.
*   **Backlink Stratejisi:** Kaliteli backlink kazanımı, sosyal medya entegrasyonu, influencer işbirlikleri.

### 11.3. Uzun Vadeli SEO Stratejileri (3+ Ay)
*   **Yerel SEO:** Google My Business optimizasyonu, yerel dizinler, yerel backlinkler.
*   **Teknik SEO Geliştirmeleri:** AMP (opsiyonel), PWA özellikleri (opsiyonel).
*   **Kullanıcı Deneyimi ve SEO:** Kullanıcı davranış analizleri, dönüşüm oranı optimizasyonu (CRO), A/B testleri.

### 11.4. SEO Metrikleri ve Takip
*   **Temel Metrikler:** Organik trafik, anahtar kelime sıralamaları, CTR, oturum süresi.
*   **Analiz Araçları:** Google Search Console, Google Analytics 4, Ahrefs/SEMrush.
*   **Aylık SEO Denetimi:** Teknik, içerik, backlink, rakip analizi.

## 12. UI Tasarım Taslakları (PRD'den - Geliştirme için Referans)

#### Ana Sayfa
*   Header: Logo, menü (Ana Sayfa, Öğretmenler, Derslerim, Hakkımızda, İletişim), dil seçimi, giriş/kayıt butonları
*   Hero bölümü: Platformun tanıtımı, "Öğretmen Bul" ve "Öğretmen Ol" butonları
*   Öne çıkan öğretmenler: Yüksek puanlı 4-6 öğretmen kartı
*   Nasıl çalışır bölümü: 3-4 adımda platform kullanımı
*   Almanca öğrenme ipuçları: Blog yazılarından seçmeler
*   Footer: İletişim bilgileri, sosyal medya linkleri, kullanım şartları

#### Öğretmen Listesi Sayfası
*   Filtreleme bölümü: Fiyat aralığı, deneyim, uzmanlık alanı, değerlendirme
*   Öğretmen kartları: Profil fotoğrafı, isim, kısa tanıtım, fiyat, puan
*   Sıralama seçenekleri: Fiyat (artan/azalan), popülerlik, puan

#### Öğretmen Profil Sayfası
*   Profil özeti: Fotoğraf, isim, deneyim, fiyat, puan
*   Hakkında bölümü: Detaylı biyografi, eğitim, deneyim
*   Uzmanlık alanları: A1-C2 seviyeler, özel alanlar (iş Almancası, tıp Almancası vb.)
*   Müsaitlik takvimi: Haftanın günleri ve saatleri gösteren takvim (FullCalendar ile)
*   Değerlendirmeler: Önceki öğrencilerin yorumları ve puanlamaları
*   "Ders Planla" butonu

#### Öğrenci Dashboard (MVP Sonrası Geliştirilecek)
*   Yaklaşan dersler: Tarih, saat, öğretmen bilgisi
*   Geçmiş dersler: Tarih, saat, öğretmen, değerlendirme
*   Favori öğretmenler: Eklenen öğretmenler listesi
*   Mesajlar: Öğretmenlerle olan yazışmalar

#### Öğretmen Dashboard (MVP Sonrası Geliştirilecek)
*   Yaklaşan dersler: Tarih, saat, öğrenci bilgisi
*   Geçmiş dersler: Tarih, saat, öğrenci, alınan değerlendirmeler
*   Kazanç istatistikleri: Günlük/haftalık/aylık kazanç grafiği
*   Müsaitlik yönetimi: Takvim üzerinde müsait saatleri belirleme (FullCalendar ile)
*   Mesajlar: Öğrencilerle olan yazışmalar

#### Ders Odası (MVP)
*   Video alanı: Öğretmen ve öğrenci video görüntüsü (Zoom SDK)
*   Kontrol çubuğu: Mikrofon açma/kapama, kamera açma/kapama, ekran paylaşımı
*   Sohbet paneli: Yazılı mesajlaşma
*   Ders notları: Paylaşılan dokümanlar, notlar (Basit)

## 13. Açık Sorular ve Kararlar (PRD'den ve Eklenenler)

*   **Ödeme Yöntemleri:** MVP'de Stripe. MVP sonrası Paypal, iyzico eklenecek. Banka havalesi?
*   **Komisyon Oranları:** Başlangıçta %10-15 hedefi. Kademeli ve ayarlanabilir bir sistem kurulacak. Net oranlar belirlenecek.
*   **Dil Seviyesi Değerlendirmesi:** MVP'de yok. MVP sonrası eklenebilir (opsiyonel test veya öğretmen değerlendirmesi).
*   **Ölçeklenebilirlik:** Supabase ve Vercel'in ücretli planları incelenecek. Caching (Redis/Vercel Edge) MVP sonrası değerlendirilecek.
*   **GDPR Uyumluluğu:** Yasal metinler (Avukat) ve teknik implementasyon (Çerez onayı, veri yönetimi) gerekli.
*   **Çoklu Dil Desteği (i18n):** Kütüphane seçimi (next-intl?) ve kolay çeviri yönetimi sistemi kurulacak.
*   **Video SDK Seçimi:** Zoom SDK kararlaştırıldı.
*   **Takvim Kütüphanesi:** FullCalendar kararlaştırıldı.
*   **Prisma Migration Sorunu:** Çözüm bekleniyor.
*   **Clerk Rol Atama:** Clerk ayarı bekleniyor.
*   **Admin Panel Requirements**: Specific features and UI for the admin panel (teacher approval, platform settings) need definition.
*   **Notification System Details**: Specific triggers and channels (email, in-app) for notifications need planning (post-MVP).
*   **Data Migration from Blog**: Strategy for potentially migrating relevant content or users from the existing blog (if desired).