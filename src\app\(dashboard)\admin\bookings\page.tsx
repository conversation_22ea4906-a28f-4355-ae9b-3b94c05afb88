// src/app/(dashboard)/admin/bookings/page.tsx
import { DataTable } from "@/components/dashboard/DataTable";
import { columns } from "./_components/columns"; // Rezervasyon sütun tanımları

// TODO: Gerçek rezervasyon verilerini Prisma ve Server Action ile çek.
// Prisma'dan döne<PERSON>k Booking tipine benzer bir tip varsayalım.
// <PERSON><PERSON><PERSON><PERSON><PERSON> modeller<PERSON> (Student, Teacher) isim gibi ek bilgiler gerekebilir.
type BookingData = {
  id: string;
  studentId: string; // Veya studentName
  teacherId: string; // Veya teacherName
  lessonTime: Date;
  durationMinutes: number;
  status: string;
  pricePaid: number; // Prisma Decimal tipini number olarak varsayalım
  created_at: Date;
};

async function getBookings(): Promise<BookingData[]> {
  // Şimdilik örnek veriler döndürelim
  // Gerçek implementasyonda: await prisma.booking.findMany({ include: { student: true, teacher: true } }) veya ilgili action
  const now = new Date();
  return [
    { id: "booking-1", studentId: "student-1", teacherId: "teacher-1", lessonTime: new Date(now.getTime() + 2 * 60 * 60 * 1000), durationMinutes: 50, status: "CONFIRMED", pricePaid: 150, created_at: new Date(now.getTime() - 24 * 60 * 60 * 1000) },
    { id: "booking-2", studentId: "student-2", teacherId: "teacher-1", lessonTime: new Date(now.getTime() + 26 * 60 * 60 * 1000), durationMinutes: 50, status: "CONFIRMED", pricePaid: 150, created_at: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000) },
    { id: "booking-3", studentId: "student-1", teacherId: "teacher-2", lessonTime: new Date(now.getTime() + 48 * 60 * 60 * 1000), durationMinutes: 25, status: "PENDING_PAYMENT", pricePaid: 80, created_at: new Date(now.getTime() - 60 * 60 * 1000) },
  ];
}

export default async function AdminBookingsPage() {
  const bookings = await getBookings();

  return (
    <div className="flex flex-col gap-6">
       <div className="flex justify-between items-center">
         <h1 className="text-2xl font-semibold">Rezervasyon Yönetimi</h1>
         {/* TODO: Yeni rezervasyon ekleme butonu (admin için?) gerekli mi? */}
       </div>

      {/* Rezervasyon Veri Tablosu */}
      <DataTable
        columns={columns}
        data={bookings}
        searchColumnId="studentId" // Öğrenci ID veya email ile arama?
        searchPlaceholder="Öğrenci ID ile ara..." // Veya E-posta
       />
    </div>
  );
}