// src/types/filters.ts
export interface TeacherFilters {
  level: string[];
  price: [number, number];
  availability: string[];
  specialties: string[];
  country: string[];
  search: string;
  spokenLanguages?: string[]; // Eklendi: Konuşulan dillere göre filtre
  isNativeSpeaker?: boolean | null; // Eklendi: Ana dili konuşanları filtrele (null tümü anlamına gelebilir)
  sortBy?: 'relevance' | 'price_asc' | 'price_desc' | 'rating_desc' | 'newest'; // Eklendi: Sıralama ölçütü
}