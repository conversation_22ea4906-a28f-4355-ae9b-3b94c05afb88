"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { levelTestEmailSchema, LevelTestEmailFormValues } from "@/lib/schemas/level-test-email.schema"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Form, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { toast } from "sonner"

export default function LevelTestPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = React.useState(false)

  React.useEffect(() => {
    // Ana sayfadan gelen kullanıcı bilgilerini kontrol et
    const email = localStorage.getItem("userEmail")
    const name = localStorage.getItem("userName")

    // Eğer localStorage'da email varsa direkt teste yönlendir
    if (email) {
      router.push("/seviye-testi/test")
    }
  }, [])

  const form = useForm<LevelTestEmailFormValues>({
    resolver: zodResolver(levelTestEmailSchema),
    defaultValues: { email: "" },
  })

  const onSubmit = async (values: LevelTestEmailFormValues) => {
    setIsSubmitting(true)
    try {
      // E-posta bilgilerini localStorage'a kaydet
      localStorage.setItem("userEmail", values.email)

      // E-posta gönder
      const response = await fetch("/api/level-test-email", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      })
      const result = await response.json()
      
      if (result.success) {
        // Teste yönlendir
        router.push("/seviye-testi/test")
      } else {
        toast.error(result.error || "Bir hata oluştu. Lütfen tekrar deneyin.")
      }
    } catch (error) {
      toast.error("Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-12 max-w-md">
      <h1 className="text-3xl font-bold mb-6">Ücretsiz Almanca Seviye Tespit Sınavı</h1>
      <p className="mb-8">
        Almanca seviyenizi belirlemek için ücretsiz seviye tespit sınavımıza katılın. 
        Sınav sonucunda hangi seviyede olduğunuzu öğrenecek ve size özel ders planı önerileri alacaksınız.
      </p>
      <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-8">
        <p className="text-amber-800 text-sm">
          <strong>Nasıl Çalışır?</strong> E-posta adresinizi girin ve hemen teste başlayın. 
          Sınav yaklaşık 15-20 dakika sürer ve sonuçlar anında görüntülenir.
        </p>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4" noValidate>
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel htmlFor="email">E-posta Adresi</FormLabel>
                <Input
                  id="email"
                  type="email"
                  autoComplete="email"
                  placeholder="<EMAIL>"
                  {...field}
                  disabled={isSubmitting}
                  required
                  aria-required="true"
                />
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" disabled={isSubmitting} className="w-full bg-primary hover:bg-primary/90">
            {isSubmitting ? "Yönlendiriliyor..." : "Teste Başla"}
          </Button>
          <div className="text-center mt-4">
            <button 
              type="button" 
              onClick={() => router.push("/seviye-testi/test")}
              className="text-sm text-primary underline hover:text-primary/80"
            >
              E-posta girmeden hemen teste başla
            </button>
          </div>
        </form>
      </Form>
    </div>
  )
}