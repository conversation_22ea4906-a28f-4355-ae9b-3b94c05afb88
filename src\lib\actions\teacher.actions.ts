// src/lib/actions/teacher.actions.ts
"use server";

// Kaldırıldı: import { PrismaClient, Prisma } from "@prisma/client";
// Kaldırıldı: const prisma = new PrismaClient();
import { prisma } from '@/lib/prisma'; // Eklendi
import { Prisma } from "@prisma/client"; // Bu satır Prisma namespace'i için kalmalı
import { unstable_noStore as noStore, revalidatePath } from 'next/cache';

import { ClientEducation, ClientCertificate } from "@/types/teacher";

// Main data fetching function for the teacher profile page
export async function getTeacherProfileData(teacherId: string) {
  noStore();
  try {
    const teacher = await prisma.teacher.findUnique({
      where: { id: teacherId, is_approved: true, is_visible: true },
      include: {
        reviewsReceived: {
          orderBy: { createdAt: 'desc' },
          take: 20, // Fetch a decent number of reviews
          include: {
            student: true, // Öğ<PERSON>ci bilgilerini de dahil et
          },
        },
        lessonPackages: {
          where: { isActive: true },
          orderBy: { createdAt: 'asc' },
        },
        teacherFaqs: {
          orderBy: { createdAt: 'asc' },
        },
      },
    });

    if (!teacher) {
      return null;
    }

    // Process and combine data for the client
    const reviewCount = teacher.reviewsReceived.length;
    const totalLessons = await prisma.booking.count({
      where: { teacherId: teacher.id, status: 'COMPLETED' },
    });

    // JSON alanlarını güvenli bir şekilde işle
    const education: ClientEducation[] = teacher.education ? JSON.parse(JSON.stringify(teacher.education)) : [];
    const certificates: ClientCertificate[] = teacher.certificates ? JSON.parse(JSON.stringify(teacher.certificates)) : [];

    return {
      ...teacher,
      // Decimal alanları number'a çevir
      hourly_rate: teacher.hourly_rate ? new Prisma.Decimal(teacher.hourly_rate).toNumber() : null,
      average_rating: teacher.average_rating ? new Prisma.Decimal(teacher.average_rating).toNumber() : null,
      // İşlenmiş JSON verilerini ekle
      education,
      certificates,
      // Hesaplanan alanları ekle
      reviewCount,
      totalLessons,
      // Ders paketlerini tipe göre ayır
      oneOnOnePackages: teacher.lessonPackages.filter(p => p.type === 'ONE_ON_ONE'),
      groupCourses: teacher.lessonPackages.filter(p => p.type === 'GROUP'),
      videoCourses: teacher.lessonPackages.filter(p => p.type === 'VIDEO_COURSE'),
    };
  } catch (error) {
    console.error(`Failed to fetch teacher profile data for ID: ${teacherId}`, error);
    return null;
  }
}


// Admin-specific actions
export async function getTeachersForAdmin() {
  noStore();
  try {
    const teachers = await prisma.teacher.findMany({
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        is_approved: true,
        created_at: true,
      },
      orderBy: {
        created_at: 'desc',
      }
    });
    return teachers;
  } catch (error) {
    console.error("Failed to fetch teachers for admin:", error);
    return [];
  }
}

export async function getTeacherDetailsForAdmin(teacherId: string) {
  noStore();
  try {
    const teacher = await prisma.teacher.findUnique({
      where: { id: teacherId },
      include: {
        lessonPackages: true,
        teacherFaqs: true,
        reviewsReceived: true,
      },
    });

    if (!teacher) return null;
    
    // Process decimal and JSON fields for admin view
    return {
        ...teacher,
        hourly_rate: teacher.hourly_rate ? new Prisma.Decimal(teacher.hourly_rate).toNumber() : null,
        average_rating: teacher.average_rating ? new Prisma.Decimal(teacher.average_rating).toNumber() : null,
        education: teacher.education ? JSON.parse(JSON.stringify(teacher.education)) : null,
        certificates: teacher.certificates ? JSON.parse(JSON.stringify(teacher.certificates)) : null,
    };

  } catch (error) {
     console.error(`Failed to fetch teacher details for admin (ID: ${teacherId}):`, error);
     return null;
  }
}

export async function approveTeacher(teacherId: string) {
  noStore();
  try {
    const updatedTeacher = await prisma.teacher.update({
      where: { id: teacherId },
      data: { is_approved: true },
    });
    revalidatePath('/admin/teachers');
    return { success: true, message: "Öğretmen başarıyla onaylandı.", teacher: updatedTeacher };
  } catch (error) {
    console.error(`Failed to approve teacher (ID: ${teacherId}):`, error);
    return { success: false, message: "Öğretmen onaylanırken bir hata oluştu." };
  }
}

export async function deleteTeacher(teacherId: string) {
  noStore();
  try {
    await prisma.teacher.delete({
      where: { id: teacherId },
    });
    revalidatePath('/admin/teachers');
    return { success: true, message: "Öğretmen başarıyla silindi." };
  } catch (error) {
    console.error(`Failed to delete teacher (ID: ${teacherId}):`, error);
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2003') {
      return { success: false, message: "Öğretmen silinemedi. Öğretmene bağlı kayıtlar bulunmaktadır." };
    }
    return { success: false, message: "Öğretmen silinirken bir hata oluştu." };
  }
}

export async function rejectTeacherApplication(teacherId: string) {
  noStore();
  try {
    // Burada öğretmeni silmek yerine belki sadece durumunu 'rejected' olarak güncelleyebiliriz.
    // Şimdilik, admin panelindeki yoruma uyarak siliyoruz.
    await prisma.teacher.delete({
      where: { id: teacherId },
    });
    revalidatePath('/admin/teacher-applications');
    return { success: true, message: "Öğretmen başvurusu reddedildi ve silindi." };
  } catch (error) {
    console.error(`Failed to reject teacher application (ID: ${teacherId}):`, error);
    return { success: false, error: "Başvuru reddedilirken bir hata oluştu." };
  }
}

export async function getPendingTeacherApplications() {
    noStore();
    try {
        const pendingTeachers = await prisma.teacher.findMany({
            where: { is_approved: false },
            orderBy: { created_at: 'desc' },
        });
        return pendingTeachers;
    } catch (error) {
        console.error("Failed to fetch pending teacher applications:", error);
        return [];
    }
}

export async function updateTeacherApprovalStatus(teacherId: string, is_approved: boolean) {
    noStore();
    try {
        const updatedTeacher = await prisma.teacher.update({
            where: { id: teacherId },
            data: { is_approved: is_approved },
        });
        revalidatePath('/admin/teacher-applications');
        revalidatePath(`/admin/teachers/${teacherId}`);
        return { success: true, teacher: updatedTeacher };
    } catch (error) {
        console.error(`Failed to update teacher approval status for ID: ${teacherId}`, error);
        return { success: false, error: "Öğretmen onay durumu güncellenemedi." };
    }
}

export async function getTeacherBookingsForCalendar(teacherId: string, startDate: Date, endDate: Date) {
    noStore();
    try {
        const bookings = await prisma.booking.findMany({
            where: {
                teacherId: teacherId,
                lessonTime: {
                    gte: startDate,
                    lte: endDate,
                },
                status: {
                    in: ['CONFIRMED', 'COMPLETED']
                }
            },
            include: {
                student: true,
                // lessonPackage ilişkisi Booking modelinde yok, kaldırıldı.
            }
        });
        return bookings;
    } catch (error) {
        console.error(`Failed to fetch bookings for teacher calendar (ID: ${teacherId}):`, error);
        return [];
    }
}

export async function updateTeacher(teacherId: string, data: Prisma.TeacherUpdateInput) {
    try {
        const updatedTeacher = await prisma.teacher.update({
            where: { id: teacherId },
            data: data,
        });
        revalidatePath(`/(dashboard)/teacher/profile`);
        revalidatePath(`/teachers/${teacherId}`);
        return { success: true, teacher: updatedTeacher };
    } catch (error) {
        console.error(`Failed to update teacher (ID: ${teacherId}):`, error);
        return { success: false, error: "Öğretmen profili güncellenemedi." };
    }
}
// Other utility actions
export async function searchTeachers(searchTerm: string) {
  noStore();
  if (!searchTerm || searchTerm.trim() === "") {
    return [];
  }
  const trimmedSearchTerm = searchTerm.trim();
  try {
    const teachers = await prisma.teacher.findMany({
      where: {
        is_approved: true,
        is_visible: true,
        OR: [
          { name: { contains: trimmedSearchTerm, mode: 'insensitive' } },
          { title: { contains: trimmedSearchTerm, mode: 'insensitive' } },
          { bio: { contains: trimmedSearchTerm, mode: 'insensitive' } },
          { specializations: { has: trimmedSearchTerm } },
        ],
      },
      select: {
        id: true,
        name: true,
        avatar: true,
        title: true,
        hourly_rate: true,
        average_rating: true,
        country: true,
        _count: {
          select: { reviewsReceived: true },
        },
      },
      take: 20,
    });
    return teachers.map(teacher => ({
      ...teacher,
      hourly_rate: teacher.hourly_rate ? new Prisma.Decimal(teacher.hourly_rate).toNumber() : null,
      average_rating: teacher.average_rating ? new Prisma.Decimal(teacher.average_rating).toNumber() : null,
      reviews_count: teacher._count?.reviewsReceived || 0,
    }));
  } catch (error) {
    console.error("Failed to search teachers:", error);
    return [];
  }
}

// admin.actions.ts'ten taşındı ve diğer eksik fonksiyonlar eklendi.
export async function approveTeacherApplication(teacherId: string) {
    noStore();
    try {
        const updatedTeacher = await prisma.teacher.update({
            where: { id: teacherId },
            data: { is_approved: true },
        });
        
        // TODO: Aktivite loglama eklenebilir.
        
        revalidatePath('/admin/teacher-applications');
        revalidatePath('/admin/teachers');
        return { success: true, teacher: updatedTeacher };
    } catch (error) {
        console.error(`Failed to approve teacher application (ID: ${teacherId}):`, error);
        return { success: false, error: "Öğretmen başvurusu onaylanamadı." };
    }
}

export async function countPendingTeacherApplications() {
    noStore();
    try {
        const count = await prisma.teacher.count({
            where: { is_approved: false }
        });
        return count;
    } catch (error) {
        console.error("Failed to count pending teacher applications:", error);
        return 0;
    }
}

export async function getAllApprovedTeachers() {
    noStore();
    try {
        const teachers = await prisma.teacher.findMany({
            where: { is_approved: true },
            orderBy: { created_at: 'desc' },
        });
        return teachers;
    } catch (error) {
        console.error("Failed to fetch all approved teachers:", error);
        return [];
    }
}

export async function countAllApprovedTeachers() {
    noStore();
    try {
        const count = await prisma.teacher.count({
            where: { is_approved: true }
        });
        return count;
    } catch (error) {
        console.error("Failed to count all approved teachers:", error);
        return 0;
    }
}

export async function getTeacherActiveStudentCount(teacherId: string) {
    noStore();
    try {
        const teacher = await prisma.teacher.findUnique({
            where: { id: teacherId },
            select: { activeStudentCount: true }
        });
        return teacher?.activeStudentCount ?? 0;
    } catch (error) {
        console.error(`Failed to get active student count for teacher ${teacherId}:`, error);
        return 0;
    }
}

export async function getTeacherCompletedLessonsCount(teacherId: string) {
    noStore();
    try {
        const teacher = await prisma.teacher.findUnique({
            where: { id: teacherId },
            select: { completedLessons: true }
        });
        return teacher?.completedLessons ?? 0;
    } catch (error) {
        console.error(`Failed to get completed lessons count for teacher ${teacherId}:`, error);
        return 0;
    }
}

export async function getUpcomingLessonsForTeacher(teacherId: string, limit: number = 3) {
    noStore();
    try {
        const bookings = await prisma.booking.findMany({
            where: {
                teacherId: teacherId,
                status: 'CONFIRMED',
                lessonTime: {
                    gte: new Date(),
                }
            },
            orderBy: {
                lessonTime: 'asc',
            },
            take: limit,
            include: {
                student: {
                    select: {
                        firstName: true,
                        lastName: true,
                        profile_image_url: true,
                    }
                }
            }
        });
        return bookings;
    } catch (error) {
        console.error(`Failed to fetch upcoming lessons for teacher ${teacherId}:`, error);
        return [];
    }
}
