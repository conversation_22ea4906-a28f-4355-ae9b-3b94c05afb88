"use client"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Play, Pause, Volume2, VolumeX } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface TeacherIntroVideoProps {
  videoUrl?: string
}

// Helper function to extract YouTube video ID from various URL formats
function getYouTubeId(url: string): string | null {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
}

export function TeacherIntroVideo({ videoUrl }: TeacherIntroVideoProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(true)
  const [showOverlay, setShowOverlay] = useState(true)
  const videoRef = useRef<HTMLVideoElement>(null)

  // Check if it's a YouTube URL and get the ID
  const youTubeId = videoUrl ? getYouTubeId(videoUrl) : null;
  const isYouTube = !!youTubeId;

  const handlePlayPause = () => {
    // Only handle for direct video
    if (!isYouTube && videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
      setShowOverlay(false)
    }
  }

  const handleMuteToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Only handle for direct video
    if (!isYouTube && videoRef.current) {
      videoRef.current.muted = !isMuted
      setIsMuted(!isMuted)
    }
  }

  // useEffect logic is only for direct video
  useEffect(() => {
    if (isYouTube) return; // Skip effect for YouTube videos

    const handleEnded = () => {
      setIsPlaying(false)
      setShowOverlay(true)
      if (videoRef.current) {
         videoRef.current.currentTime = 0;
      }
    }

    const video = videoRef.current
    video?.addEventListener("ended", handleEnded)

    const handlePlay = () => setShowOverlay(false);
    const handlePause = () => {
        if (video && !video.ended) {
            setShowOverlay(false);
        }
    };

    video?.addEventListener("play", handlePlay);
    video?.addEventListener("pause", handlePause);

    return () => {
      video?.removeEventListener("ended", handleEnded)
      video?.removeEventListener("play", handlePlay);
      video?.removeEventListener("pause", handlePause);
    }
  }, [isYouTube]) // Add isYouTube dependency

  if (!videoUrl) {
    return null;
  }

  return (
    (<Card className="overflow-hidden">
      <CardContent className="p-0">
        <div className="relative aspect-video group">
          {isYouTube ? (
            // YouTube Embed
            (<iframe
              src={`https://www.youtube.com/embed/${youTubeId}?autoplay=0&mute=1&modestbranding=1&rel=0`} // autoplay=0, mute=1
              title="YouTube video player"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
              className="w-full h-full"
            ></iframe>)
          ) : (
            // Direct Video Player
            (<>
              <video
                ref={videoRef}
                src={videoUrl}
                className="w-full h-full object-cover"
                playsInline
                loop={false}
                muted={isMuted}
                onClick={handlePlayPause}
              />
              {/* Overlay */}
              <AnimatePresence>
                {showOverlay && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="absolute inset-0 bg-black/50 flex items-center justify-center Roo-pointer"
                    onClick={handlePlayPause}
                  >
                    <motion.div
                      initial={{ scale: 0.5 }}
                      animate={{ scale: 1 }}
                      exit={{ scale: 0.5 }}
                      className="text-center text-white pointer-events-none"
                    >
                       <div className="inline-flex items-center justify-center rounded-full h-16 w-16 bg-white/20 backdrop-blur-sm mb-2">
                         <Play className="w-8 h-8 ml-1" />
                       </div>
                      <p className="mt-2 text-lg font-medium">Tanıtım Videosunu İzle</p>
                    </motion.div>
                  </motion.div>
                )}
              </AnimatePresence>
              {/* Custom Controls */}
              <div className={`absolute bottom-2 right-2 flex gap-2 transition-opacity duration-300 ${isPlaying ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}>
                <Button
                  size="icon"
                  variant="ghost"
                  className="rounded-full bg-black/50 hover:bg-black/70 text-white h-8 w-8"
                  onClick={handleMuteToggle}
                  aria-label={isMuted ? "Sesi Aç" : "Sesi Kapat"}
                >
                  {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                </Button>
                <Button
                  size="icon"
                  variant="ghost"
                  className="rounded-full bg-black/50 hover:bg-black/70 text-white h-8 w-8"
                  onClick={handlePlayPause}
                  aria-label={isPlaying ? "Duraklat" : "Oynat"}
                  disabled={showOverlay}
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </Button>
              </div>
            </>)
          )}
        </div>
      </CardContent>
    </Card>)
  );
}