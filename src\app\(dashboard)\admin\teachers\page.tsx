// src/app/(dashboard)/admin/teachers/page.tsx
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/dashboard/DataTable";
import { columns } from "./_components/columns";
import { PlusCircle } from "lucide-react";
import Link from "next/link";
import { getTeachersForAdmin } from "@/lib/actions/teacher.actions"; // Server Action import edildi

// TODO: Gerçek öğretmen verilerini Prisma ve Server Action ile çek.
// TeacherData tipi, Server Action'dan dönen veriyle eşleşmeli
// Server Action'daki geçici email alanı burada da tanımlanmalı
type TeacherData = {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string; // Server Action'dan gelen geçici email
    is_approved: boolean;
    created_at: Date;
};

// Placeholder getTeachers fonksiyonu kaldırıldı.


export default async function AdminTeachersPage() { // Sayfa artık async
  // Server Action çağrılarak öğretmen verileri çekilir
  const teachers: TeacherData[] = await getTeachersForAdmin();

  return (
    (<div className="flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold">Öğretmen Yönetimi</h1>
        <Button asChild>
          <Link href="/admin/teachers/new"> {/* Yeni öğretmen ekleme sayfasına link */}
            <PlusCircle className="mr-2 h-4 w-4" /> Yeni Öğretmen Ekle
          </Link>
        </Button>
      </div>
      {/* Öğretmen Veri Tablosu */}
      <DataTable
        columns={columns}
        data={teachers}
        searchColumnId="email" // E-posta sütununda arama yap
        searchPlaceholder="E-posta ile ara..."
       />
    </div>)
  );
}