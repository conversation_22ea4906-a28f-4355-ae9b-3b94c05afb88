// prisma/seed.ts
import { PrismaClient, Prisma } from '@prisma/client';

const prisma = new PrismaClient();

const specificTeacherId = "26750095";

// Rastgele 8 basamaklı sayısal ID üreten fonksiyon
function generateRandomId(): string {
  let id;
  do {
    id = Math.floor(10000000 + Math.random() * 90000000).toString();
  } while (id === specificTeacherId); // Spesifik ID ile çakışmayı önle
  return id;
}

const drSophiaWeberData: Prisma.TeacherCreateInput = {
  id: specificTeacherId,
  firstName: 'Sophia',
  lastName: '<PERSON>',
  title: 'Sertifikalı Almanca Öğretmeni & Dil Bilimci',
  shortBio: "Münih Üniversitesi'nden doktoralı, 10 yılı aşkın deneyime sahip profesyonel Almanca öğretmeniyim. Öğrencilerime Almancayı sevdirerek öğretmek için kişiselleştirilmiş dersler sunuyorum.",
  aboutMe: [
    "Merhab<PERSON>! Ben Dr. <PERSON>. Münih Ludwig Maximilian Üniversitesi'nden Dil Bilimi alanında doktora derecesine sahibim ve 10 yılı aşkın süredir Almanca öğretmenliği yapıyorum.",
    "Berlin'de doğdum ve büyüdüm, ancak Türkiye'ye olan sevgim beni uzun yıllar İstanbul'da yaşamaya yönlendirdi. Bu süre içinde Türkçeyi öğrenerek kültürel bağlarımı güçlendirdim.",
    "Dil öğrenmenin sadece kelime ve dilbilgisi ezberlemekten ibaret olmadığına inanıyorum. Gerçek iletişim, kültürü anlamak ve günlük yaşamda dili aktif olarak kullanabilmekle mümkündür. Bu nedenle derslerimde öğrencilerimin günlük konuşma becerilerini geliştirmeye ve Alman kültürünü tanımalarına özel önem veriyorum.",
    "Çevrimiçi platformda ders vermeye 2015 yılında başladım ve bu süreçte 40'tan fazla ülkeden 500'ün üzerinde öğrenciye Almanca öğrettim. Her seviyede öğrenciye uygun, kişiselleştirilmiş ders planları hazırlıyorum. A1 seviyesinden C2 seviyesine kadar her aşamada destek sunabiliyorum."
  ],
  specializations: ['Günlük Konuşma Almancası', 'İş Almancası', 'Akademik Almanca', 'Yabancı Dil Olarak Almanca Öğretimi', 'TestDaF ve Goethe Sınavlarına Hazırlık', 'Çocuklar İçin Almanca', 'Almanca Edebiyatı', 'Teknik Almanca'],
  levels: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],
  hourly_rate: 250, // Örnek bir saatlik ücret
  profile_image_url: 'https://images.pexels.com/photos/5212345/pexels-photo-5212345.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1', // Görseldeki gibi bir URL veya placeholder
  intro_video_url: 'https://www.youtube.com/watch?v=hZIfMEl6x-E', // Örnek video
  country: 'DE', // Almanya
  city: 'Berlin',
  nativeLanguage: 'Almanca',
  spokenLanguages: ['Almanca', 'İngilizce', 'Türkçe'],
  is_approved: true,
  is_verified: true, // Mavi tik için
  is_visible: true,
  experience_years: 10,
  average_rating: 4.9,
  totalReviews: 128,
  activeStudentCount: 50, // Örnek veri
  completedLessons: 3240,
  newStudentsLast30Days: 45,
  satisfactionRate: 98,
  averageResponseTimeHours: 24,
  benefits: ["Kişiye özel ders planları", "Sertifikalı ve deneyimli eğitmen", "İnteraktif ve modern öğretim metotları", "Esnek ders saatleri", "Kanıtlanmış öğrenci başarısı"],
  teachingMethodology: [
    "Öğretim metodum, iletişimsel dil öğretimi yaklaşımını temel alır. Bu, öğrencilerin dili gerçek durumlarda kullanarak öğrenmesini sağlar. Her derste konuşma, dinleme, okuma ve yazma becerilerini dengeli bir şekilde geliştirmeye özen gösteriyorum.",
    "Derslerim her zaman öğrencinin ihtiyaçlarına ve hedeflerine göre şekillenir. Bir iş görüşmesine hazırlanan öğrenciye farklı, Almanya'da eğitim görmek isteyen bir öğrenciye farklı bir program uygularım. Herkesin öğrenme hızı ve stili farklıdır, bu yüzden ders planlarımı kişiselleştirmek benim için çok önemli.",
    "Öğrencilerimle genellikle ilk derste detaylı bir ihtiyaç analizi yaparım. Bu, onların güçlü ve zayıf yönlerini, hedeflerini ve ilgi alanlarını anlamama yardımcı olur. Sonrasında, bu bilgilere dayanarak bir öğrenme yolu çizeriz.",
    "Modern teknoloji ve çeşitli öğretim materyalleri kullanarak dersleri ilgi çekici hale getirmeye çalışırım. Video, ses kaydı, interaktif alıştırmalar ve güncel makaleler gibi çeşitli kaynakları derslerime entegre ederim."
  ],
  education: [
    { year: "2013", degree: "Doktora, Dil Bilimi", institution: "Ludwig Maximilian Üniversitesi", location: "Münih, Almanya" },
    { year: "2010", degree: "Yüksek Lisans, Dil Öğretimi ve Kültürlerarası İletişim", institution: "Humboldt Üniversitesi", location: "Berlin, Almanya" },
    { year: "2008", degree: "Lisans, Alman Dili ve Edebiyatı", institution: "Freie Üniversitesi", location: "Berlin, Almanya" }
  ],
  certificates: [
    { name: "DaF (Yabancı Dil Olarak Almanca Öğretimi) Sertifikası", issuer: "Goethe Enstitüsü", date: "2012" },
    { name: "Çevrimiçi Dil Öğretimi Uzmanlık Sertifikası", issuer: "Cambridge Üniversitesi", date: "2015" },
    { name: "TestDaF Sınav Hazırlık Eğitmenliği", issuer: "TestDaF Enstitüsü", date: "2014" },
    { name: "Kültürlerarası İletişim ve Dil Öğretimi", issuer: "DAAD", date: "2016" }
  ],
  // videoCourses ve offersGroupLessons alanları için örnek veriler eklenebilir.
};


// Teacher verileri (ID alanı olmadan)
const teacherBaseData: Omit<Prisma.TeacherCreateInput, 'id'>[] = [
  {
    firstName: 'Ayşe',
    lastName: 'Yılmaz',
    title: 'Deneyimli Almanca Öğretmeni',
    shortBio: '10 yıllık deneyimli, sabırlı ve öğrenci odaklı bir öğretmen.',
    aboutMe: ['10 yıllık deneyimli, sabırlı ve öğrenci odaklı bir öğretmenim. Her seviyeden öğrenciye özel ders planları hazırlıyorum.'],
    specializations: ['İş Almancası', 'Günlük Konuşma'],
    levels: ['A1', 'A2', 'B1', 'B2'],
    hourly_rate: 150,
    profile_image_url: 'https://placekitten.com/g/200/200',
    intro_video_url: 'https://www.youtube.com/watch?v=hZIfMEl6x-E',
    country: 'Türkiye',
    city: 'İstanbul',
    spokenLanguages: ['Türkçe', 'Almanca', 'İngilizce'],
    is_approved: true,
    is_verified: true,
    experience_years: 10,
    average_rating: 4.9,
    completedLessons: 127,
    is_visible: true,
  },
  {
    firstName: 'Ahmet',
    lastName: 'Kaya',
    title: 'Sınav Odaklı Almanca Uzmanı',
    shortBio: "TestDaF ve telc sınavlarında uzman. Hedef odaklı dersler.",
    aboutMe: ["Goethe Enstitüsü'nde 5 yıl çalıştım. TestDaF ve telc sınavlarında uzmanlığım var. Hedef odaklı derslerle başarıyı garantileyin."],
    specializations: ['Sınav Hazırlık (TestDaF, telc)', 'Akademik Almanca'],
    levels: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],
    hourly_rate: 180,
    profile_image_url: 'https://placekitten.com/g/201/201',
    country: 'Türkiye',
    city: 'Ankara',
    spokenLanguages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: true,
    experience_years: 7,
    average_rating: 4.8,
    completedLessons: 98,
    is_visible: true,
  },
  {
    firstName: 'Zeynep',
    lastName: 'Demir',
    title: 'Anadil Seviyesinde Konuşma Pratiği',
    shortBio: "Almanya'da doğup büyüdü. Doğal Almanca konuşma pratiği.",
    aboutMe: ["Almanya'da doğdum ve büyüdüm. Doğal bir Almanca konuşma pratiği sunuyorum. Günlük hayatta kullanılan ifadeler ve kültürel nüanslar üzerine odaklanıyorum."],
    specializations: ['Konuşma Pratiği', 'Günlük Almanca', 'Alman Kültürü'],
    levels: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],
    hourly_rate: 140,
    profile_image_url: 'https://placekitten.com/g/202/202',
    country: 'Almanya',
    city: 'Berlin',
    spokenLanguages: ['Almanca', 'Türkçe'],
    is_approved: true,
    is_verified: false,
    experience_years: 4,
    average_rating: 5.0,
    completedLessons: 56,
    is_visible: true,
  },
  {
    firstName: 'Can',
    lastName: 'Öztürk',
    title: 'Çocuklar İçin Eğlenceli Almanca',
    shortBio: 'Çocuklara ve gençlere Almanca öğretmede 8 yıl deneyimli.',
    aboutMe: ['Çocuklara ve gençlere Almanca öğretme konusunda 8 yıllık deneyime sahibim. Oyunlar ve interaktif aktivitelerle öğrenmeyi keyifli hale getiriyorum.'],
    specializations: ['Çocuklar için Almanca', 'Gençler için Almanca'],
    levels: ['A1', 'A2', 'B1', 'B2', 'C1'],
    hourly_rate: 160,
    profile_image_url: 'https://placekitten.com/g/203/203',
    country: 'Türkiye',
    city: 'İzmir',
    spokenLanguages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: true,
    experience_years: 8,
    average_rating: 4.7,
    completedLessons: 84,
    is_visible: true,
  },
  {
    firstName: 'Fatma',
    lastName: 'Şahin',
    title: 'Akademik Almanca ve YDS Uzmanı',
    shortBio: 'Akademik Almanca ve YDS hazırlığında uzman.',
    aboutMe: ['Akademik Almanca ve YDS hazırlığı konusunda uzmanım. Üniversite öğrencileri ve akademisyenlere yönelik özel programlar sunuyorum.'],
    specializations: ['Akademik Almanca', 'YDS Hazırlık', 'İleri Seviye Gramer'],
    levels: ['A1', 'A2', 'B1', 'B2', 'C1'],
    hourly_rate: 170,
    profile_image_url: 'https://placekitten.com/g/204/204',
    country: 'Türkiye',
    city: 'İstanbul',
    spokenLanguages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: true,
    experience_years: 9,
    average_rating: 4.9,
    completedLessons: 110,
    is_visible: true,
  },
  {
    firstName: 'Ali',
    lastName: 'Vural',
    title: 'Tıbbi ve Mesleki Almanca Eğitmeni',
    shortBio: 'Sağlık sektörü çalışanlarına özel tıbbi ve mesleki Almanca.',
    aboutMe: ['Sağlık sektöründe çalışanlar için özel Almanca dersleri veriyorum. Tıbbi terminoloji ve mesleki iletişim becerileri üzerine yoğunlaşıyorum.'],
    specializations: ['Tıbbi Almanca', 'Mesleki Almanca (Sağlık)'],
    levels: ['B1', 'B2', 'C1', 'C2'],
    hourly_rate: 190,
    profile_image_url: 'https://placekitten.com/g/205/205',
    country: 'Almanya',
    city: 'Münih',
    spokenLanguages: ['Türkçe', 'Almanca', 'İngilizce'],
    is_approved: true,
    is_verified: true,
    experience_years: 12,
    average_rating: 4.6,
    completedLessons: 75,
    is_visible: true,
  },
  {
    firstName: 'Elif',
    lastName: 'Aksoy',
    title: 'Çeviri Teknikleri ve İleri Dilbilgisi',
    shortBio: 'Almanca-Türkçe çeviri ve ileri dilbilgisi uzmanı.',
    aboutMe: ['Almanca-Türkçe çeviri ve dilbilgisi konularında yardımcı olabilirim. İleri seviye öğrenciler ve çevirmen adayları için ideal.'],
    specializations: ['Çeviri Teknikleri', 'İleri Gramer', 'Akademik Yazım'],
    levels: ['B2', 'C1', 'C2'],
    hourly_rate: 165,
    profile_image_url: 'https://placekitten.com/g/206/206',
    country: 'Türkiye',
    city: 'Bursa',
    spokenLanguages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: true,
    experience_years: 7,
    average_rating: 4.8,
    completedLessons: 92,
    is_visible: true,
  },
  {
    firstName: 'Mustafa',
    lastName: 'Yıldız',
    title: 'Günlük Konuşma ve Kültür Rehberi',
    shortBio: 'Almanya kültürü ve günlük yaşam üzerine konuşma pratiği.',
    aboutMe: ['Almanya kültürü ve günlük yaşam hakkında konuşarak pratik yapalım. Rahat bir ortamda Almanca konuşma becerilerinizi geliştirin.'],
    specializations: ['Günlük Konuşma', 'Alman Kültürü', 'Seyahat Almancası'],
    levels: ['A1', 'A2', 'B1', 'B2'],
    hourly_rate: 155,
    profile_image_url: 'https://placekitten.com/g/207/207',
    country: 'Almanya',
    city: 'Hamburg',
    spokenLanguages: ['Türkçe', 'Almanca'],
    is_approved: true,
    is_verified: false,
    experience_years: 6,
    average_rating: 4.7,
    completedLessons: 88,
    is_visible: true,
  },
];

async function main() {
  console.log(`Start seeding ...`);

  // Öncelikle Dr. Sophia Weber'i ekleyelim veya güncelleyelim
  try {
    const sophia = await prisma.teacher.upsert({
      where: { id: specificTeacherId },
      update: drSophiaWeberData,
      create: drSophiaWeberData,
    });
    console.log(`Upserted Dr. Sophia Weber with id: ${sophia.id}`);

    // Dr. Sophia Weber için örnek ders paketleri oluşturalım
    const packageTypes = ["ONE_ON_ONE", "GROUP", "VIDEO_COURSE"];
    for (const type of packageTypes) {
        let packageData: Prisma.LessonPackageCreateWithoutTeacherInput;
        if (type === "ONE_ON_ONE") {
            packageData = {
                type: type,
                name: "Birebir Özel Ders Paketi - 10 Ders",
                description: "Kişiye özel Almanca dersleri.",
                price: 2400, // 10 * 240
                lessonsInPackage: 10,
                lessonDurationMinutes: 50,
                features: ["Kişiye özel ders planı", "Esnek zamanlama"],
                level: "Tüm Seviyeler",
                isActive: true,
            };
        } else if (type === "GROUP") {
            packageData = {
                type: type,
                name: "A1 Seviye Grup Dersi - 8 Hafta",
                description: "Küçük gruplarla interaktif Almanca.",
                price: 1200,
                lessonsInPackage: 16, // Haftada 2 ders * 8 hafta
                lessonDurationMinutes: 60,
                studentCapacity: "3-5 kişilik",
                groupCourseStartDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1), // Gelecek ayın biri
                groupCourseSchedule: "Salı ve Perşembe 19:00-20:00",
                level: "A1",
                isActive: true,
            };
        } else { // VIDEO_COURSE
            packageData = {
                type: type,
                name: "Almanca Gramer Video Kursu (A1-B1)",
                description: "Kapsamlı Almanca gramer anlatımı ve alıştırmalar.",
                price: 499,
                videoCourseTotalLessons: 50,
                videoCourseTotalHours: 20,
                videoCourseMaterials: "Videolar, PDF notlar, Alıştırmalar",
                youtubePlaylistUrl: "https://www.youtube.com/playlist?list=PL... (örnek)",
                thumbnailUrl: "https://placekitten.com/g/300/200",
                isActive: true,
            };
        }
        await prisma.lessonPackage.create({
            data: {
                ...packageData,
                teacherId: specificTeacherId,
            }
        });
        console.log(`Created ${type} package for Dr. Sophia Weber`);
    }

    // Dr. Sophia Weber için örnek SSS oluşturalım
    const faqs = [
        { question: "Dersler hangi platform üzerinden yapılıyor?", answer: "Derslerimiz genellikle Zoom veya Google Meet üzerinden yapılmaktadır. Sizin için uygun olan platformu birlikte belirleyebiliriz." },
        { question: "Ders materyallerini sağlıyor musunuz?", answer: "Evet, ders seviyenize ve hedeflerinize uygun tüm dijital materyaller (kitaplar, çalışma yaprakları, interaktif alıştırmalar) tarafımca sağlanmaktadır." },
        { question: "Ders iptal politikası nedir?", answer: "Dersinizi en az 24 saat öncesinden haber vererek iptal edebilir veya erteleyebilirsiniz. Aksi takdirde ders yapılmış sayılacaktır." }
    ];
    for (const faq of faqs) {
        await prisma.teacherFAQ.create({
            data: {
                teacherId: specificTeacherId,
                question: faq.question,
                answer: faq.answer,
            }
        });
    }
    console.log(`Created FAQs for Dr. Sophia Weber`);


  } catch (e) {
    console.error("Error upserting Dr. Sophia Weber:", e);
  }


  // Diğer rastgele öğretmenleri ekleme
  for (const baseData of teacherBaseData) {
    let teacherId: string;
    let existingTeacher: Prisma.TeacherGetPayload<{ select: { id: true } }> | null;

    do {
      teacherId = generateRandomId();
      existingTeacher = await prisma.teacher.findUnique({ where: { id: teacherId }, select: { id: true } });
      if (existingTeacher) {
        console.warn(`ID ${teacherId} already exists (randomly generated). Generating a new one.`);
      }
    } while (existingTeacher);

    try {
      const teacher = await prisma.teacher.create({
        data: {
          id: teacherId,
          ...baseData,
        },
      });
      console.log(`Created random teacher with id: ${teacher.id}`);
    } catch (e) {
        console.error(`Error creating random teacher with base data ${baseData.firstName}:`, e)
    }
  }
  console.log(`Seeding finished.`);
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
