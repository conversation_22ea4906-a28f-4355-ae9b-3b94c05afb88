'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardFooter, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Clock, BookOpen, Star as StarIcon, Zap } from 'lucide-react'; // Users ve CheckCircle kaldırıldı

export interface CourseCardProps {
  id: number | string;
  level: string;
  title: string;
  description: string;
  price: number;
  discounted_price?: number;
  duration: string;
  // student_count kaldırıldı
  lesson_count?: string;
  rating?: number;
  color?: string; // Deprecated, use levelSpecificStyles
  icon_color?: string; // Deprecated, use levelSpecificStyles
  // features kaldırıldı
  popular?: boolean;
  url: string;
  image?: string;
}

export const CourseCard: React.FC<CourseCardProps> = ({
  id,
  level,
  title,
  description,
  price,
  discounted_price,
  duration,
  // student_count, // Kaldırıldı
  lesson_count,
  rating,
  // color prop'u artık doğrudan kullanılmayacak, levelSpecificStyles tercih edilecek
  // icon_color prop'u artık doğrudan kullanılmayacak, levelSpecificStyles tercih edilecek
  // features, // Kaldırıldı
  popular,
  url,
  image,
}) => {
  const formatPrice = (priceValue: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(priceValue);
  };

  const calculateDiscount = (original: number, discounted?: number) => {
    if (discounted === undefined || discounted >= original || original === 0) return 0;
    return Math.round(((original - discounted) / original) * 100);
  };

  const discountPercentage = calculateDiscount(price, discounted_price);
  const displayPrice = discounted_price !== undefined && discounted_price < price ? discounted_price : price;

  const levelSpecificStyles: { [key: string]: { cardBg: string; badgeBg: string; badgeText: string; titleText: string; iconText: string; border: string; buttonBg: string; buttonHoverBg: string; priceText: string; } } = {
    a1: { cardBg: 'bg-blue-50 dark:bg-blue-900/40', badgeBg: 'bg-blue-600 dark:bg-blue-500', badgeText: 'text-white', titleText: 'text-blue-800 dark:text-blue-200', iconText: 'text-blue-600 dark:text-blue-400', border: 'border-blue-500', buttonBg: 'bg-blue-600', buttonHoverBg: 'hover:bg-blue-700', priceText: 'text-blue-700 dark:text-blue-300' },
    a2: { cardBg: 'bg-green-50 dark:bg-green-900/40', badgeBg: 'bg-green-600 dark:bg-green-500', badgeText: 'text-white', titleText: 'text-green-800 dark:text-green-200', iconText: 'text-green-600 dark:text-green-400', border: 'border-green-500', buttonBg: 'bg-green-600', buttonHoverBg: 'hover:bg-green-700', priceText: 'text-green-700 dark:text-green-300' },
    b1: { cardBg: 'bg-amber-50 dark:bg-amber-900/40', badgeBg: 'bg-amber-500', badgeText: 'text-amber-900', titleText: 'text-amber-800 dark:text-amber-200', iconText: 'text-amber-600 dark:text-amber-400', border: 'border-amber-500', buttonBg: 'bg-amber-500', buttonHoverBg: 'hover:bg-amber-600', priceText: 'text-amber-700 dark:text-amber-300' },
    b2: { cardBg: 'bg-purple-50 dark:bg-purple-900/40', badgeBg: 'bg-purple-600 dark:bg-purple-500', badgeText: 'text-white', titleText: 'text-purple-800 dark:text-purple-200', iconText: 'text-purple-600 dark:text-purple-400', border: 'border-purple-500', buttonBg: 'bg-purple-600', buttonHoverBg: 'hover:bg-purple-700', priceText: 'text-purple-700 dark:text-purple-300' },
    c1: { cardBg: 'bg-red-50 dark:bg-red-900/40', badgeBg: 'bg-red-600 dark:bg-red-500', badgeText: 'text-white', titleText: 'text-red-800 dark:text-red-200', iconText: 'text-red-600 dark:text-red-400', border: 'border-red-500', buttonBg: 'bg-red-600', buttonHoverBg: 'hover:bg-red-700', priceText: 'text-red-700 dark:text-red-300' },
    c2: { cardBg: 'bg-indigo-50 dark:bg-indigo-900/40', badgeBg: 'bg-indigo-600 dark:bg-indigo-500', badgeText: 'text-white', titleText: 'text-indigo-800 dark:text-indigo-200', iconText: 'text-indigo-600 dark:text-indigo-400', border: 'border-indigo-500', buttonBg: 'bg-indigo-600', buttonHoverBg: 'hover:bg-indigo-700', priceText: 'text-indigo-700 dark:text-indigo-300' },
    yoğunlaştırılmış: { cardBg: 'bg-teal-50 dark:bg-teal-900/40', badgeBg: 'bg-teal-600 dark:bg-teal-500', badgeText: 'text-white', titleText: 'text-teal-800 dark:text-teal-200', iconText: 'text-teal-600 dark:text-teal-400', border: 'border-teal-500', buttonBg: 'bg-teal-600', buttonHoverBg: 'hover:bg-teal-700', priceText: 'text-teal-700 dark:text-teal-300' },
    'iş almancası': { cardBg: 'bg-orange-50 dark:bg-orange-900/40', badgeBg: 'bg-orange-600 dark:bg-orange-500', badgeText: 'text-white', titleText: 'text-orange-800 dark:text-orange-200', iconText: 'text-orange-600 dark:text-orange-400', border: 'border-orange-500', buttonBg: 'bg-orange-600', buttonHoverBg: 'hover:bg-orange-700', priceText: 'text-orange-700 dark:text-orange-300' },
    default: { cardBg: 'bg-gray-100 dark:bg-gray-800', badgeBg: 'bg-gray-500', badgeText: 'text-white', titleText: 'text-gray-800 dark:text-gray-100', iconText: 'text-gray-600 dark:text-gray-400', border: 'border-gray-500', buttonBg: 'bg-gray-600', buttonHoverBg: 'hover:bg-gray-700', priceText: 'text-gray-800 dark:text-gray-200' }
  };

  const currentStyle = levelSpecificStyles[level.toLowerCase()] || levelSpecificStyles.default;

  return (
    (<motion.div
      id={`course-card-${id.toString()}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="h-full w-full flex-shrink-0 snap-center md:max-w-sm" // Carousel için genişlik ayarı
    >
      <Card className={`flex flex-col h-full overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out transform hover:-translate-y-1 dark:border-gray-700/50 ${popular ? `border-2 ${currentStyle.border}` : `border dark:border-gray-700/50`} ${currentStyle.cardBg}`}>
        <div className="relative">
          {image ? (
            // eslint-disable-next-line @next/next/no-img-element
            (<img src={image} alt={title} className="object-cover w-full h-40 md:h-44 group-hover:scale-105 transition-transform duration-300" />)
          ) : (
            <div className={`w-full h-40 md:h-44 ${currentStyle.cardBg.replace('bg-opacity-20', '')} bg-opacity-30 flex items-center justify-center`}>
              <BookOpen className={`w-16 h-16 ${currentStyle.iconText} opacity-40`} />
            </div>
          )}
          {discountPercentage > 0 && (
            <div className="absolute top-0 right-0 bg-red-600 text-white px-3 py-1 text-xs font-bold rounded-bl-lg shadow-md z-10">
              %{discountPercentage} İNDİRİM
            </div>
          )}
           {popular && !discountPercentage && (
             <div className={`absolute top-0 right-0 ${currentStyle.badgeBg} ${currentStyle.badgeText} px-3 py-1 text-xs font-bold rounded-bl-lg shadow-md z-10 flex items-center`}>
                <StarIcon size={12} className="mr-1 fill-current" /> ÖNE ÇIKAN
            </div>
          )}
          <div className={`absolute top-3 left-3 text-xs px-2.5 py-1 font-semibold rounded-full ${currentStyle.badgeBg} ${currentStyle.badgeText} shadow-sm`}>
            {level}
          </div>
        </div>
        
        <div className={`p-5 ${currentStyle.cardBg.replace('bg-opacity-20', '')}`}>
          <CardTitle className={`text-xl font-extrabold ${currentStyle.titleText} mt-1 mb-1.5 line-clamp-2 leading-tight`}>
            {title}
          </CardTitle>
          {rating && (
            <div className="flex items-center mb-2.5">
              {[...Array(5)].map((_, i) => (
                <StarIcon
                  key={i}
                  size={17}
                  className={i < Math.floor(rating) ? "text-yellow-400 fill-yellow-400" : "text-gray-300 dark:text-gray-500"}
                />
              ))}
              <span className={`ml-1.5 text-xs font-medium ${currentStyle.titleText} opacity-80`}>{rating.toFixed(1)}/5.0</span>
            </div>
          )}
          <CardDescription className={`text-sm ${currentStyle.titleText} opacity-70 h-12 line-clamp-3 mb-2`}>
            {description}
          </CardDescription>
        </div>

        <CardContent className={`p-5 flex-grow space-y-2 bg-white dark:bg-slate-800`}>
          {(duration || lesson_count) && (
            <div className="flex items-center text-sm text-gray-700 dark:text-gray-300 space-x-4">
              {duration && (
                <div className="flex items-center">
                  <Clock size={15} className={`${currentStyle.iconText} mr-1.5 flex-shrink-0`} />
                  <span>{duration}</span>
                </div>
              )}
              {lesson_count && (
                <div className="flex items-center">
                  <BookOpen size={15} className={`${currentStyle.iconText} mr-1.5 flex-shrink-0`} />
                  <span>{lesson_count}</span>
                </div>
              )}
            </div>
          )}
        </CardContent>

        <CardFooter className={`p-5 bg-gray-50 dark:bg-slate-800/70 border-t dark:border-gray-700 mt-auto`}>
          <div className="w-full">
            <div className="flex items-center justify-between mb-4">
              <div>
                {discounted_price !== undefined && discounted_price < price && (
                  <span className="text-gray-500 dark:text-gray-400 line-through text-base mr-2">
                    {formatPrice(price)}
                  </span>
                )}
                <p className={`text-3xl font-bold ${currentStyle.priceText}`}>
                  {formatPrice(displayPrice)}
                </p>
              </div>
            </div>
            <Button asChild size="lg" className={`w-full font-semibold text-base ${currentStyle.buttonBg} ${currentStyle.badgeText} ${currentStyle.buttonHoverBg} transition-colors shadow-md hover:shadow-lg`}>
              <Link href={url}>
                Kayıt Ol <Zap size={16} className="ml-2" />
              </Link>
            </Button>
             <Button asChild variant="ghost" className="w-full text-sm mt-2 text-gray-600 dark:text-gray-300 hover:text-current">
              <Link href={url}>
                Detaylı Bilgi
              </Link>
            </Button>
          </div>
        </CardFooter>
      </Card>
    </motion.div>)
  );
};