'use client';

import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Star } from 'lucide-react';

interface TeacherAvatarProps {
  src?: string;
  name: string;
  isVerified?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showBadge?: boolean;
  rating?: number;
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-12 h-12',
  lg: 'w-16 h-16',
  xl: 'w-24 h-24'
};

export const TeacherAvatar: React.FC<TeacherAvatarProps> = ({
  src,
  name,
  isVerified = false,
  size = 'md',
  showBadge = true,
  rating
}) => {
  const initials = name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <div className="relative inline-block">
      <Avatar className={`${sizeClasses[size]} border-2 border-white shadow-lg`}>
        <AvatarImage src={src} alt={name} />
        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
          {initials}
        </AvatarFallback>
      </Avatar>
      
      {isVerified && showBadge && (
        <div className="absolute -bottom-1 -right-1">
          <Badge className="bg-green-500 hover:bg-green-600 p-1 rounded-full">
            <CheckCircle className="w-3 h-3 text-white" />
          </Badge>
        </div>
      )}
      
      {rating && rating > 0 && (
        <div className="absolute -top-1 -right-1">
          <Badge className="bg-yellow-500 hover:bg-yellow-600 p-1 rounded-full flex items-center gap-1">
            <Star className="w-3 h-3 text-white fill-white" />
            <span className="text-xs text-white font-bold">{rating.toFixed(1)}</span>
          </Badge>
        </div>
      )}
    </div>
  );
};