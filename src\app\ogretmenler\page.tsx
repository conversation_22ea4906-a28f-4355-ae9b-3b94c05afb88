"use client"

import React, { useState, useEffect, useMemo, useCallback, useRef } from "react"
import Link from "next/link";
import { useRouter, useSearchParams } from 'next/navigation'
import { motion, AnimatePresence } from "framer-motion"
import Image from "next/image";
import { TeacherCard } from "@/components/TeacherCard"
import { FilterBar } from "@/components/FilterBar"
import type { Teacher } from "@/types/teacher"
import type { TeacherFilters } from "@/types/filters"
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog";
import { ModernContactForm } from "@/app/iletisim/page";
import { IletisimBilgileriKarti } from "@/components/IletisimBilgileriKarti";
import { SearchX, Target, Users, Award, CircleDollarSign, Star, ChevronLeft, ChevronRight, MessageSquarePlus } from "lucide-react"
import { cn } from "@/lib/utils";
import Pagination from "@/components/Pagination";
import { ITEM_PER_PAGE } from "@/lib/constants";
import FaqAccordion from "@/components/FaqAccordion";
import { ogretmenlerFaqData, FaqItem } from '@/lib/data/faq-data'; // Merkezi SSS verisini import ediyoruz
import Head from 'next/head'; // Head bileşenini import ediyoruz

// Mock teacher data
const mockTeachers: Teacher[] = [
  {
    id: 1,
    name: "Frau Schmidt",
    avatar: "https://randomuser.me/api/portraits/women/1.jpg",
    rating: 4.9,
    reviewCount: 120,
    specializations: ["A1-C2", "TestDaF", "İş Almancası"],
    price: 120,
    badges: ["Native Speaker", "10+ Yıl Deneyim"],
    levels: ["A1", "A2", "B1", "B2", "C1", "C2"],
    country: "DE",
    availability: ["Sabah", "Öğleden Sonra"],
    languages: ["Almanca", "İngilizce", "Türkçe"],
    is_verified: true,
    activeStudents: 25,
    totalLessons: 1500,
    isSuperTeacher: true,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "İngilizce", level: "C1" },
      { language: "Türkçe", level: "B2" },
    ],
    isOnline: true,
  },
  {
    id: 2,
    name: "Herr Müller",
    avatar: "https://randomuser.me/api/portraits/men/1.jpg",
    rating: 4.8,
    reviewCount: 98,
    specializations: ["İş Almancası", "Konuşma Pratiği", "A1-B2"],
    price: 110,
    badges: ["TELC Sertifikalı", "5+ Yıl Deneyim"],
    levels: ["A1", "A2", "B1", "B2"],
    country: "AT",
    availability: ["Akşam", "Gece"],
    languages: ["Almanca", "İngilizce"],
    is_verified: false,
    activeStudents: 18,
    totalLessons: 800,
    isSuperTeacher: false,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "İngilizce", level: "B2" },
    ],
    isOnline: false,
  },
  {
    id: 3,
    name: "Frau Weber",
    avatar: "https://randomuser.me/api/portraits/women/2.jpg",
    rating: 5.0,
    reviewCount: 75,
    specializations: ["Goethe Sınavları", "Akademik Almanca", "C1-C2"],
    price: 130,
    badges: ["Goethe Sertifikalı", "Akademisyen"],
    levels: ["C1", "C2"],
    country: "DE",
    availability: ["Sabah", "Öğleden Sonra", "Akşam"],
    languages: ["Almanca", "Türkçe"],
    is_verified: true,
    activeStudents: 30,
    totalLessons: 2200,
    isSuperTeacher: true,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "Türkçe", level: "C2" },
    ],
    isOnline: true,
  },
  {
    id: 4,
    name: "Herr Klein",
    avatar: "https://randomuser.me/api/portraits/men/2.jpg",
    rating: 4.7,
    reviewCount: 89,
    specializations: ["Çocuklar için Almanca", "A1-B2", "Oyunlarla Öğrenme"],
    price: 100,
    badges: ["Pedagojik Formasyon", "Çocuk Eğitimi Uzmanı"],
    levels: ["A1", "A2", "B1", "B2"],
    country: "CH",
    availability: ["Öğleden Sonra", "Akşam"],
    languages: ["Almanca", "İngilizce", "Fransızca"],
    is_verified: false,
    activeStudents: 15,
    totalLessons: 500,
    isSuperTeacher: false,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "İngilizce", level: "B1" },
      { language: "Fransızca", level: "A2" },
    ],
  },
  {
    id: 5,
    name: "Frau Wagner",
    avatar: "https://randomuser.me/api/portraits/women/3.jpg",
    rating: 4.9,
    reviewCount: 150,
    specializations: ["Sınav Hazırlık", "Akademik Almanca", "TestDaF"],
    price: 140,
    badges: ["TestDaF Uzmanı", "Doktora Derecesi"],
    levels: ["B2", "C1", "C2"],
    country: "DE",
    availability: ["Sabah", "Akşam"],
    languages: ["Almanca", "İngilizce"],
    is_verified: true,
    activeStudents: 40,
    totalLessons: 3000,
    isSuperTeacher: true,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "İngilizce", level: "C2" },
    ],
    isOnline: true,
  },
  {
    id: 6,
    name: "Herr Fischer",
    avatar: "https://randomuser.me/api/portraits/men/3.jpg",
    rating: 4.6,
    reviewCount: 67,
    specializations: ["Konuşma Pratiği", "Günlük Almanca", "A1-B1"],
    price: 90,
    badges: ["Konuşma Uzmanı"],
    levels: ["A1", "A2", "B1"],
    country: "AT",
    availability: ["Öğleden Sonra", "Gece"],
    languages: ["Almanca", "Türkçe"],
    is_verified: false,
    activeStudents: 12,
    totalLessons: 300,
    isSuperTeacher: false,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "Türkçe", level: "B1" },
    ],
  },
  {
    id: 7,
    name: "Frau Koch",
    avatar: "https://randomuser.me/api/portraits/women/4.jpg",
    rating: 4.8,
    reviewCount: 92,
    specializations: ["İş Almancası", "B1-C2", "Sunum Teknikleri"],
    price: 125,
    badges: ["İş Almancası Uzmanı", "Native Speaker"],
    levels: ["B1", "B2", "C1", "C2"],
    country: "CH",
    availability: ["Sabah", "Öğleden Sonra", "Akşam"],
    languages: ["Almanca", "İngilizce", "İtalyanca"],
    is_verified: true,
    activeStudents: 22,
    totalLessons: 1200,
    isSuperTeacher: true,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "İngilizce", level: "C1" },
      { language: "İtalyanca", level: "B2" },
    ],
    isOnline: true,
  },
  {
    id: 8,
    name: "Herr Bauer",
    avatar: "https://randomuser.me/api/portraits/men/4.jpg",
    rating: 4.7,
    reviewCount: 83,
    specializations: ["Teknik Almanca", "Mühendislik Almancası", "B2-C2"],
    price: 135,
    badges: ["Teknik Terminoloji Uzmanı", "Mühendis"],
    levels: ["B2", "C1", "C2"],
    country: "DE",
    availability: ["Akşam", "Hafta Sonu"],
    languages: ["Almanca", "İngilizce"],
    is_verified: false,
    activeStudents: 10,
    totalLessons: 450,
    isSuperTeacher: false,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "İngilizce", level: "C1" },
    ],
  },
  {
    id: 9,
    name: "Frau Hoffmann",
    avatar: "https://randomuser.me/api/portraits/women/5.jpg",
    rating: 4.9,
    reviewCount: 110,
    specializations: ["Edebiyat", "Yazma Becerileri", "C1-C2"],
    price: 120,
    badges: ["Yazar", "Edebiyat Uzmanı"],
    levels: ["C1", "C2"],
    country: "AT",
    availability: ["Sabah", "Akşam"],
    languages: ["Almanca", "İngilizce", "Fransızca"],
    is_verified: true,
    activeStudents: 19,
    totalLessons: 900,
    isSuperTeacher: true,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "İngilizce", level: "C2" },
      { language: "Fransızca", level: "B2" },
    ],
    isOnline: true,
  },
  {
    id: 10,
    name: "Herr Schulz",
    avatar: "https://randomuser.me/api/portraits/men/5.jpg",
    rating: 4.8,
    reviewCount: 95,
    specializations: ["Tıp Almancası", "Akademik Yazma", "B2-C2"],
    price: 150,
    badges: ["Tıp Doktoru", "Akademik Almanca Uzmanı"],
    levels: ["B2", "C1", "C2"],
    country: "CH",
    availability: ["Öğleden Sonra", "Akşam", "Hafta Sonu"],
    languages: ["Almanca", "İngilizce", "Latince"],
    is_verified: false,
    activeStudents: 28,
    totalLessons: 1800,
    isSuperTeacher: false,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "İngilizce", level: "C2" },
      { language: "Latince", level: "B1" },
    ],
  },
];

// AI önerilen öğretmenler
const AIRecommendations: Teacher[] = [
  {
    id: 101,
    name: "Frau Becker",
    avatar: "https://randomuser.me/api/portraits/women/6.jpg",
    rating: 5.0,
    reviewCount: 180,
    specializations: ["A1-C2", "TestDaF", "Goethe"],
    price: 150,
    badges: ["AI Önerisi", "Premium Öğretmen", "Native Speaker"],
    levels: ["A1", "A2", "B1", "B2", "C1", "C2"],
    country: "DE",
    availability: ["Sabah", "Öğleden Sonra", "Akşam"],
    languages: ["Almanca", "İngilizce", "Türkçe"],
    is_verified: true,
    activeStudents: 50,
    totalLessons: 4000,
    isSuperTeacher: true,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "İngilizce", level: "C2" },
      { language: "Türkçe", level: "C1" },
    ],
    isOnline: true,
  },
  {
    id: 102,
    name: "Herr Schneider",
    avatar: "https://randomuser.me/api/portraits/men/6.jpg",
    rating: 4.9,
    reviewCount: 150,
    specializations: ["İş Almancası", "Akademik Almanca"],
    price: 140,
    badges: ["AI Önerisi", "İş Almancası Uzmanı"],
    levels: ["B1", "B2", "C1", "C2"],
    country: "AT",
    availability: ["Öğleden Sonra", "Akşam"],
    languages: ["Almanca", "İngilizce"],
    is_verified: false,
    activeStudents: 35,
    totalLessons: 2500,
    isSuperTeacher: true,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "İngilizce", level: "C1" },
    ],
  },
  {
    id: 103,
    name: "Frau Meyer",
    avatar: "https://randomuser.me/api/portraits/women/7.jpg",
    rating: 4.9,
    reviewCount: 130,
    specializations: ["Sınav Hazırlık", "Konuşma Pratiği"],
    price: 135,
    badges: ["AI Önerisi", "Sınav Uzmanı"],
    levels: ["A2", "B1", "B2", "C1"],
    country: "CH",
    availability: ["Sabah", "Akşam"],
    languages: ["Almanca", "Fransızca"],
    is_verified: true,
    activeStudents: 28,
    totalLessons: 1800,
    isSuperTeacher: true,
    spokenLanguages: [
      { language: "Almanca", level: "Ana Dil" },
      { language: "Fransızca", level: "C1" },
    ],
    isOnline: true,
  },
];

const ogrenciYorumlari = [
  {
    id: 1,
    name: "Ayşe K.",
    role: "Üniversite Öğrencisi",
    image: "https://randomuser.me/api/portraits/women/11.jpg",
    rating: 5,
    text: "AlmancaABC sayesinde Almanca korkumu yendim! Öğretmenim çok sabırlıydı ve dersler çok keyifliydi. Kısa sürede konuşmaya başladım.",
    dersAldigiOgretmen: "Frau Schmidt",
  },
  {
    id: 2,
    name: "Mehmet T.",
    role: "Mühendis",
    image: "https://randomuser.me/api/portraits/men/22.jpg",
    rating: 5,
    text: "İş için Almanca öğrenmem gerekiyordu. AlmancaABC'deki öğretmenim sayesinde hem dilbilgimi geliştirdim hem de iş terminolojisine hakim oldum. Kesinlikle tavsiye ederim!",
    dersAldigiOgretmen: "Herr Müller",
  },
  {
    id: 3,
    name: "Zeynep A.",
    role: "Doktor",
    image: "https://randomuser.me/api/portraits/women/33.jpg",
    rating: 4,
    text: "Almanya&apos;da çalışmak için Goethe sınavına hazırlanıyordum. AlmancaABC'nin sınav odaklı dersleri ve pratik testleri çok yardımcı oldu.",
    dersAldigiOgretmen: "Frau Weber",
  },
  {
    id: 4,
    name: "Ali V.",
    role: "Turizmci",
    image: "https://randomuser.me/api/portraits/men/44.jpg",
    rating: 5,
    text: "Almanca konuşulan ülkelerden gelen turistlerle daha iyi iletişim kurmak için ders aldım. Öğretmenimin kültürel bilgileri de çok değerliydi.",
    dersAldigiOgretmen: "Herr Klein",
  },
  {
    id: 5,
    name: "Fatma E.",
    role: "Ev Hanımı",
    image: "https://randomuser.me/api/portraits/women/55.jpg",
    rating: 5,
    text: "Çocuklarımın eğitimi için Almanya'ya taşınacağız. AlmancaABC ile günlük konuşma pratiği yaparak kendime güvenim geldi.",
    dersAldigiOgretmen: "Frau Wagner",
  },
  {
    id: 6,
    name: "Mustafa C.",
    role: "Öğrenci (Lise)",
    image: "https://randomuser.me/api/portraits/men/66.jpg",
    rating: 4,
    text: "Okuldaki Almanca derslerime takviye olarak başladım ve notlarım yükseldi. Dersler hiç sıkıcı değil!",
    dersAldigiOgretmen: "Herr Fischer",
  },
  {
    id: 7,
    name: "Selin B.",
    role: "Grafik Tasarımcı",
    image: "https://randomuser.me/api/portraits/women/77.jpg",
    rating: 5,
    text: "Yaratıcı projeler için Almanca kaynakları anlamak istiyordum. AlmancaABC'deki öğretmenimle çok spesifik konulara odaklanabildik.",
    dersAldigiOgretmen: "Frau Koch",
  },
  {
    id: 8,
    name: "Kemal S.",
    role: "Emekli",
    image: "https://randomuser.me/api/portraits/men/88.jpg",
    rating: 5,
    text: "Yeni bir hobi olarak Almanca öğrenmeye karar verdim. AlmancaABC'deki dersler sayesinde hem yeni bir dil öğreniyorum hem de zihnimi aktif tutuyorum.",
    dersAldigiOgretmen: "Herr Bauer",
  },
  {
    id: 9,
    name: "Gizem D.",
    role: "Avukat",
    image: "https://randomuser.me/api/portraits/women/99.jpg",
    rating: 4,
    text: "Hukuk alanında Almanca terminolojiye ihtiyacım vardı. AlmancaABC'de bu konuda uzman bir öğretmen bulabildiğim için çok şanslıyım.",
    dersAldigiOgretmen: "Frau Hoffmann",
  },
  {
    id: 10,
    name: "Orhan G.",
    role: "Girişimci",
    image: "https://randomuser.me/api/portraits/men/10.jpg",
    rating: 5,
    text: "Almanya pazarına açılmayı planlıyorum. AlmancaABC'deki iş Almancası dersleri sayesinde kendimi çok daha hazır hissediyorum.",
    dersAldigiOgretmen: "Herr Schulz",
  }
];

// Bu blok kaldırılacak çünkü veriyi merkezi dosyadan alacağız

export default function TeachersPage() {
  return (
    // TeachersPageContent bileşenine SSS verisini ve başlıklarını prop olarak geçirebiliriz
    // veya TeachersPageContent içinde import edebiliriz. Şimdilik içeride import edelim.
    (<div className="flex-grow">
      <TeachersPageContent />
    </div>)
  );
}

// FAQPage için JSON-LD verisi oluşturan fonksiyon
const generateFaqPageSchema = (faqItems: FaqItem[]) => {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqItems.map(item => ({
      "@type": "Question",
      "name": item.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": item.answer
      }
    }))
  };
};

// TeacherList için JSON-LD verisi oluşturan fonksiyon
const generateTeacherListSchema = (teachers: Teacher[], pageUrl: string) => {
  const itemListElement = teachers.map((teacher, index) => ({
    "@type": "ListItem",
    "position": index + 1,
    "item": {
      "@type": "Person",
      "name": teacher.name,
      "image": teacher.avatar,
      "description": teacher.specializations?.join(", ") || `Almanca öğretmeni ${teacher.name}`,
      "url": `https://almancaabc.com/ogretmenler/${teacher.id}`, // Canlı domain ile güncellenmeli
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": `https://almancaabc.com/ogretmenler/${teacher.id}` // Canlı domain ile güncellenmeli
      },
      "jobTitle": "Almanca Öğretmeni",
      ...(teacher.spokenLanguages && teacher.spokenLanguages.length > 0 && {
        "knowsLanguage": teacher.spokenLanguages.map((lang: { language: string; level: string }) => lang.language).join(", ")
      }),
      ...(teacher.price && { "priceRange": `${teacher.price} TRY` }),
      ...(teacher.rating && teacher.reviewCount && {
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": teacher.rating.toString(),
          "reviewCount": teacher.reviewCount.toString()
        }
      }),
      // TODO: Gelecekte eklenebilir: "nationality": teacher.country,
      // TODO: Gelecekte eklenebilir: "alumniOf": teacher.university (eğer varsa)
    }
  }));

  return {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": "Online Almanca Öğretmenleri - AlmancaABC",
    "description": "AlmancaABC platformunda online özel ders veren deneyimli ve uzman Almanca öğretmenlerini keşfedin. Seviyenize ve hedeflerinize uygun öğretmeni bulun.",
    "url": pageUrl,
    "numberOfItems": teachers.length,
    "itemListElement": itemListElement
  };
};

function TeachersPageContent() {
  // Öğretmenler SSS verisini ve Schema'yı burada kullanacağız
  const faqItemsToDisplay = ogretmenlerFaqData;
  const faqPageSchema = generateFaqPageSchema(faqItemsToDisplay);

  const router = useRouter();
  const testimonialsScrollRef = useRef<HTMLDivElement>(null);
  const [showTestimonialLeftArrow, setShowTestimonialLeftArrow] = useState(false);
  const [showTestimonialRightArrow, setShowTestimonialRightArrow] = useState(true);

  const handleTestimonialScroll = useCallback(() => {
    const container = testimonialsScrollRef.current;
    if (!container) return;
    setShowTestimonialLeftArrow(container.scrollLeft > 20);
    const isAtEnd = container.scrollLeft + container.clientWidth >= container.scrollWidth - 20;
    setShowTestimonialRightArrow(!isAtEnd);
  }, []);

  const scrollTestimonials = useCallback((direction: 'left' | 'right') => {
    const container = testimonialsScrollRef.current;
    if (!container) return;
    const scrollAmount = 300;
    container.scrollTo({
      left: direction === 'left' ? container.scrollLeft - scrollAmount : container.scrollLeft + scrollAmount,
      behavior: 'smooth',
    });
  }, []);

  useEffect(() => {
    const container = testimonialsScrollRef.current;
    if (container) {
      container.addEventListener('scroll', handleTestimonialScroll);
      handleTestimonialScroll(); // Initial check
      return () => {
        if (container) {
            container.removeEventListener('scroll', handleTestimonialScroll);
        }
      };
    }
  }, [handleTestimonialScroll]);

  const searchParams = useSearchParams();
  const currentPage = parseInt(searchParams.get('sayfa') || '1', 10);
  const teachersPerPage = ITEM_PER_PAGE;

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [filters, setFilters] = useState<TeacherFilters>({
    level: [] as string[],
    price: [0, 500] as [number, number],
    availability: [],
    specialties: [],
    country: [],
    search: "",
  })

  const filterTeachers = useCallback((teachersArray: Teacher[]) => {
    if (!Array.isArray(teachersArray)) {
      console.error("teachersArray is not an array:", teachersArray)
      return []
    }
    return teachersArray.filter((teacher) => {
      const searchMatch =
        !filters.search ||
        (teacher.name && teacher.name.toLowerCase().includes(filters.search.toLowerCase())) ||
        (teacher.specializations && teacher.specializations.some((s: string) => s.toLowerCase().includes(filters.search.toLowerCase()))) ||
        (teacher.badges && teacher.badges.some((b: string) => b.toLowerCase().includes(filters.search.toLowerCase()))) ||
        (teacher.languages && teacher.languages.some((l: string) => l.toLowerCase().includes(filters.search.toLowerCase())))
      const levelMatch = filters.level.length === 0 || (!!teacher.levels && filters.level.some((level) => teacher.levels!.includes(level)))
      const priceMatch = typeof teacher.price === 'number' && teacher.price >= Number(filters.price[0]) && teacher.price <= Number(filters.price[1]);
      const availabilityMatch =
        filters.availability.length === 0 || (!!teacher.availability && filters.availability.some((a: string) => teacher.availability!.includes(a)))
      const specialtiesMatch =
        filters.specialties.length === 0 ||
        (!!teacher.specializations && filters.specialties.some((s: string) =>
          teacher.specializations!.some((teacherSpecialty: string) => teacherSpecialty.toLowerCase().includes(s.toLowerCase())),
        ))
      const countryMatch = filters.country.length === 0 || (teacher.country && filters.country.includes(teacher.country))
      return searchMatch && levelMatch && priceMatch && availabilityMatch && specialtiesMatch && countryMatch
    })
  }, [filters])

  const filteredTeachers = useMemo(() => filterTeachers(mockTeachers), [filterTeachers])
  const filteredAIRecommendations = useMemo(() => filterTeachers(AIRecommendations), [filterTeachers])

  // Schema.org için tüm öğretmenleri birleştir
  const allTeachersForSchema = useMemo(() => {
    // Her iki listeden de benzersiz öğretmenleri almak için bir Set kullanalım
    const allTeachersMap = new Map<number, Teacher>();
    filteredAIRecommendations.forEach(teacher => {
      const id = Number(teacher.id);
      if (!isNaN(id)) {
        allTeachersMap.set(id, teacher);
      }
    });
    filteredTeachers.forEach(teacher => {
      const id = Number(teacher.id);
      if (!isNaN(id)) {
        allTeachersMap.set(id, teacher);
      }
    });
    return Array.from(allTeachersMap.values());
  }, [filteredAIRecommendations, filteredTeachers]);
  
  const teacherListSchema = useMemo(() => generateTeacherListSchema(allTeachersForSchema, "https://almancaabc.com/ogretmenler"), [allTeachersForSchema]);


  useEffect(() => {
    // Placeholder for potential URL update logic on filter change
  }, [filters, router]);

  const hasResults = filteredAIRecommendations.length > 0 || filteredTeachers.length > 0;

  const renderContent = () => {
    if (!hasResults) {
      return (
        <div className="text-center py-16">
          <SearchX className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-lg font-medium text-gray-900">Sonuç Bulunamadı</h3>
          <p className="mt-1 text-sm text-gray-500">Filtre kriterlerinizi değiştirmeyi deneyin.</p>
          <Button variant="link" className="mt-4 text-sky-600 hover:text-sky-700 dark:text-sky-400 dark:hover:text-sky-500" onClick={() => setFilters({ level: [] as string[], price: [0, 500] as [number, number], availability: [], specialties: [], country: [], search: "" })}>
            Filtreleri Temizle
          </Button>
        </div>
      );
    }

    return (<>
      {filteredAIRecommendations.length > 0 && (
        <section className="mb-12">
          <h2 className="text-2xl sm:text-3xl font-bold mb-6 text-zinc-800 dark:text-zinc-100">Size Özel Öneriler</h2>
          <div className={cn(
            "gap-4",
            viewMode === 'grid' ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 w-fit" : "flex flex-col"
          )}>
            {filteredAIRecommendations.map((teacher, index) => (
              <motion.div
                key={`ai-${teacher.id}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <TeacherCard teacher={teacher} index={index} isAIRecommended viewMode={viewMode} />
              </motion.div>
            ))}
          </div>
        </section>
      )}
      {filteredTeachers.length > 0 && (
        <section className="mt-10 sm:mt-16">
          <div className="flex flex-col sm:flex-row justify-between items-center mb-6">
            <h1 className="text-2xl sm:text-3xl font-bold text-zinc-800 dark:text-zinc-100 mb-3 sm:mb-0" tabIndex={0} aria-label={`Tüm Öğretmenler (${filteredTeachers.length})`}>
              Tüm Öğretmenler <span className="text-base sm:text-lg font-semibold text-zinc-500 dark:text-zinc-400">({filteredTeachers.length} bulundu)</span>
            </h1>
          </div>
          <div className={cn(
            "gap-4",
            viewMode === 'grid' ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 w-fit" : "flex flex-col"
          )}>
            {filteredTeachers.slice((currentPage - 1) * teachersPerPage, currentPage * teachersPerPage).map((teacher, index) => (
              <motion.div
                key={`teacher-${teacher.id}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <TeacherCard teacher={teacher} index={((currentPage - 1) * teachersPerPage) + index} viewMode={viewMode} />
              </motion.div>
            ))}
          </div>
          {filteredTeachers.length > teachersPerPage && (
            <div className="mt-8 sm:mt-12 flex justify-center">
              <Pagination
                page={currentPage}
                count={filteredTeachers.length}
              />
            </div>
          )}
        </section>
      )}
    </>);
  }; // renderContent fonksiyonunun kapanışı

  return (
    // Fragment kapanışı
    (<>
      <Head>
        <title>Almanca Öğretmenleri - Online Almanca Özel Ders | AlmancaABC</title>
        <meta name="description" content="AlmancaABC platformunda deneyimli Almanca öğretmenlerini bulun. Size özel online Almanca dersleri ile Almanca öğrenmeye başlayın." />
        {/* Diğer meta etiketleri buraya eklenebilir */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(faqPageSchema) }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(teacherListSchema) }}
        />
      </Head>
      <div className="min-h-screen bg-[#f2f4f7]"> {/* Bu ana sarmalayıcı div */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
          <section className="text-center mb-12">
            <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight text-slate-900 dark:text-slate-100 mb-4"
          >
            Online Almanca Kursu:
            <span className="block sm:inline">Almanca Özel Ders Öğretmeni Seç</span>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-4 max-w-2xl mx-auto text-lg sm:text-xl text-slate-600 dark:text-slate-300"
          >
            AlmancaABC, kişiye özel online Almanca dersleri sunar. Hayalinizdeki Almanca öğretmenini bulun,
            hemen derslere başlayın ve Almanca öğrenme hedeflerinize ulaşın!
          </motion.p>
        </section>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <FilterBar
            filters={filters}
            setFilters={setFilters}
            totalTeachers={filteredTeachers.length}
            viewMode={viewMode}
            setViewMode={setViewMode}
          />
        </motion.div>

        <div className="mt-8 sm:mt-12">
          {renderContent()}
        </div>

        <section className="py-16 bg-white dark:bg-slate-900 mt-16">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-center text-slate-800 dark:text-slate-200 mb-12">
              Online Almanca Öğrenmek için AlmancaABC Neden En İyisi?
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="flex flex-col items-center text-center p-6 bg-slate-50 dark:bg-slate-800 rounded-lg shadow-lg hover:shadow-sky-500/30 transition-shadow duration-300">
                <Target className="h-12 w-12 text-sky-500 mb-4" />
                <h3 className="text-xl font-semibold text-slate-700 dark:text-slate-300 mb-2">Sadece Almanca, Tam Uzmanlık</h3>
                <p className="text-slate-600 dark:text-slate-400 text-sm">
                  Genel dil platformlarının aksine, AlmancaABC sadece Almanca öğretimine odaklanır. Bu sayede size Almanca öğrenme yolculuğunuzda en iyi uzmanlığı ve kaynakları sunarız.
                </p>
              </div>
              <div className="flex flex-col items-center text-center p-6 bg-slate-50 dark:bg-slate-800 rounded-lg shadow-lg hover:shadow-sky-500/30 transition-shadow duration-300">
                <Users className="h-12 w-12 text-sky-500 mb-4" />
                <h3 className="text-xl font-semibold text-slate-700 dark:text-slate-300 mb-2">Kişiye Özel Ders Deneyimi</h3>
                <p className="text-slate-600 dark:text-slate-400 text-sm">
                  Size özel atanmış öğretmenlerle birebir dersler alın. Kendi hızınızda, kendi programınıza uygun esnek ders saatleri ile rahat ve baskısız bir ortamda öğrenme özgürlüğünü yaşayın.
                </p>
              </div>
              <div className="flex flex-col items-center text-center p-6 bg-slate-50 dark:bg-slate-800 rounded-lg shadow-lg hover:shadow-sky-500/30 transition-shadow duration-300">
                <Award className="h-12 w-12 text-sky-500 mb-4" />
                <h3 className="text-xl font-semibold text-slate-700 dark:text-slate-300 mb-2">Deneyimli ve Onaylı Öğretmenler</h3>
                <p className="text-slate-600 dark:text-slate-400 text-sm">
                  Alanında uzman, deneyimli ve özenle seçilmiş Almanca öğretmenlerimizle çalışın. Ana dili Almanca olan veya anadil seviyesinde Almanca konuşan eğitmenlerimizle hedeflerinize ulaşın.
                </p>
              </div>
              <div className="flex flex-col items-center text-center p-6 bg-slate-50 dark:bg-slate-800 rounded-lg shadow-lg hover:shadow-sky-500/30 transition-shadow duration-300">
                <CircleDollarSign className="h-12 w-12 text-sky-500 mb-4" />
                <h3 className="text-xl font-semibold text-slate-700 dark:text-slate-300 mb-2">Şeffaf ve Uygun Fiyatlar</h3>
                <p className="text-slate-600 dark:text-slate-400 text-sm">
                  Bütçenize uygun, rekabetçi ve şeffaf fiyatlandırma politikamızla kaliteli Almanca eğitimine kolayca erişin. Gizli ücretler olmadan, net ve anlaşılır ders ücretleri.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16 bg-slate-50 dark:bg-slate-800/50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-center text-slate-800 dark:text-slate-200 mb-12">
              Almanca Öğrencilerimiz Bizi Çok Seviyor
            </h2>
            <div className="relative">
              <AnimatePresence>
                {showTestimonialLeftArrow && (
                  <motion.button
                    onClick={() => scrollTestimonials('left')}
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="absolute left-0 top-1/2 -translate-y-1/2 z-20 bg-white/80 backdrop-blur-sm rounded-full p-2 shadow-md text-sky-600 border border-gray-200 hover:bg-sky-500/10 transition-all duration-200 -ml-3 disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Sola kaydır"
                    whileHover={{ scale: 1.1, boxShadow: "0 4px 12px rgba(0,0,0,0.1)" }}
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </motion.button>
                )}
              </AnimatePresence>
              <AnimatePresence>
                {showTestimonialRightArrow && (
                  <motion.button
                    onClick={() => scrollTestimonials('right')}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    className="absolute right-0 top-1/2 -translate-y-1/2 z-20 bg-white/80 backdrop-blur-sm rounded-full p-2 shadow-md text-sky-600 border border-gray-200 hover:bg-sky-500/10 transition-all duration-200 -mr-3 disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Sağa kaydır"
                    whileHover={{ scale: 1.1, boxShadow: "0 4px 12px rgba(0,0,0,0.1)" }}
                  >
                    <ChevronRight className="h-5 w-5" />
                  </motion.button>
                )}
              </AnimatePresence>
              <div ref={testimonialsScrollRef} className="flex overflow-x-auto space-x-6 pb-4 -mx-4 px-4 scrollbar-hide scroll-smooth">
                {ogrenciYorumlari.map((yorum, index) => (
                  <motion.div
                    key={yorum.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="w-[300px] md:w-[320px] flex-shrink-0"
                  >
                    <Card className="h-full bg-white dark:bg-slate-800 shadow-lg rounded-xl overflow-hidden">
                      <CardContent className="p-6 flex flex-col">
                        <div className="flex items-center mb-4">
                          <div className="relative h-12 w-12 rounded-full overflow-hidden mr-4 border-2 border-sky-200 dark:border-sky-700">
                            <Image src={yorum.image} alt={yorum.name} fill sizes="48px" className="object-cover" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-slate-800 dark:text-slate-200">{yorum.name}</h3>
                            <p className="text-sm text-slate-500 dark:text-slate-400">{yorum.role}</p>
                          </div>
                        </div>
                        <div className="flex mb-3">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < yorum.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300 dark:text-gray-600"
                              }`}
                            />
                          ))}
                        </div>
                        <p className="text-slate-600 dark:text-slate-300 text-sm italic leading-relaxed flex-grow">&ldquo;{yorum.text}&rdquo;</p>
                        {yorum.dersAldigiOgretmen && (
                          <p className="text-xs text-slate-400 dark:text-slate-500 mt-3 pt-3 border-t border-slate-200 dark:border-slate-700">
                            Öğretmeni: {yorum.dersAldigiOgretmen}
                          </p>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>

        <section className="py-16 bg-white dark:bg-slate-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <FaqAccordion items={faqItemsToDisplay} title="Öğretmenlerle İlgili Sıkça Sorulan Sorular" subtitle="Öğretmenlerimiz ve platformumuz hakkında merak ettikleriniz."/>
          </div>
        </section>

        <section className="py-16 bg-slate-50 dark:bg-slate-800/50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Bizimle İletişime Geçin
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6 max-w-xl mx-auto">
              Sorularınız mı var veya yardıma mı ihtiyacınız var? Hızlıca mesaj gönderebilir veya diğer tüm iletişim seçenekleri için <Link href="/iletisim" className="text-sky-600 hover:underline dark:text-sky-400">iletişim sayfamızı</Link> ya da <Link href="/yardim" className="text-sky-600 hover:underline dark:text-sky-400">yardım merkezimizi</Link> ziyaret edebilirsiniz.
            </p>
            <Dialog>
              <DialogTrigger asChild>
                <Button size="lg" className="bg-primary hover:bg-primary/90 text-white dark:bg-sky-600 dark:hover:bg-sky-700">
                  <MessageSquarePlus className="mr-2 h-5 w-5" />
                  Mesaj Gönder
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-4xl dark:bg-slate-800 p-0">
                <DialogHeader className="p-6 pb-4">
                  <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-gray-100">Bize Ulaşın</DialogTitle>
                  <DialogDescription className="text-sm text-gray-600 dark:text-gray-300">
                    Aşağıdaki formu doldurarak veya iletişim bilgilerimizi kullanarak bize ulaşabilirsiniz.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-1 lg:grid-cols-5 overflow-hidden rounded-b-lg">
                  <IletisimBilgileriKarti />
                  <div className="p-6 md:p-8 lg:col-span-3 bg-white dark:bg-slate-800">
                    <ModernContactForm />
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </motion.div>
        </div>
      </section>

      {/* Yeni SEO İçeriği Başlangıcı */}
      <section className="py-16 bg-white dark:bg-slate-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center text-slate-800 dark:text-slate-200 mb-6">
            Online Almanca Kursu: Almanca Özel Ders ile Hedeflerinize Ulaşın!
          </h2>
          <p className="text-lg text-slate-600 dark:text-slate-300 mb-8 text-center max-w-3xl mx-auto">
            Almanca öğrenmek, kariyerinizde yeni kapılar açmak, Almanya&apos;da yaşamak, eğitim almak ya da sadece yeni bir kültürü keşfetmek mi istiyorsunuz? <strong>Almanca özel ders</strong> ve <strong>online Almanca kursu</strong> seçeneklerimizle almancaabc.com olarak yanınızdayız! Kişiye özel hazırlanan ders programlarımızla, Almanca öğrenme hedeflerinize en kısa sürede ve en etkili şekilde ulaşmanızı sağlıyoruz. İster sıfırdan başlayın, ister mevcut Almanca bilginizi ilerletmek isteyin, <strong>en iyi online Almanca kursu</strong> deneyimini size sunmak için buradayız.
          </p>

          <h3 className="text-2xl font-semibold text-slate-800 dark:text-slate-200 mb-4 mt-10 text-center">
            Neden Almancaabc.com ile Almanca Özel Ders Almalısınız?
          </h3>
          <p className="text-slate-600 dark:text-slate-300 mb-8 text-center max-w-2xl mx-auto">
            Kalabalık sınıfların aksine, <strong>birebir Almanca dersleri</strong> ile tamamen size odaklanıyoruz. Uzman Almanca eğitmenimiz eşliğinde, kendi hızınızda ve ihtiyaçlarınıza göre şekillendirilmiş bir <strong>Almanca eğitim</strong> programıyla öğrenirsiniz.
          </p>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-lg">
              <h4 className="text-xl font-semibold text-sky-600 dark:text-sky-400 mb-2">Kişiye Özel Ders Programı</h4>
              <p className="text-sm text-slate-600 dark:text-slate-400">Seviyeniz, hedefleriniz ve öğrenme stiliniz analiz edilerek size özel bir <strong>Almanca ders</strong> planı oluşturulur. Böylece zamanınızı en verimli şekilde kullanırsınız.</p>
            </div>
            <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-lg">
              <h4 className="text-xl font-semibold text-sky-600 dark:text-sky-400 mb-2">Esnek Ders Saatleri</h4>
              <p className="text-sm text-slate-600 dark:text-slate-400">Yoğun programınıza uyum sağlayan esnek ders saatleriyle <strong>online Almanca</strong> öğrenmenin keyfini çıkarın. Evinizin konforunda, dilediğiniz zaman ders alabilirsiniz.</p>
            </div>
            <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-lg">
              <h4 className="text-xl font-semibold text-sky-600 dark:text-sky-400 mb-2">Uzman Almanca Eğitmeni</h4>
              <p className="text-sm text-slate-600 dark:text-slate-400">Deneyimli ve pedagojik formasyona sahip eğitmenimizle Almancayı doğru ve kalıcı bir şekilde öğrenin. <strong>Almanca konuşma pratiği</strong> ve dilbilgisi konularında uzmanlaşın.</p>
            </div>
            <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-lg">
              <h4 className="text-xl font-semibold text-sky-600 dark:text-sky-400 mb-2">Hedefe Yönelik Eğitim</h4>
              <p className="text-sm text-slate-600 dark:text-slate-400">Goethe, TestDaF, TELC gibi sınavlara hazırlık, iş Almancası, akademik Almanca veya günlük konuşma gibi özel ihtiyaçlarınıza yönelik <strong>Almanca kursları</strong> sunuyoruz.</p>
            </div>
            <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-lg">
              <h4 className="text-xl font-semibold text-sky-600 dark:text-sky-400 mb-2">Uygun Almanca Özel Ders Fiyatları</h4>
              <p className="text-sm text-slate-600 dark:text-slate-400">Kaliteli bir <strong>Almanca eğitimi</strong> için bütçenizi zorlamadan, rekabetçi ve şeffaf <strong>Almanca özel ders fiyatları</strong> ile hizmet veriyoruz.</p>
            </div>
            <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-lg">
              <h4 className="text-xl font-semibold text-sky-600 dark:text-sky-400 mb-2">Etkileşimli Online Dersler</h4>
              <p className="text-sm text-slate-600 dark:text-slate-400">Modern eğitim metotları ve interaktif <strong>Almanca ders materyalleri</strong> ile dersler sıkıcı olmaktan çıkıyor, keyifli bir öğrenme sürecine dönüşüyor.</p>
            </div>
          </div>

          <h3 className="text-2xl font-semibold text-slate-800 dark:text-slate-200 mb-4 mt-10 text-center">
            Online Almanca Kursu Programlarımız
          </h3>
          <ul className="list-disc list-inside space-y-2 mb-8 text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
            <li><strong>Başlangıç Seviyesi Almanca (A1-A2):</strong> Temel Almanca dilbilgisi, kelime bilgisi ve günlük konuşma kalıpları.</li>
            <li><strong>Orta Seviye Almanca (B1-B2):</strong> Daha karmaşık cümle yapıları, akıcı konuşma ve anlama becerileri. <strong>B1 Almanca kursu</strong> ile Almancada önemli bir eşiği aşın!</li>
            <li><strong>İleri Seviye Almanca (C1-C2):</strong> Akademik ve profesyonel düzeyde Almanca, ileri dilbilgisi ve sofistike ifade biçimleri.</li>
            <li><strong>Sınav Hazırlık Kursları:</strong> Goethe, TELC, TestDaF gibi sınavlara yönelik özel stratejiler ve pratik çalışmalar.</li>
            <li><strong>Konuşma Odaklı Almanca Dersleri:</strong> Akıcılığınızı artırmak ve özgüvenle Almanca konuşmak için pratik dersler.</li>
          </ul>
          <p className="text-lg text-slate-600 dark:text-slate-300 mb-8 text-center max-w-3xl mx-auto">
            <strong>Almanca öğrenme</strong> yolculuğunuzda size rehberlik etmekten mutluluk duyarız. <strong>Online Almanca dersleri</strong> ile coğrafi sınırlamalar olmadan, Türkiye&apos;nin veya dünyanın neresinde olursanız olun kaliteli bir eğitim alabilirsiniz.
          </p>
          <div className="text-center">
            <Button size="lg" asChild className="bg-green-500 hover:bg-green-600 text-white">
              <Link href="/iletisim#iletisim-formu"> {/* İletişim sayfasındaki forma yönlendirme */}
                Hemen Bilgi Alın ve Almanca Özel Derslere Başlayın!
              </Link>
            </Button>
          </div>
        </div>
      </section>
      {/* Yeni SEO İçeriği Bitişi */}

    </div> {/* .container kapanışı */}
  </div> {/* .min-h-screen kapanışı */}
    </>)
  );
} // TeachersPageContent fonksiyonunun kapanışı