# Sequential Thinking MCP <PERSON><PERSON><PERSON> (AlmancaABC Projesi)

## <PERSON><PERSON><PERSON> Bilgileri

- **<PERSON><PERSON><PERSON>layıcı:** `github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking`
- **<PERSON><PERSON><PERSON><PERSON><PERSON>:** `cmd /c npx -y @modelcontextprotocol/server-sequential-thinking`
- **Açıklama:** Karmaşık problemleri adım adım çözmek için kullanılan bir araç sunar.

## Kullanılabilir Araçlar

### `sequentialthinking`
- **Açıklama:** Bir problemi adım adım analiz etmek ve çözmek için kullanılır.
- **<PERSON><PERSON><PERSON>:**
  ```json
  {
    "type": "object",
    "properties": {
      "thought": {
        "type": "string",
        "description": "Mevcut düşünce adımı"
      },
      "nextThoughtNeeded": {
        "type": "boolean",
        "description": "Başka bir düşünce adımına ihtiyaç olup olmadığı"
      },
      "thoughtNumber": {
        "type": "integer",
        "description": "Mevcut düşünce numarası",
        "minimum": 1
      },
      "totalThoughts": {
        "type": "integer",
        "description": "Tahmini toplam düşünce sayısı",
        "minimum": 1
      },
      "isRevision": {
        "type": "boolean",
        "description": "Önceki düşünceyi gözden geçirip geçirmediği"
      },
      "revisesThought": {
        "type": "integer",
        "description": "Hangi düşüncenin yeniden değerlendirildiği",
        "minimum": 1
      },
      "branchFromThought": {
        "type": "integer",
        "description": "Dallanma noktası düşünce numarası",
        "minimum": 1
      },
      "branchId": {
        "type": "string",
        "description": "Dal tanımlayıcısı"
      },
      "needsMoreThoughts": {
        "type": "boolean",
        "description": "Daha fazla düşünceye ihtiyaç olup olmadığı"
      }
    },
    "required": [
      "thought",
      "nextThoughtNeeded",
      "thoughtNumber",
      "totalThoughts"
    ]
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "thought": "İlk adım olarak, problemi tanımla.",
    "nextThoughtNeeded": true,
    "thoughtNumber": 1,
    "totalThoughts": 5
  }
