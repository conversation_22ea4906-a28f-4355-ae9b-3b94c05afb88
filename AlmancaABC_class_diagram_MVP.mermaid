classDiagram
    User <|-- Student
    User <|-- Teacher
    Teacher "1" -- "*" Schedule
    Teacher "1" -- "*" Course
    Teacher "1" -- "*" Booking : "Teaches in"
    Teacher "1" -- "*" Review : "Receives" // Eklendi
    Student "*" -- "*" Course
    Student "1" -- "*" Booking : "Books"
    Student "1" -- "*" Review : "Writes" // Eklendi
    Booking "1" -- "1" VideoSession
    Booking "1" -- "1" Schedule
    Booking "1" -- "0..1" Review : "Is Reviewed" // Etiket güncellendi (isteğe bağlı)
    Booking "1" -- "0..1" Payment

    class User {
        +string id
        +string email
        +string firstName
        +string lastName
        +string profileImage
        +string phoneNumber
        +string language
        +DateTime createdAt
        +DateTime updatedAt
        +updateProfile(ProfileUpdateParams) User
    }

    class Student {
        +string id
        +User user
        +string[] learningGoals
        +string proficiencyLevel
        +string[] favoriteTeacherIds
        +Review[] reviewsWritten // Eklendi
        +getProfile() StudentProfile
        +bookLesson(BookingParams) Booking
        +getFavoriteTeachers() Teacher[]
        +addFavoriteTeacher(teacherId) void
    }

    class Teacher {
        +string id
        +User user
        +string bio
        +string[] specializations
        +string[] certificates
        +number hourlyRate
        +number rating
        +number completedLessons
        +boolean isVerified
        +string introVideoUrl
        +Review[] reviewsReceived // Eklendi
        +getProfile() TeacherProfile
        +updateAvailability(ScheduleParams[]) void
        +getBookings(DateRange) Booking[]
        +createCourse(CourseParams) Course
    }

    class Schedule {
        +string id
        +string teacherId
        +DateTime startTime
        +DateTime endTime
        +boolean isRecurring
        +string recurrencePattern
        +boolean isBooked
        +getAvailableSlots(DateRange) TimeSlot[]
    }

    class Booking {
        +string id
        +string studentId
        +string teacherId
        +string scheduleId
        +DateTime startTime
        +DateTime endTime
        +string status
        +string notes
        +string lessonType
        +cancelBooking(reason) boolean
        +rescheduleBooking(newScheduleId) Booking
        +completeLesson() void
    }

    class VideoSession {
        +string id
        +string bookingId
        +string sessionUrl // Zoom Start/Join URL'leri burada veya ayrı olabilir
        +string sessionToken // Veya Zoom Meeting ID
        +DateTime scheduledStartTime
        +DateTime scheduledEndTime
        +DateTime actualStartTime
        +DateTime actualEndTime
        +string status
        +createSession() void
        +joinSession() string
        +endSession() void
    }

    class Course { // MVP Sonrası
        +string id
        +string teacherId
        +string title
        +string description
        +string level
        +number price
        +string[] topics
        +string courseImageUrl
        +string[] materialUrls // Veya YouTube linkleri
        +boolean isPublished
        +DateTime createdAt
        +publishCourse() void
        +enrollStudent(studentId) boolean
    }

    class Review {
        +string id
        +string bookingId
        +string studentId
        +string teacherId
        +number rating // 1-5
        +string comment
        +DateTime createdAt
        +createReview() void
        +updateReview(ReviewParams) void
    }

    class Payment {
        +string id
        +string bookingId
        +string studentId
        +string teacherId
        +number amount // Ödenen toplam
        +number platformFee // Platform komisyonu
        +number teacherAmount // Öğretmene giden
        +string currency
        +string status // Stripe durumu
        +string paymentIntentId // Stripe ID
        +DateTime createdAt
        +processPayment() boolean
        +refundPayment(RefundParams) boolean
    }

    // Servis ve Bileşen Sınıfları (Diyagramı sade tutmak için yorumlandı veya ayrı bir diyagramda gösterilebilir)
    /*
    class AuthService { ... }
    class StudentService { ... }
    class TeacherService { ... }
    class BookingService { ... }
    class VideoService { ... }
    class CourseService { ... }
    class ReviewService { ... }
    class PaymentService { ... }
    class SearchService { ... }
    class AppLayout { ... }
    class AuthComponents { ... }
    class TeacherComponents { ... }
    class StudentComponents { ... }
    class BookingComponents { ... }
    class VideoComponents { ... }
    class UIComponents { ... }
    class FormComponents { ... }
    */