sequenceDiagram
    participant Client
    participant AuthAPI as Next.js Auth API
    participant Clerk
    participant Supabase
    participant ZoomAPI
    participant StripeAP<PERSON>

    %% User Registration and Role Selection
    Client->>AuthAPI: registerUser(email, password, role)
    AuthAPI->>Clerk: createUser(email, password)
    Clerk-->>AuthAPI: userId, sessionToken
    AuthAPI->>Supabase: createUserProfile(userId, role, initialData)
    Supabase-->>AuthAPI: userProfile
    AuthAPI->>Clerk: updateUserMetadata(userId, {role})
    Clerk-->>AuthAPI: updatedUser
    AuthAPI-->>Client: {success, user, session}
    Client->>Client: redirectToRoleSpecificOnboarding(role)

    %% Teacher Profile Creation and Calendar Setup
    Client->>TeacherAPI: createTeacherProfile(userId, profileData)
    TeacherAPI->>Clerk: getUser(userId)
    Clerk-->>TeacherAPI: user
    TeacherAPI->>Supabase: createTeacherProfile(userId, profileData)
    Supabase-->>TeacherAPI: teacherProfile
    Client->>TeacherAPI: setTeacherAvailability(userId, availabilityData)
    TeacherAPI->>Supabase: insertAvailabilitySlots(userId, formattedSlots)
    Supabase-->>TeacherAPI: scheduleEntries
    TeacherAPI-->>Client: {success, teacherProfile, scheduleEntries}

    %% Student Searching for Teachers
    Client->>SearchAPI: searchTeachers(filters)
    SearchAPI->>Supabase: queryTeachers(formattedFilters)
    Supabase-->>SearchAPI: teacherResults
    SearchAPI->>Supabase: getTeacherAvailability(teacherIds, dateRange)
    Supabase-->>SearchAPI: availabilityData
    SearchAPI->>Supabase: getTeacherRatings(teacherIds)
    Supabase-->>SearchAPI: ratingsData
    SearchAPI-->>Client: {teachers, availability, ratings}
    Client->>Client: renderTeacherResults(data)

    %% Booking a Lesson
    Client->>BookingAPI: initiateBooking(teacherId, scheduleId, studentId)
    BookingAPI->>Supabase: checkSlotAvailability(scheduleId)
    Supabase-->>BookingAPI: {available: true/false}
    alt Slot Available
        BookingAPI->>Supabase: createPendingBooking(bookingData)
        Supabase-->>BookingAPI: pendingBooking
        BookingAPI->>StripeAPI: createPaymentIntent(bookingAmount, metadata)
        StripeAPI-->>BookingAPI: paymentIntent
        BookingAPI-->>Client: {success: true, booking, paymentIntent}
        Client->>Client: showPaymentForm(paymentIntent)
        Client->>StripeAPI: confirmPayment(paymentIntent, paymentMethod)
        StripeAPI-->>Client: paymentResult
        Client->>BookingAPI: finalizeBooking(bookingId, paymentResult)
        BookingAPI->>Supabase: updateBookingStatus(bookingId, "confirmed")
        Supabase-->>BookingAPI: updatedBooking
        BookingAPI->>Supabase: updateScheduleSlotStatus(scheduleId, "booked")
        Supabase-->>BookingAPI: updatedSchedule
        BookingAPI-->>Client: {success: true, finalizedBooking}
    else Slot Not Available
        BookingAPI-->>Client: {success: false, error: "Slot unavailable"}
    end

    %% Joining a Video Lesson
    Client->>VideoAPI: prepareVideoSession(bookingId, userId, role)
    VideoAPI->>Supabase: getBookingDetails(bookingId)
    Supabase-->>VideoAPI: bookingDetails
    VideoAPI->>Supabase: checkExistingSession(bookingId)
    Supabase-->>VideoAPI: existingSession

    alt No Existing Session
        VideoAPI->>ZoomAPI: createMeeting()
        ZoomAPI-->>VideoAPI: meetingDetails
        VideoAPI->>Supabase: createVideoSession(bookingId, meetingDetails)
        Supabase-->>VideoAPI: videoSession
    else Session Exists
        VideoAPI->>ZoomAPI: getMeetingToken(existingSession.meetingId, userId, role)
        ZoomAPI-->>VideoAPI: joinToken
    end

    VideoAPI-->>Client: {sessionUrl, joinToken, sessionDetails}
    Client->>Client: initializeZoomClient(sessionUrl, joinToken)
    Client->>ZoomAPI: joinMeeting(joinToken)
    ZoomAPI-->>Client: Connected to session

    %% Post-Lesson Review and Rating
    Client->>ReviewAPI: submitReview(bookingId, teacherId, rating, comment)
    ReviewAPI->>Supabase: checkEligibility(bookingId, studentId)
    Supabase-->>ReviewAPI: {eligible: true/false}

    alt Eligible for Review
        ReviewAPI->>Supabase: createReview(reviewData)
        Supabase-->>ReviewAPI: newReview
        ReviewAPI->>Supabase: updateTeacherRating(teacherId)
        Supabase-->>ReviewAPI: updatedTeacher
        ReviewAPI-->>Client: {success: true, review}
    else Not Eligible
        ReviewAPI-->>Client: {success: false, error: "Not eligible for review"}
    end