import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Geliştirme aşamasında cache'i tamamen devre dışı bırak
  webpack: (config, { dev }) => {
    if (dev) {
      // Cache'i devre dışı bırak
      config.cache = false;
    }
    return config;
  },

  // Sadece temel image ayarları
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "randomuser.me",
        pathname: "/api/portraits/**",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.pexels.com",
        pathname: "/**",
      },
    ],
  },
};

export default nextConfig;
