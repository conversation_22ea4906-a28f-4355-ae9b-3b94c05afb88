"use client"

import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'

interface LevelFilterProps {
  selected: string[]
  onChange: (levels: string[]) => void
  onClose: () => void
}

const LEVELS = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']

export function LevelFilter({ selected, onChange, onClose }: LevelFilterProps) {
  const handleToggle = (level: string) => {
    if (selected.includes(level)) {
      onChange(selected.filter(l => l !== level))
    } else {
      onChange([...selected, level])
    }
  }

  const handleClear = () => onChange([])

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-2">
        {LEVELS.map(level => (
          <div key={level} className="flex items-center space-x-2">
            <Checkbox
              id={`level-${level}`}
              checked={selected.includes(level)}
              onCheckedChange={() => handleToggle(level)}
            />
            <Label htmlFor={`level-${level}`}>{level}</Label>
          </div>
        ))}
      </div>
      <div className="flex justify-end gap-2 mt-4">
        <Button variant="outline" onClick={handleClear}>Temizle</Button>
        <Button onClick={onClose}>Uygula</Button>
      </div>
    </div>
  )
}
