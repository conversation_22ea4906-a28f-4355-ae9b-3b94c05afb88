import React from 'react';
import { Play, Video, Star, BookOpen } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";

interface TeacherTabNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

interface TabItem {
  id: string;
  label: string;
  shortLabel: string;
  icon: React.ElementType;
}

export const TeacherTabNavigation: React.FC<TeacherTabNavigationProps> = ({ activeTab, onTabChange }) => {
  const tabs: TabItem[] = [
    { id: 'courses', label: 'Kurslar', shortLabel: 'Kurs', icon: Play },
    { id: 'videos', label: '<PERSON> Dersler', shortLabel: 'Video', icon: Video },
    { id: 'reviews', label: 'Yorumlar', shortLabel: 'Yorum', icon: Star },
    { id: 'about', label: 'Hakkında', shortLabel: 'Bilgi', icon: BookOpen }
  ];return (
    <nav className="bg-white border-b border-gray-200 w-full">
      <div className="w-full max-w-7xl mx-auto px-2 sm:px-4 lg:px-8">
        <div className="relative w-full overflow-x-auto scrollbar-hide py-3 lg:py-4 scroll-smooth touch-pan-x">
          {/* Subtle gradient overlays to indicate scrolling possibility on small screens */}
          <div className="absolute left-0 top-0 bottom-0 w-6 bg-gradient-to-r from-white to-transparent z-10 pointer-events-none sm:hidden"></div>
          <div className="absolute right-0 top-0 bottom-0 w-6 bg-gradient-to-l from-white to-transparent z-10 pointer-events-none sm:hidden"></div>
          
          <div className="flex space-x-2 md:space-x-3 overflow-x-auto scrollbar-hide bg-gray-50 rounded-lg lg:rounded-xl p-2 w-max min-w-full sm:w-auto mx-auto">
            {tabs.map(tab => {
              const Icon = tab.icon;
              return (                <Button
                  key={tab.id}
                  variant="ghost"
                  onClick={() => onTabChange(tab.id)}
                  className={`flex-shrink-0 min-w-[68px] sm:min-w-[100px] px-2 sm:px-4 md:px-6 py-1.5 sm:py-3 flex items-center justify-center gap-1.5 md:gap-2 transition-all duration-300 whitespace-nowrap font-medium sm:font-semibold rounded-md lg:rounded-lg text-xs sm:text-sm md:text-base ${
                    activeTab === tab.id
                      ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                  }`}
                >                  <Icon className="w-3.5 h-3.5 sm:w-4 sm:h-4 md:w-5 md:h-5 flex-shrink-0" />
                  <span className="hidden xs:inline">{tab.label}</span>
                  <span className="xs:hidden">{tab.shortLabel}</span>
                </Button>
              );
            })}
          </div>
        </div>
      </div>
    </nav>
  );
};
