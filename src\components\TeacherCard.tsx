"use client"

import Link from "next/link";
import { useState, useCallback } from "react"; // useCallback import edildi
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, MessageSquare, CalendarDays, Users, BookCheck, Heart, MapPin, CalendarPlus, Globe2 } from "lucide-react"; // MapPin, CalendarPlus eklendi, MessageCircle -> MessageSquare, Globe2 eklendi
import { VerifiedBadge } from "@/components/VerifiedBadge";
import type { Teacher } from "@/types/teacher";
import { cn } from "@/lib/utils";

interface TeacherCardProps {
  teacher: Teacher;
  index?: number;
  isAIRecommended?: boolean;
  viewMode?: 'grid' | 'list'; // Yeni prop eklendi
}

// Ülke bayrakları için basit bir eşleme (gerçek uygulamada daha kapsamlı bir çözüm gerekebilir)
const countryFlags: { [key: string]: string } = {
  DE: "🇩🇪",
  AT: "🇦🇹",
  CH: "🇨🇭",
  TR: "🇹🇷",
};

export function TeacherCard({ teacher, isAIRecommended = false, index = 0, viewMode = 'grid' }: TeacherCardProps) { // index prop'u parent tarafından kullanıldığı için kalıyor.
  const [isFavorite, setIsFavorite] = useState(false);

  const handleFavoriteToggle = (e: React.MouseEvent) => {
    e.preventDefault(); // Linkin tıklanmasını engelle
    e.stopPropagation(); // Olayın yayılmasını engelle
    setIsFavorite(!isFavorite);
    // TODO: Favori durumunu backend'e kaydetme logiği eklenecek
    // console.log(`${teacher.name} favori durumu: ${!isFavorite}`); - REMOVED
  };
  
  // Geçici müsaitlik durumu (API'den gelmeli veya teacher objesinden okunmalı)
  const getAvailabilityDetails = useCallback(() => {
    if (teacher.availability?.includes("Bugün")) return { text: "Bugün müsait", color: "bg-green-500 text-white" }; // Canlı yeşil
    if (teacher.availability?.includes("Yarın")) return { text: "Yarın müsait", color: "text-yellow-700 bg-yellow-100 dark:text-yellow-300 dark:bg-yellow-700/30" };
    if (teacher.availability && teacher.availability.length > 0) return { text: teacher.availability[0], color: "text-orange-700 bg-orange-100 dark:text-orange-300 dark:bg-orange-700/30" };
    return { text: "Müsaitlik Belirsiz", color: "text-zinc-500 bg-zinc-100 dark:text-zinc-400 dark:bg-zinc-700/30" };
  }, [teacher.availability]);
  const { text: availabilityText, color: availabilityColorClass } = getAvailabilityDetails();


  if (viewMode === 'list') {
    return (
      (<Link
        href={`/ogretmenler/${teacher.id}`}
        className="block group">
        <Card className={cn(
          "bg-white rounded-2xl shadow-xl overflow-hidden transition-all duration-300 ease-in-out hover:shadow-2xl flex flex-col md:flex-row",
          isAIRecommended ? "border-2 border-sky-400 dark:border-sky-600" : "border border-zinc-200 dark:border-zinc-700"
        )}>
          {/* Sol Kısım - Resim ve Fiyat */}
          <div className="md:w-1/3 p-5 sm:p-6 flex flex-col items-center justify-center bg-gradient-to-br from-sky-50 to-indigo-100 dark:from-sky-900/30 dark:to-indigo-900/30 md:rounded-l-2xl md:rounded-r-none rounded-t-2xl relative">
            <Avatar className="h-32 w-32 sm:h-36 sm:w-36 object-cover rounded-full shadow-lg border-4 border-white dark:border-zinc-700 group-hover:scale-105 transition-transform duration-300"> {/* Boyut artırıldı */}
              <AvatarImage
                src={teacher.avatar || teacher.profile_image_url || `https://avatar.vercel.sh/${teacher.name || teacher.id}.png?size=144`}
                alt={`${teacher.name || 'Öğretmen'} profil fotoğrafı`}
                className="object-cover"
              />
              <AvatarFallback className="text-3xl sm:text-4xl rounded-full bg-zinc-200 dark:bg-zinc-700 text-zinc-500 dark:text-zinc-400">
                {teacher.name ? teacher.name.split(' ').map(n => n[0]).join('').toUpperCase() : 'Ö'}
              </AvatarFallback>
            </Avatar>
            {teacher.isSuperTeacher && (
              <Badge className="absolute top-3 left-3 bg-gradient-to-r from-amber-400 to-orange-500 text-white px-2 py-0.5 text-xs font-semibold shadow-lg">
                Süper Öğretmen <Star className="w-3 h-3 ml-1 inline-block fill-white" />
              </Badge>
            )}
            <div className="mt-3 text-center"> {/* mt-4 -> mt-3 */}
              <span className="text-2xl sm:text-3xl font-bold text-sky-600 dark:text-sky-400">{teacher.price}₺</span> {/* Font boyutu sm breakpoint'i için ayarlandı */}
              <span className="text-xs sm:text-sm font-normal text-zinc-500 dark:text-zinc-400">/ders</span>
            </div>
            <Button className="mt-3 w-full bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition duration-150 text-sm font-semibold flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-green-400"> {/* mt-4 -> mt-3, py-2.5 -> py-2 */}
              <CalendarPlus size={16} className="mr-1.5" /> Deneme Dersi Al {/* İkon boyutu ve margin ayarlandı */}
            </Button>
          </div>

          {/* Sağ Kısım - Bilgiler */}
          <div className="md:w-2/3 p-3 sm:p-4 flex flex-col justify-between"> {/* p-4/p-5 -> p-3/p-4 */}
            <div>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-1"> {/* mb-1.5 -> mb-1 */}                <h2 className="text-lg sm:text-xl font-bold text-zinc-800 dark:text-zinc-100 hover:text-sky-600 dark:hover:text-sky-400 transition-colors cursor-pointer flex items-center"> {/* Font boyutu küçültüldü */}
                  {teacher.name || "İsim Belirtilmemiş"}
                  {(teacher.verified || teacher.is_verified) && <VerifiedBadge 
                    isVerified={!!(teacher.verified || teacher.is_verified)} 
                    className="ml-2" 
                    size="sm" 
                    variant="simple"
                  />}
                  {teacher.isOnline && (
                    <Badge className="ml-2 bg-green-500 hover:bg-green-600 text-white px-2 py-0.5 text-xs font-semibold">
                      Online
                    </Badge>
                  )}
                </h2>
                <div className="flex items-center mt-1 sm:mt-0">
                  <Star size={18} className="text-yellow-400 mr-1" fill="currentColor" /> {/* İkon boyutu ayarlandı */}
                  <span className="text-zinc-700 dark:text-zinc-200 font-semibold text-xs sm:text-sm">{teacher.rating?.toFixed(1)}</span> {/* Font boyutu küçültüldü */}
                  <span className="text-zinc-500 dark:text-zinc-400 text-xs ml-1">({teacher.reviewCount} yorum)</span>
                </div>
              </div>
              <p className="text-xs text-sky-600 dark:text-sky-400 font-medium flex items-center mb-0.5"> {/* mb-1 -> mb-0.5, font boyutu ayarlandı */}
                <MapPin size={12} className="mr-1" /> {countryFlags[teacher.country || ''] || teacher.country || "Belirtilmemiş"}
              </p>
              <p className="text-sm text-zinc-700 dark:text-zinc-300 font-medium mb-1.5">{teacher.title || (teacher.specializations && teacher.specializations.length > 0 ? teacher.specializations[0] : "Almanca Öğretmeni")}</p> {/* mb-2 -> mb-1.5, font boyutu küçültüldü */}
              
              <p className="text-zinc-600 dark:text-zinc-400 text-xs mb-2 leading-relaxed line-clamp-2 group-hover:line-clamp-none transition-all duration-300">{teacher.bio || "Bu öğretmen hakkında henüz bir açıklama eklenmemiş."}</p> {/* sm:text-sm kaldırıldı, mb-3 -> mb-2 */}

              {isAIRecommended &&
                <Badge variant="default" className="mb-1.5 bg-sky-100 text-sky-700 dark:bg-sky-700/30 dark:text-sky-300 border border-sky-300 dark:border-sky-600 text-xs px-1.5 py-0.5"> {/* mb-2 -> mb-1.5 */}
                  ★ AI Önerisi
                </Badge>
              }

              {teacher.specializations && teacher.specializations.length > 0 && (
                <div className="mb-2"> {/* mb-3 -> mb-2 */}
                  <h4 className="text-xs font-semibold text-zinc-700 dark:text-zinc-300 mb-0.5">Uzmanlık Alanları:</h4> {/* mb-1 -> mb-0.5 */}
                  <div className="flex flex-wrap gap-1"> {/* gap-1.5 -> gap-1 */}
                    {teacher.specializations.slice(0, 3).map(spec => (
                      <Badge key={spec} variant="secondary" className="bg-indigo-100 text-indigo-700 dark:bg-indigo-700/30 dark:text-indigo-300 px-2.5 py-1 text-xs font-medium">{spec}</Badge>
                    ))}
                    {teacher.specializations.length > 3 && (
                        <Badge variant="secondary" className="bg-gray-100 text-gray-700 dark:bg-gray-700/30 dark:text-gray-300 px-2.5 py-1 text-xs font-medium">+{teacher.specializations.length - 3} daha</Badge>
                    )}
                  </div>
                </div>
              )}
              
              {teacher.spokenLanguages && teacher.spokenLanguages.length > 0 && (
                <div className="mb-2"> {/* mb-3 -> mb-2 */}
                  <h4 className="text-xs font-semibold text-zinc-700 dark:text-zinc-300 mb-0.5">Konuştuğu Diller:</h4> {/* mb-1 -> mb-0.5 */}
                  <div className="flex flex-wrap gap-1">
                    {teacher.spokenLanguages.map(lang => (
                      <Badge key={lang.language} variant="outline" className="text-xs font-normal border-zinc-300 dark:border-zinc-600 text-zinc-600 dark:text-zinc-400">
                        {lang.language} ({lang.level})
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="border-t border-zinc-200 dark:border-zinc-700 pt-2 mt-auto"> {/* pt-3 -> pt-2 */}
              <div className="flex justify-between items-center text-xs text-zinc-500 dark:text-zinc-400 mb-2"> {/* sm:text-sm kaldırıldı, mb-3 -> mb-2 */}
                <span className="flex items-center"><BookCheck size={12} className="mr-1 text-zinc-400 dark:text-zinc-500"/> {teacher.totalLessons || 0} ders</span> {/* İkon boyutu küçültüldü */}
                <span className="flex items-center"><Users size={12} className="mr-1 text-zinc-400 dark:text-zinc-500"/> {teacher.activeStudents || 0} aktif öğrenci</span> {/* İkon boyutu küçültüldü */}
              </div>
              <div className="flex flex-col sm:flex-row space-y-1.5 sm:space-y-0 sm:space-x-1.5"> {/* space-y/x-2 -> space-y/x-1.5 */}
                <Button size="sm" className="w-full sm:w-auto flex-grow bg-gray-900 hover:bg-gray-700 text-white dark:bg-gray-800 dark:hover:bg-gray-700 px-3 py-1.5 text-xs font-semibold flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-gray-500"> {/* padding ve font boyutu küçültüldü */}
                    <CalendarPlus size={14} className="mr-1" /> Ders Planla {/* İkon boyutu ve margin küçültüldü */}
                </Button>
                <Button size="sm" variant="outline" className="w-full sm:w-auto flex-grow bg-white hover:bg-gray-100 text-gray-700 border-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-500 dark:hover:bg-gray-600 px-3 py-1.5 text-xs font-semibold flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-gray-400"> {/* padding ve font boyutu küçültüldü */}
                    <MessageSquare size={14} className="mr-1" /> Mesaj Gönder {/* İkon boyutu ve margin küçültüldü */}
                </Button>
                <Button variant="ghost" size="icon" className="sm:ml-1 hover:bg-red-50 dark:hover:bg-red-900/30 w-8 h-8" onClick={handleFavoriteToggle} aria-label="Favorilere ekle"> {/* boyut küçültüldü */}
                  <Heart className={cn("w-3.5 h-3.5", isFavorite ? "fill-red-500 text-red-500" : "text-zinc-400 dark:text-zinc-500 group-hover:text-red-400")} /> {/* İkon boyutu küçültüldü */}
                </Button>
              </div>
               <div className={cn("text-xs font-medium px-1.5 py-0.5 rounded-full inline-flex items-center gap-0.5 mt-1.5", availabilityColorClass)}> {/* gap ve mt küçültüldü */}
                  <CalendarDays className="w-2.5 h-2.5" /> {/* İkon boyutu küçültüldü */}
                  {availabilityText}
              </div>
            </div>
          </div>
        </Card>
      </Link>)
    );
  }

  // Grid View (Ana sayfa referans alınarak güncellendi - Daha kapsamlı değişiklik)
  return (
    (<Link
      href={`/ogretmenler/${teacher.id}`}
      className="block group h-full w-[240px] md:w-[260px] flex-shrink-0 relative overflow-visible">
      {/* Sıra Numarası Rozeti - TeacherList.tsx stilinde */}
      {typeof index === 'number' && viewMode === 'grid' && (
        <div className="absolute left-2 top-2 z-10 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 text-sm font-bold shadow">
          {index + 1}
        </div>
      )}
      <Card className={cn(
        "flex flex-col rounded-xl border py-6 shadow-sm h-full hover:shadow-lg transition-shadow overflow-visible", // gap-6 kaldırıldı, TeacherList.tsx'deki gibi
        isAIRecommended ? "bg-white dark:bg-zinc-800/50 border-sky-200 dark:border-sky-800" : "bg-white dark:bg-zinc-800/50 border-zinc-200 dark:border-zinc-700"
      )}>
        <CardContent className="p-4 flex flex-col items-center text-center"> {/* flex-grow kaldırıldı */}
          <div className="relative h-24 w-24 mb-4"> {/* Avatar ve online göstergesi için wrapper */}
            <Avatar className="h-full w-full rounded-full border-2 border-white dark:border-zinc-700 shadow-md group-hover:scale-105 transition-transform duration-300">
              <AvatarImage
                src={teacher.avatar || teacher.profile_image_url || `https://avatar.vercel.sh/${teacher.name || teacher.id}.png?size=96`} // size=96
                alt={`${teacher.name || 'Öğretmen'} profil fotoğrafı`}
              />
              <AvatarFallback className="text-3xl rounded-full bg-zinc-200 dark:bg-zinc-700 text-zinc-500 dark:text-zinc-400">
                {teacher.name ? teacher.name.split(' ').map(n => n[0]).join('').toUpperCase() : 'Ö'}
              </AvatarFallback>
            </Avatar>
            {teacher.isOnline && (
              <span className="absolute bottom-1 right-1 block h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-zinc-800/50 ring-1 ring-green-600" title="Online"></span>
            )}
          </div>

          {/* İsim, Doğrulama ve Ülke (TeacherList.tsx stiline yakın) */}
          <div className="mb-2">
            <div className="flex items-baseline justify-center gap-1">              <h3 className="font-semibold text-lg text-zinc-800 dark:text-zinc-100 group-hover:text-sky-600 dark:group-hover:text-sky-400 transition-colors">
                {teacher.name || "İsim Belirtilmemiş"}
              </h3>
              {(teacher.verified || teacher.is_verified) && <VerifiedBadge 
                isVerified={!!(teacher.verified || teacher.is_verified)} 
                size="xs"
                variant="simple"
              />}
              {countryFlags[teacher.country || ''] && <span className="text-base flex-shrink-0 ml-1">{countryFlags[teacher.country || '']}</span>}
            </div>
          </div>
          
          {/* Puanlama (TeacherList.tsx stiline yakın) */}
          <div className="flex items-center justify-center text-sm text-zinc-500 dark:text-zinc-400 mb-2">
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <span className="font-medium text-zinc-700 dark:text-zinc-200 ml-1">{teacher.rating?.toFixed(1)}</span>
            <span className="ml-1">({teacher.reviewCount} yorum)</span>
          </div>

          {/* Fiyat (TeacherList.tsx stiline yakın) */}
          <div className="text-center mb-2">
            <span className="font-semibold text-lg text-primary">{teacher.price}₺</span>
            <span className="text-sm text-zinc-500 dark:text-zinc-400">/saat</span>
          </div>
          
          {/* Uzmanlık Alanları (Badge olarak, ilk 2 tane) */}
          {teacher.specializations && teacher.specializations.length > 0 && (
            <div className="flex flex-wrap gap-1 justify-center mb-2 px-2">
              {teacher.specializations.slice(0, 2).map((spec, idx) => (
                <Badge key={idx} variant="secondary" className="text-xs bg-indigo-100 text-indigo-700 dark:bg-indigo-700/30 dark:text-indigo-300">
                  {spec}
                </Badge>
              ))}
            </div>
          )}

          {/* Konuştuğu Diller (İlk 2 tane, Globe2 ikonu ile) */}
          {teacher.spokenLanguages && teacher.spokenLanguages.length > 0 && (
            <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground mb-2">
              <Globe2 className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">
                {teacher.spokenLanguages.slice(0, 2).map(lang => `${lang.language} (${lang.level.charAt(0).toUpperCase()})`).join(", ")}
              </span>
            </div>
          )}

          {/* Kısa Açıklama/Bio (TeacherList.tsx stiline yakın) */}
          <p className="text-sm text-gray-600 dark:text-zinc-400 line-clamp-2 mb-3 min-h-[40px] px-1">
            {teacher.bio || "Deneyimli Almanca öğretmeni."}
          </p>
          
          {/* AI ve Süper Öğretmen Rozetleri */}
          {(isAIRecommended || teacher.isSuperTeacher) && (
            <div className="flex flex-wrap justify-center gap-1.5 mb-3">
              {isAIRecommended && (
                <Badge className="bg-gradient-to-r from-sky-500 to-blue-600 text-white px-2 py-0.5 text-xs font-semibold shadow-sm">
                  ★ AI Önerisi
                </Badge>
              )}
              {teacher.isSuperTeacher && (
                <Badge className="bg-gradient-to-r from-amber-400 to-orange-500 text-white px-2 py-0.5 text-xs font-semibold shadow-sm">
                  Süper Öğretmen <Star className="w-3 h-3 ml-1 inline-block fill-white" />
                </Badge>
              )}
            </div>
          )}
          
          {/* Müsaitlik Durumu */}
          <div className={cn("text-xs font-medium px-2.5 py-1 rounded-full inline-flex items-center gap-1.5 mb-3 shadow-sm", availabilityColorClass)}> {/* mb-4'ten mb-3'e düşürüldü */}
            <CalendarDays className="w-3.5 h-3.5" />
            {availabilityText}
          </div>

          {/* Aktif Öğrenci ve Toplam Ders İstatistikleri */}
          <div className="flex justify-center items-center gap-3 text-xs text-zinc-500 dark:text-zinc-400 mb-3">
            <span className="flex items-center">
              <Users size={12} className="mr-1 text-zinc-400 dark:text-zinc-500"/>
              {teacher.activeStudents || 0} aktif öğrenci
            </span>
            <span className="flex items-center">
              <BookCheck size={12} className="mr-1 text-zinc-400 dark:text-zinc-500"/>
              {teacher.totalLessons || 0} ders
            </span>
          </div>

          {/* Butonlar (TeacherList.tsx stiline yakın) */}
          <div className="w-full mt-auto pt-4 border-t border-zinc-200 dark:border-zinc-700/50">
            <div className="flex justify-center space-x-2">
              <Button
                size="sm"
                variant="outline" // Outline variantı ana sayfadaki gibi
                className="text-sm px-3 py-1.5 h-auto bg-white hover:bg-gray-100 text-gray-900 border-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-500 dark:hover:bg-gray-600" // Beyaz buton, siyah yazı
                onClick={(e) => { e.preventDefault(); e.stopPropagation(); /* console.log("Mesaj gönder tıklandı"); - REMOVED */ }}
              >
                <MessageSquare className="h-4 w-4 mr-1" /> {/* MessageCircle -> MessageSquare olarak düzeltildi */}
                Mesaj
              </Button>
              <Button
                size="sm"
                className="text-sm px-3 py-1.5 h-auto bg-gray-900 hover:bg-gray-700 text-white dark:bg-gray-800 dark:hover:bg-gray-700" // Siyah buton
              >
                <CalendarDays className="h-4 w-4 mr-1" /> {/* İkon CalendarDays olarak değiştirildi, TeacherList.tsx'deki gibi */}
                Ders Al
              </Button>
            </div>
          </div>
          <Button variant="ghost" size="icon" className="absolute top-1 right-1 bg-transparent hover:bg-red-50/50 dark:hover:bg-red-900/30 w-8 h-8 z-10" onClick={handleFavoriteToggle} aria-label="Favorilere ekle">
            <Heart className={cn("w-4 h-4", isFavorite ? "fill-red-500 text-red-500" : "text-zinc-400 dark:text-zinc-500 group-hover:text-red-400")} />
          </Button>
        </CardContent>
      </Card>
    </Link>)
  );
}
