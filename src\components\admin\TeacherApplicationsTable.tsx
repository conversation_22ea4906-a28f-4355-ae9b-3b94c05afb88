// src/components/admin/TeacherApplicationsTable.tsx
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Teacher } from "@prisma/client";
import { getPendingTeacherApplications } from "@/lib/actions/teacher.actions";
import { updateTeacherApprovalStatus } from "@/lib/actions/teacher.actions";
import { formatDate } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { Pagination } from "@/components/ui/pagination";

type TeacherApplicationsTableProps = {
  page: number;
  limit: number;
  search: string;
  totalPages: number;
};

export default function TeacherApplicationsTable({
  page,
  limit,
  search,
  totalPages,
}: TeacherApplicationsTableProps) {
  const [applications, setApplications] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const router = useRouter();
  // const { user } = useUser(); // Clerk kaldırıldı

  // Başvuruları getir
  const fetchApplications = async () => {
    setLoading(true);
    try {
      // TODO: Arama ve sayfalama backend'e taşınmalı. Şimdilik tüm bekleyenleri getiriyoruz.
      const data = await getPendingTeacherApplications();
      setApplications(data);
    } catch (error) {
      console.error("Başvurular getirilirken hata oluştu:", error);
    } finally {
      setLoading(false);
    }
  };

  // Sayfa yüklendiğinde başvuruları getir
  useEffect(() => {
    fetchApplications();
  }, [page, limit, search]);

  // Başvuru onaylama/reddetme işlemi
  const handleUpdateStatus = async (teacherId: string, isApproved: boolean) => {
    // if (!user?.id) { // Clerk kaldırıldı, Supabase ile güncellenecek
    //   alert("Bu işlemi gerçekleştirmek için giriş yapmalısınız.");
    //   return;
    // }

    setProcessingId(teacherId);
    try {
      const result = await updateTeacherApprovalStatus(
        teacherId,
        isApproved
      );

      if (result.success) {
        alert(`Öğretmen başvurusu başarıyla ${
          isApproved ? "onaylandı" : "reddedildi"
        }.`);

        // Başvuru listesini güncelle
        setApplications((prev) =>
          prev.filter((app) => app.id !== teacherId)
        );
        
        // Sayfayı yenile
        router.refresh();
      } else {
        throw new Error("İşlem başarısız oldu");
      }
    } catch (error) {
      // console.error("Başvuru durumu güncellenirken hata oluştu:", error); - REMOVED
      alert("İşlem sırasında bir sorun oluştu.");
    } finally {
      setProcessingId(null);
    }
  };

  if (loading) {
    return <div className="p-4 text-center">Yükleniyor...</div>;
  }

  if (applications.length === 0) {
    return (
      <div className="p-8 text-center">
        <p className="text-muted-foreground">Bekleyen başvuru bulunmuyor.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead className="bg-muted/50">
            <tr>
              <th className="px-4 py-3 text-left font-medium">Öğretmen</th>
              <th className="px-4 py-3 text-left font-medium">Konum</th>
              <th className="px-4 py-3 text-left font-medium">Başvuru Tarihi</th>
              <th className="px-4 py-3 text-left font-medium">Uzmanlık</th>
              <th className="px-4 py-3 text-left font-medium">İşlemler</th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {applications.map((teacher) => (
              <tr key={teacher.id} className="hover:bg-muted/30">
                <td className="px-4 py-3">
                  <div className="font-medium">
                    {teacher.firstName} {teacher.lastName}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {teacher.title || "Unvan belirtilmemiş"}
                  </div>
                </td>
                <td className="px-4 py-3">
                  {teacher.country && teacher.city 
                    ? `${teacher.city}, ${teacher.country}` 
                    : teacher.country || teacher.city || "Konum belirtilmemiş"}
                </td>
                <td className="px-4 py-3">
                  {formatDate(teacher.created_at)}
                </td>
                <td className="px-4 py-3">
                  <div className="flex flex-wrap gap-1">
                    {teacher.specializations.length > 0 ? (
                      teacher.specializations.slice(0, 2).map((spec, i) => (
                        <Badge key={i} variant="outline">
                          {spec}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-xs text-muted-foreground">Belirtilmemiş</span>
                    )}
                    {teacher.specializations.length > 2 && (
                      <Badge variant="outline">+{teacher.specializations.length - 2}</Badge>
                    )}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="default"
                      onClick={() => handleUpdateStatus(teacher.id, true)}
                      disabled={processingId === teacher.id}
                    >
                      {processingId === teacher.id ? "İşleniyor..." : "Onayla"}
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleUpdateStatus(teacher.id, false)}
                      disabled={processingId === teacher.id}
                    >
                      {processingId === teacher.id ? "İşleniyor..." : "Reddet"}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      asChild
                    >
                      <Link href={`/dashboard/list/teacher-applications/${teacher.id}`}>
                        Detaylar
                      </Link>
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Sayfalama */}
      {totalPages > 1 && (
        <div className="py-4 px-4 flex justify-center">
          <Pagination currentPage={page} totalPages={totalPages} />
        </div>
      )}
    </div>
  );
}
