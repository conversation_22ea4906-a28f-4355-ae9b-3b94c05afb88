// src/app/(dashboard)/layout.tsx
"use client";

import { useState, useEffect } from 'react';
import Menu from '@/components/Menu';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Navbar } from '@/components/Navbar';
// Geçici olarak devre dışı: import { useMediaQuery } from '@/hooks/useMediaQuery';

const isMobile = useMediaQuery('(max-width: 768px)');

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMounted, setIsMounted] = useState(false);
  // Geçici olarak devre dışı: const isMobile = useMediaQuery('(max-width: 768px)');
  const isMobile = false; // Geçici sabit değer

  // Sayfa yüklendiğinde mobil cihazlarda sidebar'ı otomatik kapat
  useEffect(() => {
    setIsMounted(true);
    // Geçici olarak devre dışı
    // if (isMobile) {
    //   setIsSidebarOpen(false);
    // } else {
    //   setIsSidebarOpen(true);
    // }
  }, []);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    (<div className="flex h-screen flex-col bg-background">
      {/* Üst Navbar */}
      <Navbar 
        onMenuButtonClick={toggleSidebar} 
        isDashboardLayout={true} 
      />
      <div className="flex flex-1 overflow-hidden mt-0">
        {/* Sol Kenar Çubuğu - Menü */}
        <aside
          className={`
            fixed inset-y-0 left-0 z-30 border-r border-border/40 bg-background/95 backdrop-blur-sm
            pt-0 transition-all duration-300 ease-in-out shadow-sm
            ${isSidebarOpen ? "translate-x-0 w-64" : "-translate-x-full w-64"} 
            md:static md:translate-x-0 md:w-64
          `}
        >
          <div className="flex h-full flex-col overflow-hidden">
            {/* Logo Bölümü */}
            <div className="flex items-center px-4 h-12 border-b border-border/30">
              <Link
                href="/"
                className="flex items-center gap-2 transition-all duration-200">
                {/* İçerik doğrudan Link altında, Link bir <a> render edecek */}
                <div className="relative h-8 w-8 overflow-hidden rounded-md">
                  <Image
                    src="/logo.png"
                    alt="AlmancaABC Logo"
                    width={32}
                    height={32}
                    className="object-cover"
                  />
                </div>
                <span className="font-bold text-base">
                  <span className="text-primary">Almanca</span>ABC
                </span>
              </Link>
            </div>
            
            {/* Menü Bileşeni */}
            <div className="flex-1 overflow-hidden">
              <Menu />
            </div>
            
            {/* Alt Bilgi */}
            <div className="p-2 text-xs text-center text-muted-foreground/70 border-t border-border/30">
              <p className="text-center">©</p>
            </div>
          </div>
        </aside>

        {/* Mobil için overlay */}
        {isMounted && isSidebarOpen && isMobile && (
          <div
            className="fixed inset-0 z-20 bg-black/50 backdrop-blur-sm transition-opacity duration-300 ease-in-out"
            onClick={toggleSidebar}
          ></div>
        )}

        {/* Sağ İçerik Alanı */}
        <main className="flex-1 overflow-y-auto transition-all duration-300 ease-in-out px-4 py-20 md:py-6 md:px-6 lg:px-8">
          <div className="container mx-auto max-w-7xl h-full">
            {/* Sayfa Başlığı */}
            <div className="mb-6">
              <h1 className="text-2xl font-bold tracking-tight">
                {pathname === "/dashboard" ? "Kontrol Paneli" : 
                 pathname.includes("/admin") ? "Yönetim" :
                 pathname.includes("/teacher") ? "Öğretmen Paneli" :
                 pathname.includes("/student") ? "Öğrenci Paneli" :
                 pathname.includes("/parent") ? "Veli Paneli" : "Dashboard"}
              </h1>
              <p className="text-muted-foreground">
                {new Date().toLocaleDateString('tr-TR', { day: 'numeric', month: 'long', year: 'numeric' })}  
              </p>
            </div>
            
            {/* Ana İçerik */}
            <div className="bg-card rounded-lg border border-border/40 shadow-sm overflow-hidden">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>)
  );
}