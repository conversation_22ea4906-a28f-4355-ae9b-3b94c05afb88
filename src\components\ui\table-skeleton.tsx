// src/components/ui/table-skeleton.tsx
import { Skeleton } from "@/components/ui/skeleton";

type TableSkeletonProps = {
  columns: number;
  rows: number;
};

export function TableSkeleton({ columns, rows }: TableSkeletonProps) {
  return (
    <div className="w-full overflow-hidden">
      <div className="bg-muted/50 p-4">
        {/* Tablo başlığı skeleton */}
        <div className="flex gap-4">
          {Array.from({ length: columns }).map((_, i) => (
            <Skeleton key={`header-${i}`} className="h-6 w-32" />
          ))}
        </div>
      </div>
      
      <div className="divide-y">
        {/* Tablo satırları skeleton */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={`row-${rowIndex}`} className="flex items-center p-4 gap-4">
            {Array.from({ length: columns }).map((_, colIndex) => (
              <Skeleton 
                key={`cell-${rowIndex}-${colIndex}`} 
                className={`h-5 ${colIndex === columns - 1 ? 'w-40' : 'w-32'}`} 
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}
