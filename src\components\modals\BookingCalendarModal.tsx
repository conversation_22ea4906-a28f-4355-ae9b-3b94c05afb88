"use client";

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ChevronLeft, ChevronRight, Calendar, CheckCircle } from 'lucide-react'; // Clock kaldırıldı
import { Button } from '@/components/ui/button'; // Güncellendi
import { useToast } from '@/hooks/useToast'; // Güncellendi
import { Teacher } from '@/types/teacher'; // Güncellendi

interface SelectedCourseType {
  title: string;
  price: string; 
}

interface BookingCalendarModalProps { // Güncellendi
  onClose: () => void;
  selectedCourse: SelectedCourseType | null;
  teacher: Teacher | null;
  onBookingSuccess: () => void;
}

export function BookingCalendarModal({ onClose, selectedCourse, teacher, onBookingSuccess }: BookingCalendarModalProps) { // Güncellendi
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [step, setStep] = useState(1); // 1: Select date, 2: Select time, 3: Confirm
  const { toast } = useToast();
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    }

    document.body.classList.add('modal-open-background-scroll');
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.body.classList.remove('modal-open-background-scroll');
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // Modal overlay'e tıklayınca kapanması için
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDay = firstDay.getDay();
    
    const days = [];
    
    const startDayIndex = startingDay === 0 ? 6 : startingDay -1;
    for (let i = 0; i < startDayIndex; i++) {
      const prevDate = new Date(year, month, 0 - (startDayIndex - 1 - i));
      days.push({ date: prevDate, isCurrentMonth: false });
    }
    
    for (let day = 1; day <= daysInMonth; day++) {
      const currentDate = new Date(year, month, day); // Değişken adı düzeltildi
      days.push({ date: currentDate, isCurrentMonth: true });
    }
    
    const totalDaysDisplayed = 42; 
    const nextDaysCount = totalDaysDisplayed - days.length;
    for (let day = 1; day <= nextDaysCount; day++) {
      const nextDate = new Date(year, month + 1, day);
      days.push({ date: nextDate, isCurrentMonth: false });
    }
    
    return days;
  };

  const isPastDate = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  const isUnavailable = (date: Date) => {
    const day = date.getDay();
    return day === 0 || (day === 2 && date.getDate() % 7 === 0) || (day === 5 && date.getDate() % 5 === 0);
  };

  const formatDateHeader = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = { month: 'long', year: 'numeric' };
    return date.toLocaleDateString('tr-TR', options);
  };

  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = { day: 'numeric', month: 'long', year: 'numeric' };
    return date.toLocaleDateString('tr-TR', options);
  };

  const availableTimes = ['09:00', '10:30', '13:00', '14:30', '16:00', '17:30', '19:00', '20:30'];

  const prevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };

  const nextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
    setStep(3); // Onay adımına geç
  };

  const handleDateSelect = (date: Date) => {
    if (isPastDate(date) || isUnavailable(date)) return;
    setSelectedDate(date);
    setSelectedTime(null); 
  };

  const handleBooking = async () => {
    if (!selectedDate || !selectedTime || !selectedCourse || !teacher) {
      toast({
        title: "Hata!",
        description: "Lütfen tüm bilgileri doldurun.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Rezervasyon onay modalı göster
      const confirmed = window.confirm(
        `${teacher.name} ile ${selectedCourse.title} kursu için ${formatDate(selectedDate)} ${selectedTime} tarihli randevunuzu onaylıyor musunuz?`
      );

      if (!confirmed) return;

      // Burada gerçek API çağrısı yapılacak
      // const booking = await createBooking({
      //   teacherId: teacher.id,
      //   courseId: selectedCourse.id,
      //   date: selectedDate,
      //   time: selectedTime
      // });

      toast({
        title: "Rezervasyon Onaylandı!",
        description: `${teacher.name} ile ${selectedCourse.title} için ${formatDate(selectedDate)} ${selectedTime} tarihli randevunuz oluşturuldu.`,
        duration: 5000,
      });

      onClose();
      onBookingSuccess();
    } catch (error) {
      toast({
        title: "Hata!",
        description: "Rezervasyon oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.",
        variant: "destructive",
      });
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/40 backdrop-blur-sm z-50 flex items-center justify-center p-4 no-transform"
      onClick={handleOverlayClick}
    >
      <AnimatePresence>
        <motion.div
          ref={modalRef}
          className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto relative no-transform"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.3 }}
        >
          <Button 
            className="absolute top-4 right-4 rounded-full p-2 h-8 w-8"
            variant="ghost"
            size="icon"
            onClick={onClose}
          >
            <X className="w-4 h-4" />
          </Button>
          
          <div className="p-4 border-b">
            <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
              <Calendar className="w-5 h-5 text-blue-500" />
              {step === 3 ? 'Randevuyu Onaylayın' : 'Tarih ve Saat Seçin'}
            </h3>
            
            {selectedCourse && (
              <div className="mt-2 text-sm text-gray-600">
                Seçilen kurs: <span className="font-medium text-blue-600">{selectedCourse.title}</span>
              </div>
            )}
          </div>

          {step === 1 && (
            <div className="p-4">
              <div className="flex justify-between items-center mb-3">
                <Button variant="ghost" onClick={prevMonth} className="hover:bg-blue-50 p-2">
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <h4 className="text-lg font-bold text-gray-900 capitalize">
                  {formatDateHeader(currentMonth)}
                </h4>
                <Button variant="ghost" onClick={nextMonth} className="hover:bg-blue-50 p-2">
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
              
              <div className="grid grid-cols-7 gap-1 mb-2">
                {['Pt', 'Sa', 'Ça', 'Pe', 'Cu', 'Ct', 'Pz'].map((day, i) => (
                  <div key={i} className="text-center text-xs font-medium text-gray-500 py-1">
                    {day}
                  </div>
                ))}
              </div>
              
              <div className="grid grid-cols-7 gap-1">
                {getDaysInMonth(currentMonth).map((dayObj, index) => { // day -> dayObj
                  const isPast = isPastDate(dayObj.date);
                  const unavailable = isUnavailable(dayObj.date);
                  const today = new Date();
                  const isToday = dayObj.date.toDateString() === today.toDateString();
                  const isSelected = selectedDate && dayObj.date.toDateString() === selectedDate.toDateString();
                  const isClickable = dayObj.isCurrentMonth && !isPast && !unavailable;

                  return (
                    <motion.button
                      key={index}
                      disabled={!isClickable}
                      onClick={() => isClickable && handleDateSelect(dayObj.date)}
                      className={`
                        py-2 rounded-lg relative text-sm font-medium transition-all duration-200 h-8 w-8 flex items-center justify-center
                        ${!dayObj.isCurrentMonth ? 'text-gray-300' : ''}
                        ${isPast && dayObj.isCurrentMonth ? 'text-gray-400 opacity-50 cursor-not-allowed' : ''}
                        ${unavailable && dayObj.isCurrentMonth && !isPast ? 'text-gray-400 cursor-not-allowed line-through' : ''}
                        ${isClickable && !isSelected ? 'text-gray-700 hover:bg-blue-50 hover:text-blue-600 cursor-pointer' : ''}
                        ${isSelected ? 'bg-blue-500 text-white shadow-md' : ''}
                        ${isToday && !isSelected && dayObj.isCurrentMonth && !isPast && !unavailable ? 'ring-2 ring-blue-400 bg-blue-50 text-blue-600' : ''}
                      `}
                      whileHover={isClickable ? { scale: 1.1 } : {}}
                      whileTap={isClickable ? { scale: 0.95 } : {}}
                    >
                      <span className={`${isPast && dayObj.isCurrentMonth ? 'line-through' : ''} relative z-10`}>
                        {dayObj.date.getDate()}
                      </span>
                      
                    </motion.button>
                  );
                })}
              </div>

              {selectedDate && step === 1 && !isPastDate(selectedDate) && !isUnavailable(selectedDate) && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="text-sm font-semibold text-gray-900 mb-2 text-center">
                    {formatDate(selectedDate)} - Uygun Saatler:
                  </h4>
                  <div className="grid grid-cols-4 gap-2">
                    {availableTimes.map((time, index) => (
                      <motion.button
                        key={index}
                        onClick={() => handleTimeSelect(time)}
                        className={`
                          py-2 px-2 rounded text-center text-xs font-medium transition-all duration-200
                          ${selectedTime === time
                            ? 'bg-blue-500 text-white shadow-sm'
                            : 'bg-white border border-gray-200 text-gray-700 hover:border-blue-300 hover:bg-blue-50'
                          }
                        `}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {time}
                      </motion.button>
                    ))}
                  </div>
                  <div className="mt-3 flex justify-center">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedDate(null);
                        setSelectedTime(null);
                      }}
                      className="bg-white hover:bg-gray-50 text-xs"
                    >
                      <ChevronLeft className="w-3 h-3 mr-1" />
                      Tarihi Değiştir
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {step === 3 && selectedDate && selectedTime && (
            <div className="p-4">
              <div className="bg-blue-50 p-4 rounded-xl mb-4">
                <div className="flex justify-between items-center mb-3">
                  <span className="text-gray-700 font-medium">Tarih:</span>
                  <span className="text-blue-700">{formatDate(selectedDate)}</span>
                </div>
                <div className="flex justify-between items-center mb-3">
                  <span className="text-gray-700 font-medium">Saat:</span>
                  <span className="text-blue-700">{selectedTime}</span>
                </div>
                {selectedCourse && (
                  <>
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-gray-700 font-medium">Kurs:</span>
                      <span className="text-blue-700">{selectedCourse.title}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-700 font-medium">Ücret:</span>
                      <span className="text-blue-700">{selectedCourse.price}</span>
                    </div>
                  </>
                )}
              </div>
              
              <div className="flex justify-between">
                <Button 
                  variant="outline"
                  onClick={() => {
                    setSelectedTime(null);
                    setStep(1); 
                  }}
                >
                  <ChevronLeft className="w-4 h-4 mr-1" />
                  Saati Değiştir
                </Button>
                
                <Button
                  onClick={handleBooking}
                  disabled={!selectedCourse} 
                  className={`bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 ${!selectedCourse ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Randevuyu Onayla
                </Button>
              </div>
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
