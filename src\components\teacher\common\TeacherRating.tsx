'use client';

import React from 'react';
import { Star, Users } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface TeacherRatingProps {
  rating: number;
  reviewCount?: number;
  showReviewCount?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'compact' | 'detailed';
}

const sizeClasses = {
  sm: { star: 'w-3 h-3', text: 'text-xs' },
  md: { star: 'w-4 h-4', text: 'text-sm' },
  lg: { star: 'w-5 h-5', text: 'text-base' }
};

export const TeacherRating: React.FC<TeacherRatingProps> = ({
  rating,
  reviewCount = 0,
  showReviewCount = true,
  size = 'md',
  variant = 'default'
}) => {
  const renderStars = () => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Star
            key={i}
            className={`${sizeClasses[size].star} text-yellow-400 fill-yellow-400`}
          />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative">
            <Star className={`${sizeClasses[size].star} text-gray-300`} />
            <Star
              className={`${sizeClasses[size].star} text-yellow-400 fill-yellow-400 absolute top-0 left-0`}
              style={{ clipPath: 'inset(0 50% 0 0)' }}
            />
          </div>
        );
      } else {
        stars.push(
          <Star
            key={i}
            className={`${sizeClasses[size].star} text-gray-300`}
          />
        );
      }
    }
    
    return stars;
  };

  if (variant === 'compact') {
    return (
      <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
        <Star className="w-3 h-3 mr-1 fill-yellow-400 text-yellow-400" />
        {rating.toFixed(1)}
      </Badge>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className="flex flex-col items-start gap-1">
        <div className="flex items-center gap-1">
          {renderStars()}
          <span className={`font-semibold text-gray-900 ml-1 ${sizeClasses[size].text}`}>
            {rating.toFixed(1)}
          </span>
        </div>
        {showReviewCount && reviewCount > 0 && (
          <div className="flex items-center gap-1 text-gray-500">
            <Users className="w-3 h-3" />
            <span className="text-xs">{reviewCount} değerlendirme</span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center gap-1">
      {renderStars()}
      <span className={`font-semibold text-gray-900 ml-1 ${sizeClasses[size].text}`}>
        {rating.toFixed(1)}
      </span>
      {showReviewCount && reviewCount > 0 && (
        <span className={`text-gray-500 ml-1 ${sizeClasses[size].text}`}>
          ({reviewCount})
        </span>
      )}
    </div>
  );
};