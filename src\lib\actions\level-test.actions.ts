'use server'

import { Resend } from 'resend'

const resend = new Resend(process.env.RESEND_API_KEY || 're_ZsBC8Mji_4iVQ3gvbtfemZbA83oF7nSaJ')

export async function sendLevelTestEmail(email: string) {
  try {
    const data = await resend.emails.send({
      from: 'AlmancaABC <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: 'Seviye Tespit Sınavı Başvurusu',
      html: `<p>Seviye Tespit Sınavı için başvuran e-posta: ${email}</p>`
    });
    // Removed console.log - return success result instead
    return { success: true };
  } catch (error) {
    // Removed console.error - return error result with appropriate message
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'E-posta gönderilirken bir hata oluştu'
    };
  }
}
