// src/app/(dashboard)/student/page.tsx
import { StudentUpcomingLessonsCard } from "@/components/dashboard/StudentUpcomingLessonsCard";
import { StudentTeachersList } from "@/components/dashboard/StudentTeachersList";
import { StudentPastLessons } from "@/components/dashboard/StudentPastLessons"; // Yeni bileşen import edildi
// Kullanılmayan importlar kaldırıldı: Card, CardContent, CardHeader, CardTitle, BookOpenCheck

export default function StudentDashboardPage() {
  return (
    <div className="flex flex-col gap-6">
      <h1 className="text-2xl font-semibold">Öğrenci Paneli</h1>

      {/* Widget'lar */}
      <div className="grid gap-6 lg:grid-cols-3"> {/* Layout düzenlendi */}

         {/* Ya<PERSON><PERSON><PERSON> Kartı (Daha geniş alan kaplasın) */}
         <div className="lg:col-span-2">
            <StudentUpcomingLessonsCard />
         </div>

         {/* Di<PERSON>er Kartlar */}
         <div className="space-y-6">
             <StudentTeachersList limit={3} />
             <StudentPastLessons limit={3} /> {/* Ders geçmişi bileşeni eklendi */}
         </div>
      </div>
    </div>
  );
}