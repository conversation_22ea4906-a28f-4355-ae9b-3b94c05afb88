'use client'; // Form etkileşimleri için client component olması gerekiyor

import React, { useEffect } from 'react'; // useActionState kaldırıldı, useEffect eklendi
import Link from 'next/link'; // Link importu eklendi
import { useForm, Controller } from 'react-hook-form'; // react-hook-form importları
import { zodResolver } from '@hookform/resolvers/zod'; // Zod resolver importu
import { contactFormSchema, ContactFormValues } from '@/lib/formValidationSchemas'; // Zod şeması import edildi (contactFormSchema eklendi)
// import { Metadata } from 'next'; // Metadata hala sayfa seviyesinde tanımlanabilir
import { Send, MapPin, Check, AlertCircle, HelpCircle } from 'lucide-react'; // HelpCircle importu eklendi, Kullanılmayan sosyal medya ikonları kaldırıldı
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
// import { useFormStatus } from 'react-dom'; // react-hook-form'un isSubmitting'i kullanılacak
import { sendContactMessage } from '@/lib/actions/contact.actions'; // Server Action import edildi
import { IletisimBilgileriKarti } from '@/components/IletisimBilgileriKarti'; // Yeni kartı import et
import { SosyalMedyaKarti } from '@/components/SosyalMedyaKarti'; // Sosyal medya kartını import et
// import FaqAccordion from '@/components/FaqAccordion'; // Kullanılmadığı için kaldırıldı
// ContactFormValues zaten yukarıda import edildi
import { z } from 'zod';

// Metadata'yı sayfa component'inin dışına taşıdık, çünkü 'use client' ile birlikte doğrudan export edilemez.
// Bu metadata'yı layout.tsx veya parent server component üzerinden uygulamak gerekebilir
// veya Next.js'in generateMetadata fonksiyonu kullanılabilir.
// Şimdilik burada bırakıyorum ama build sırasında uyarı verebilir.
// En iyi pratik, 'use client' olan sayfalarda metadata'yı generateMetadata ile yönetmektir.
/*
export const metadata: Metadata = {
  title: 'İletişim | AlmancaABC - Almanca Öğrenin',
  description: 'AlmancaABC ile iletişime geçin. Almanca kursları, özel dersler ve platformumuzla ilgili tüm sorularınız için bize ulaşın.',
  keywords: 'AlmancaABC iletişim, Almanca kursu, Almanca özel ders, Almanca öğren, bize ulaşın, Almanca destek, SSS',
  alternates: {
    canonical: '/iletisim',
  },
  openGraph: {
    title: 'AlmancaABC | İletişim ve Destek',
    description: 'Almanca öğrenme yolculuğunuzda sorularınız mı var veya desteğe mi ihtiyacınız var? AlmancaABC ekibiyle iletişime geçin.',
    url: 'https://almancaabc.com/iletisim', // Gerçek URL ile güncelleyin
    siteName: 'AlmancaABC',
    images: [
      {
        url: 'https://almancaabc.com/og-iletisim-v2.png', // Yeni OG görseli
        width: 1200,
        height: 630,
        alt: 'AlmancaABC İletişim Sayfası',
      },
    ],
    locale: 'tr_TR',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AlmancaABC İletişim | Sorularınız İçin Buradayız',
    description: 'AlmancaABC platformu hakkında merak ettikleriniz veya destek talepleriniz için bize ulaşın. Uzman ekibimiz size yardımcı olmaktan mutluluk duyar.',
    images: ['https://almancaabc.com/twitter-iletisim-v2.png'], // Yeni Twitter görseli
  },
};
*/

const contactPageSchemaOrg = {
  "@context": "https://schema.org",
  "@type": "ContactPage",
  "name": "İletişim | AlmancaABC - Almanca Öğrenin",
  "description": "AlmancaABC ile kolayca iletişime geçin. Almanca kursları, özel dersler ve diğer tüm sorularınız için bize yazın veya diğer iletişim kanallarımızı kullanın.",
  "url": "https://almancaabc.com/iletisim",
  "inLanguage": "tr-TR",
  "keywords": "AlmancaABC iletişim, Almanca kursu, Almanca özel ders, Almanca öğren, bize ulaşın, Almanca destek, SSS, Almanca iletişim formu, AlmancaABC destek",
  "datePublished": "2024-01-01T08:00:00+00:00", // Sitenizin veya bu sayfanın yaklaşık ilk yayınlanma tarihi
  "dateModified": "2025-05-18T00:00:00+00:00",
  "lastReviewed": "2025-05-18T00:00:00+00:00",
  "primaryImageOfPage": {
    "@type": "ImageObject",
    "url": "https://almancaabc.com/og-iletisim-v2.png", // OpenGraph için kullanılan görsel
    "width": 1200,
    "height": 630,
    "caption": "AlmancaABC İletişim Sayfası"
  },
  "breadcrumb": {
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Ana Sayfa",
        "item": "https://almancaabc.com"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "İletişim",
        "item": "https://almancaabc.com/iletisim"
      }
    ]
  },
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://almancaabc.com/iletisim"
  },
  "mainEntity": { // Sayfanın ana konusu olan organizasyon
    "@type": "Organization",
    "name": "AlmancaABC",
    "url": "https://almancaabc.com",
    "logo": "https://almancaabc.com/logo.png",
    "description": "Almanca öğrenmeyi herkes için erişilebilir ve etkili hale getiren online dil öğrenme platformu.",
    "contactPoint": [
      {
        "@type": "ContactPoint",
        "telephone": "+491784441011",
        "contactType": "customer support",
        "contactOption": "TollFree", // Örnek, eğer ücretsizse
        "areaServed": ["TR", "DE"], // Hizmet verilen ülkeler (Türkiye, Almanya)
        "availableLanguage": ["Turkish", "German"],
        "email": "<EMAIL>",
        "name": "AlmancaABC Müşteri Hizmetleri"
      }
    ]
  },
  "publisher": {
    "@type": "Organization",
    "name": "AlmancaABC",
    "url": "https://almancaabc.com",
    "logo": {
      "@type": "ImageObject",
      "url": "https://almancaabc.com/logo.png"
    }
  },
  "potentialAction": {
    "@type": "CommunicateAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": "https://almancaabc.com/iletisim" // Formun gönderildiği URL
    },
    "actionPlatform": [
      "http://schema.org/DesktopWebPlatform",
      "http://schema.org/IOSPlatform",
      "http://schema.org/AndroidPlatform"
    ],
    "object": { // Bu ContactPoint, form aracılığıyla ulaşılan iletişim noktası
      "@type": "ContactPoint",
      "email": "<EMAIL>", // Formun gittiği e-posta
      "contactType": "customer support", // Formun amacı
      "name": "AlmancaABC İletişim Formu",
      "description": "AlmancaABC platformuyla ilgili soru ve önerilerinizi bu form üzerinden iletebilirsiniz.",
      "availableLanguage": ["Turkish", "German"]
    }
  },
  "about": { // Sayfanın genel olarak ne hakkında olduğu
    "@type": "Organization",
    "name": "AlmancaABC",
    "description": "Almanca öğrenmeyi kolay ve erişilebilir hale getiren online bir platform.",
    "url": "https://almancaabc.com",
    "sameAs": [ // Sosyal medya ve diğer resmi bağlantılar
      "https://www.facebook.com/almancaabc/",
      "https://www.instagram.com/almancaabc/",
      "https://twitter.com/AlmancaABC",
      "https://www.youtube.com/@AlmancaABC",
      "https://www.tiktok.com/@almancaabc",
      "https://www.whatsapp.com/channel/0029Va7U3OEIyPtT3ljHfm2c",
      "https://telegram.me/AlmancaABC"
    ]
  }
};

// Bu faqs array'i artık FaqAccordion component'i içinde tanımlı olduğu için buradan kaldırılabilir.
// const faqs = [ ... ];

const initialState = {
  success: false,
  message: null as string | null,
  error: null as string | null,
  errors: [] as z.ZodIssue[],
};

function SubmitButton({ isSubmitting }: { isSubmitting: boolean }) { // isSubmitting prop'u eklendi
  // const { pending } = useFormStatus(); // Kaldırıldı
  return (
    <Button type="submit" disabled={isSubmitting} size="lg">
      {isSubmitting ? (
        <>
          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Gönderiliyor...
        </>
      ) : (
        <>
          Mesajı Gönder <Send className="ml-2 h-5 w-5" />
        </>
      )}
    </Button>
  );
}

// Bu bileşeni ana sayfada da kullanabilmek için export ediyoruz.
export const ModernContactForm = () => {
  const [showSuccessMessage, setShowSuccessMessage] = React.useState(false);
  const [actionResponse, formAction] = React.useActionState(sendContactMessage, initialState); // useActionState korundu

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    reset
    // watch // Kullanılmadığı için kaldırıldı
  } = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: '',
      privacyPolicyAccepted: false,
    },
  });

  useEffect(() => {
    if (actionResponse.success) {
      setShowSuccessMessage(true);
      reset(); // react-hook-form ile formu sıfırla
      const timer = setTimeout(() => {
        setShowSuccessMessage(false);
      }, 5000);
      return () => clearTimeout(timer);
    } else if (actionResponse.errors && actionResponse.errors.length > 0) {
      actionResponse.errors.forEach((err) => {
        if (err.path && err.path.length > 0) {
          setError(err.path[0] as keyof ContactFormValues, {
            type: 'server',
            message: err.message,
          });
        }
      });
    }
  }, [actionResponse, reset, setError]);

  const processForm = async (data: ContactFormValues) => {
    const formData = new FormData();
    (Object.keys(data) as Array<keyof ContactFormValues>).forEach((key) => {
        const value = data[key];
        if (typeof value === 'boolean') {
            formData.append(key, value ? 'on' : ''); // Checkbox için 'on' veya boş string
        } else if (value !== null && value !== undefined) {
            formData.append(key, String(value));
        }
    });
    React.startTransition(() => {
      formAction(formData);
    });
  };

  if (showSuccessMessage) {
    return (
      <div className="h-full flex flex-col items-center justify-center text-center p-8 bg-green-50 dark:bg-green-900/20 rounded-lg">
        <div className="bg-green-100 dark:bg-green-800/30 w-16 h-16 rounded-full flex items-center justify-center mb-4">
          <Check className="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-2">Teşekkürler!</h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">{actionResponse.message || "Mesajınız başarıyla gönderildi."}</p>
        <Button onClick={() => setShowSuccessMessage(false)} variant="outline">Yeni Mesaj Gönder</Button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(processForm)} className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Bize Mesaj Gönderin</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="name" className="mb-1 block">Adınız Soyadınız *</Label>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                type="text"
                id="name"
                className={errors.name ? 'border-red-500' : ''}
                placeholder="Adınız ve Soyadınız"
              />
            )}
          />
          {errors.name && <p className="mt-1 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.name.message}</p>}
        </div>
        <div>
          <Label htmlFor="email" className="mb-1 block">E-posta Adresiniz *</Label>
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                type="email"
                id="email"
                className={errors.email ? 'border-red-500' : ''}
                placeholder="<EMAIL>"
              />
            )}
          />
          {errors.email && <p className="mt-1 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.email.message}</p>}
        </div>
      </div>
      <div>
        <Label htmlFor="phone" className="mb-1 block">Telefon Numaranız (İsteğe Bağlı)</Label>
        <Controller
          name="phone"
          control={control}
          render={({ field }) => (
            <Input
              {...field}
              type="tel"
              id="phone"
              className={errors.phone ? 'border-red-500' : ''}
              placeholder="+90 5XX XXX XX XX"
            />
          )}
        />
        {errors.phone && <p className="mt-1 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.phone.message}</p>}
      </div>
      <div>
        <Label htmlFor="subject" className="mb-1 block">Konu *</Label>
        <Controller
          name="subject"
          control={control}
          render={({ field }) => (
            <Input
              {...field}
              type="text"
              id="subject"
              className={errors.subject ? 'border-red-500' : ''}
              placeholder="Mesajınızın konusu"
            />
          )}
        />
        {errors.subject && <p className="mt-1 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.subject.message}</p>}
      </div>
      <div>
        <Label htmlFor="message" className="mb-1 block">Mesajınız *</Label>
        <Controller
          name="message"
          control={control}
          render={({ field }) => (
            <Textarea
              {...field}
              id="message"
              rows={5}
              className={errors.message ? 'border-red-500' : ''}
              placeholder="Bize iletmek istediğiniz mesaj..."
            />
          )}
        />
        {errors.message && <p className="mt-1 text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.message.message}</p>}
      </div>
      <div className="flex items-start space-x-2">
        <Controller
            name="privacyPolicyAccepted"
            control={control}
            render={({ field }) => (
                <Checkbox
                    id="privacyPolicyAccepted"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    className={errors.privacyPolicyAccepted ? 'border-red-500' : ''}
                />
            )}
        />
        <div className="grid gap-1.5 leading-none">
          <Label htmlFor="privacyPolicyAccepted" className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${errors.privacyPolicyAccepted ? 'text-red-600' : ''}`}>
            <Link href="/gizlilik-politikasi" className="text-blue-600 hover:underline dark:text-blue-400" target="_blank" rel="noopener noreferrer">Gizlilik Politikası</Link>okudum ve kabul ediyorum. *
          </Label>
          {errors.privacyPolicyAccepted && <p className="text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{errors.privacyPolicyAccepted.message}</p>}
        </div>
      </div>
      {actionResponse.error && !actionResponse.errors?.length && <p className="text-sm text-red-600 flex items-center"><AlertCircle className="h-4 w-4 mr-1" />{actionResponse.error}</p>}
      <div className="flex justify-end">
        <SubmitButton isSubmitting={isSubmitting} />
      </div>
    </form>
  );
};


const IletisimPage = () => {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(contactPageSchemaOrg) }}
      />
      {/* 
        'use client' kullanıldığı için metadata bu şekilde export edilemez.
        Bu component server component ise metadata export edilebilir.
        Eğer bu sayfa client component olarak kalacaksa, metadata'yı
        parent layout.tsx veya page.tsx (server component ise) üzerinden
        generateMetadata fonksiyonu ile sağlamak daha doğru olur.
        Ya da bu IletisimPage component'ini bir Server Component yapıp,
        ModernContactForm'u ayrı bir Client Component olarak import edebilirsiniz.
        Şimdilik metadata'yı yorum satırına alıyorum.
      */}
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-sky-100 dark:from-slate-900 dark:to-sky-900 py-8 px-4 sm:px-6 lg:px-8"> {/* py-12'den py-8'e düşürüldü */}
        <div className="max-w-6xl mx-auto">
          <header className="text-center mb-8 md:mb-10"> {/* mb-12 md:mb-16 dan mb-8 md:mb-10'a düşürüldü */}
            <h1 className="text-[50px] font-extrabold tracking-tight text-gray-900 dark:text-gray-50">
              İletişime Geçin
            </h1>
            <p className="mt-4 text-base text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Almanca öğrenme hedeflerinize ulaşmanızda size nasıl yardımcı olabileceğimizi öğrenmek için buradayız. Sorularınız, önerileriniz veya işbirliği talepleriniz için bize ulaşmaktan çekinmeyin.
            </p>
          </header>

          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl overflow-hidden">
            <div className="grid grid-cols-1 lg:grid-cols-5">
              {/* İletişim Bilgileri Bölümü */}
              <IletisimBilgileriKarti />
              
              {/* Form Bölümü */}
              <div className="p-6 md:p-8 lg:col-span-3"> {/* p-8 md:p-10 dan p-6 md:p-8 e düşürüldü */}
                <ModernContactForm />
                {/* "Bizi Takip Edin" bölümü buradan kaldırıldı, sayfanın altına taşınacak */}
              </div>
            </div>
          </div>
          
          {/* SSS Bölümü - FaqAccordion component'i ile değiştirildi */}
          {/* Bu bölüm kaldırılıyor */}
          {/*
          <section className="mt-16 md:mt-24">
            <div className="max-w-4xl mx-auto">
              <FaqAccordion />
            </div>
          </section>
          */}

          {/* Yardım Merkezi Linki */}
          <section className="mt-12 text-center py-8 bg-slate-100 dark:bg-slate-800/60 rounded-lg shadow-md">
            <HelpCircle className="h-10 w-10 mx-auto mb-3 text-sky-600 dark:text-sky-400" />
            <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2">
              Daha Fazla Yardıma mı İhtiyacınız Var?
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4 max-w-xl mx-auto">
              Sıkça sorulan sorular arasında cevabınızı bulamadıysanız, kapsamlı yardım kaynaklarımız için lütfen Yardım Merkezimizi ziyaret edin.
            </p>
            <Button asChild size="lg">
              <Link href="/yardim">
                Yardım Merkezi&apos;ne Git
              </Link>
            </Button>
          </section>

          {/* Harita Bölümü (İsteğe Bağlı) */}
          <section className="mt-16 md:mt-24">
             <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-gray-50 mb-10 md:mb-12">
              AlmancaABC Konum
            </h2>
            <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden">
              <div className="h-96 bg-gray-200 dark:bg-slate-700 relative">
                {/* Buraya Google Haritalar veya benzeri bir harita entegrasyonu eklenebilir */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center p-6">
                    <MapPin className="h-12 w-12 mx-auto text-slate-600 dark:text-slate-500 mb-2" />
                    <p className="text-gray-500 dark:text-gray-400">İnteraktif harita burada gösterilecek.</p>
                    <p className="text-sm text-gray-400 dark:text-gray-500">(Google Maps API anahtarı gereklidir)</p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Bizi Takip Edin Bölümü - Yeni Konum */}
          <section className="mt-16 md:mt-24">
            <div className="max-w-3xl mx-auto">
              <SosyalMedyaKarti className="bg-gray-50 dark:bg-slate-800/50 shadow-xl" />
            </div>
          </section>

        </div>
      </div>
    </>
  );
};

export default IletisimPage;