// src/components/dashboard/RecentActivitiesCard.tsx
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
// import { Button } from "@/components/ui/button"; // Şimdilik kullanılmıyor
import { ActivityLog } from "@prisma/client"; // Prisma tipini import et
import { getRecentActivities } from "@/lib/actions/admin.actions";
import { formatDistanceToNow } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Activity as ActivityIcon } from "lucide-react"; // İkonu yeniden adlandırarak import et
// import Link from "next/link"; // Şimdilik kullanılmıyor

// Aktivite açıklamalarını daha okunabilir hale getiren yardımcı fonksiyon (opsiyonel)
function formatActivityDescription(activity: ActivityLog): string {
    switch (activity.actionType) {
        case 'TEACHER_APPROVED':
            return `<PERSON><PERSON><PERSON><PERSON> onaylandı: ${activity.entityId?.substring(0, 8)}...`; // ID'yi kısaltabiliriz
        case 'BOOKING_CREATED':
            return `Yeni rezervasyon oluşturuldu: #${activity.entityId?.substring(0, 6)}...`;
        case 'USER_REGISTERED':
             return `Yeni kullanıcı kaydoldu: ${activity.actorId?.substring(0,8)}...`; // Veya email/isim?
        // Diğer actionType'lar için case'ler eklenebilir
        default:
            return `${activity.actionType} (Detay: ${activity.entityId ?? 'Yok'})`;
    }
}

export async function RecentActivitiesCard() {
  // Yetkilendirme kontrolü burada yapılmalı, çünkü bu bir Server Component
  // ve doğrudan action'ı çağırıyor. verifyAdmin'i import edip kullanabiliriz.
  // Ancak şimdilik action içinde yapıldığını varsayıyoruz.
  const recentActivities: ActivityLog[] = await getRecentActivities(5); // Son 5 aktivite

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
           <ActivityIcon className="h-5 w-5" />
           Son Aktiviteler
        </CardTitle>
        <CardDescription>Platformdaki son hareketler.</CardDescription>
      </CardHeader>
      <CardContent>
        {recentActivities.length === 0 ? (
          <p className="text-sm text-muted-foreground">Görüntülenecek aktivite yok.</p>
        ) : (
          <ul className="space-y-3">
            {recentActivities.map((activity) => (
              <li key={activity.id} className="flex items-center justify-between text-sm">
                <span className="truncate">{formatActivityDescription(activity)}</span>
                <span className="text-xs text-muted-foreground whitespace-nowrap">
                  {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true, locale: tr })}
                </span>
              </li>
            ))}
          </ul>
        )}
        {/* TODO: Tüm aktiviteleri gösteren bir link eklenebilir */}
         {/* <Button asChild size="sm" className="mt-4 w-full" variant="outline">
             <Link href="/admin/activity-log">Tüm Aktiviteleri Gör</Link>
         </Button> */}
      </CardContent>
    </Card>
  );
}