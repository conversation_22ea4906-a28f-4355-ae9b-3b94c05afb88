{"mcpServers": {"github.com/supabase-community/supabase-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--read-only", "--project-ref=vqsmbykhbxbomaruinct"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}, "disabled": false, "autoApprove": [], "alwaysAllow": ["list_projects", "get_project", "list_tables"]}, "github.com/mendableai/firecrawl-mcp-server": {"command": "cmd", "args": ["/c", "npx", "-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-02fe94601eee4c61b331c0aef4d56ceb"}, "disabled": false, "autoApprove": []}, "github.com/lharries/whatsapp-mcp": {"command": "uv", "args": ["--directory", "C:/Users/<USER>/Documents/Cline/MCP/whatsapp-mcp/whatsapp-mcp-server", "run", "main.py"], "disabled": true, "autoApprove": []}, "github.com/zcaceres/fetch-mcp": {"command": "node", "args": ["C:/Users/<USER>/Documents/Cline/MCP/fetch-mcp/dist/index.js"], "disabled": false, "autoApprove": []}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:/Users/<USER>"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAVodMU5V6yW4KPfpKSAZIR_uGr5Zv"}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "github": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "-e", "GITHUB_TOOLSETS", "-e", "GITHUB_READ_ONLY", "ghcr.io/github/github-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************", "GITHUB_TOOLSETS": "", "GITHUB_READ_ONLY": ""}}, "git": {"command": "uvx", "args": ["mcp-server-git", "--repository", "snahmetkaya/almancaabc"]}}}