# AlmancaABC - Online Almanca Öğrenme Platformu

**AlmancaABC**, sadece Almanca diline odaklanarak Türk ve Alman pazarlarındaki öğretmenler ile öğrencileri buluşturan, modern, kullanıcı dostu ve etkili bir online eğitim platformudur. Amacımız, can<PERSON><PERSON> dersler, ek hizmetler ve güçlü bir topluluk ile Almanca öğreniminde lider bir ekosistem yaratmaktır.

**Title:** "AlmancaABC: Online Almanca Kursu & Özel Ders Öğretmeni Seç"
**Meta Description:** "AlmancaABC ile online Almanca dil kursları ve özel derslerle Almanca öğren! Uzman öğretmenler, esnek programlar ve uygun fiyatlar. Hemen başla!"

*(Detaylı vizyon, strateji ve yol haritası için [AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md](./AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md) dosyasına bakınız.)*

## Temel Özellikler (MVP ve Sonrası)

*   **Öğretmen-Öğrenci Eşleştirme**: <PERSON>ş alanda uzman öğretmenlerle öğrencileri buluşturma.
*   **Canlı Dersler**: Birebir ve grup dersleri için Zoom entegrasyonu.
*   **Takvim ve Randevu Yönetimi**: FullCalendar ile modern öğretmen müsaitlik ve öğrenci rezervasyon sistemi.
*   **Ödeme Sistemi**: Stripe Connect ile güvenli ders ödemeleri ve otomatik komisyon yönetimi.
*   **Profil Yönetimi**: Detaylı öğretmen ve öğrenci profilleri.
*   **Değerlendirme Sistemi**: Ders ve öğretmen değerlendirmeleri (MVP sonrası).
*   **Offline Kurslar**: Öğretmenlerin video kurslar sunabilmesi (MVP sonrası).
*   **Ek Hizmetler**: Çeviri, danışmanlık, kurumsal paketler, sözlük (MVP sonrası).

## Teknoloji Yığını

*   **Frontend**: Next.js 15+ (App Router), React 19, TypeScript
*   **UI Kütüphanesi**: Shadcn/ui + Tailwind CSS
*   **Backend/Veritabanı**: Supabase (PostgreSQL)
*   **Kimlik Doğrulama**: Clerk
*   **ORM**: Prisma
*   **Video Konferans**: Zoom SDK
*   **Ödeme İşleme**: Stripe (Stripe Connect ile)
*   **Takvim**: FullCalendar
*   **Hosting**: Vercel
*   **Geliştirme Ortamı**: bun

*(Detaylı teknoloji kararları ve gerekçeleri için [Proje Planı Bölüm 7](./AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md#7-teknoloji-yığını-kararlaştırılan-ve-seçim-bekleyenler) bölümüne bakınız.)*

## Proje Dokümantasyonu

*   **Kapsamlı Plan ve Strateji:** [AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md](./AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md)
*   **Süreç Takibi ve Görevler:** [AlmancaABC_Prosess.md](./AlmancaABC_Prosess.md)
*   **Sistem Tasarımı:** [AlmancaABC_system_design.md](./AlmancaABC_system_design.md)
*   **Sınıf Diyagramları:**
    *   [MVP Odaklı](./AlmancaABC_class_diagram_MVP.mermaid)
    *   [Tam Kapsamlı](./AlmancaABC_class_diagram.mermaid)
*   **Sıralama Diyagramları:**
    *   [MVP Odaklı](./AlmancaABC_sequence_diagram_MVP.mermaid)
    *   [Tam Kapsamlı](./AlmancaABC_sequence_diagram.mermaid)
*   **Ürün Gereksinimleri (İlk):** [AlmancaABC_PRD.md](./AlmancaABC_PRD.md) *(Not: Bu dosya başlangıç gereksinimlerini içerir, güncel plan ana plandadır.)*

## WhatsApp Entegrasyonu

Detaylı kurulum ve kullanım rehberi için:
[WHATSAPP_INTEGRATION.md](./WHATSAPP_INTEGRATION.md)

**Temel Bilgiler:**
- 20 gün oturum süresi
- İki sunucu gerektirir (Go + Python)
- Medya desteği mevcut

## Sık Karşılaşılan Hatalar

1. `CGO_ENABLED=0` Hatası:
   - Çözüm: `go env -w CGO_ENABLED=1`

2. Device Limit Reached:
   - WhatsApp > Ayarlar > Bağlı Cihazlar'dan eski cihazları kaldırın

## Başlarken

1.  **Bağımlılıkları Yükleyin:**
    ```bash
    bun install
    ```
2.  **Ortam Değişkenlerini Ayarlayın:**
    *   `.env.local` adında bir dosya oluşturun (veya `.env` dosyasını kopyalayın).
    *   Gerekli Clerk ve Supabase anahtarlarınızı/URL'lerinizi bu dosyaya ekleyin. (`.env` dosyasındaki `DATABASE_URL` ve `DIRECT_URL` formatlarına dikkat edin).
3.  **Veritabanı Geçişlerini Uygulayın (Eğer Gerekliyse):**
    *(Not: Şu anda Prisma migrate ile ilgili bir sorun yaşanmaktadır.)*
    ```bash
    # bunx prisma migrate dev
    ```
4.  **Geliştirme Sunucusunu Başlatın:**
    ```bash
    bun run dev
    ```

Uygulama varsayılan olarak `http://localhost:3000` adresinde çalışacaktır.

## Sorun Giderme

### Çalışan İşlemleri Durdurma (Windows)

Geliştirme sırasında takılan işlemleri durdurmak için `taskkill` komutunu kullanabilirsiniz:

```bash
# Belirli bir işlemi adıyla sonlandırma (Örnek: node.exe)
taskkill /F /IM node.exe

# Bun süreçlerini durdurur
taskkill /F /IM bun.exe

# Diğer olası işlemler:
taskkill /F /IM bun.exe
taskkill /F /IM python.exe
```
- `/F`: Zorla sonlandırma.
- `/IM`: İmaj adı ile sonlandırma.

**Not:** Genellikle `Ctrl+C` yeterlidir. `taskkill` son çare olmalıdır.

### WhatsApp Bağlantı Sorunları

1. **Bağlantı Hatası (WinError 10061):**
   - Go köprüsü ve Python sunucusunun çalıştığından emin olun
   - `taskkill /F /IM go.exe` ve `taskkill /F /IM python.exe` komutlarıyla eski işlemleri sonlandırın

2. **QR Kodu Gözükmüyorsa:**
   - Terminalin QR kodunu desteklediğinden emin olun
   - `whatsapp-bridge/store` dizinindeki *.db dosyalarını silerek yeniden deneyin

3. **Mesaj Gönderilemiyorsa:**
   - `.roo/mcp.json` dosyasını kontrol edin
   - Sunucuları yeniden başlatın

## Veritabanı Migration ve Client Güncelleme

    - bunx prisma migrate dev --name add_parent_model_and_relation