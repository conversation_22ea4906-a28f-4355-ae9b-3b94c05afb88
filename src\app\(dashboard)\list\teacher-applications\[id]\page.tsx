// src/app/(dashboard)/list/teacher-applications/[id]/page.tsx
import { Suspense } from "react";
import { notFound, redirect } from "next/navigation";
import Link from "next/link";
import { getTeacherDetailsForAdmin, TeacherDetailsForAdminResult, EducationEntry, CertificateEntry, approveTeacher as approveTeacherAction, rejectTeacherApplication as rejectTeacherApplicationAction } from "@/lib/actions/teacher.actions"; // getTeacherById -> getTeacherDetailsForAdmin, updateTeacherApprovalStatus kaldırıldı, Tipler ve yeni actionlar eklendi
import { formatDate } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge"; // Badge -> badge
import { Skeleton } from "@/components/ui/skeleton";
import { Metadata } from "next";
// import { auth } from "@clerk/nextjs/server"; // Clerk kaldırıldı
import { toast } from "sonner";

// Dinamik metadata
export async function generateMetadata({
  params,
}: {
  params: { id: string };
}): Promise<Metadata> {
  const teacher: TeacherDetailsForAdminResult | null = await getTeacherDetailsForAdmin(params.id); // getTeacherById -> getTeacherDetailsForAdmin
  
  if (!teacher) {
    return {
      title: "Öğretmen Bulunamadı | AlmancaABC Admin",
    };
  }
  
  return {
    title: `${teacher.firstName || ""} ${teacher.lastName || ""} Başvurusu | AlmancaABC Admin`,
    description: `${teacher.firstName || ""} ${teacher.lastName || ""} öğretmen başvurusu detayları`,
  };
}

// Öğretmen başvurusunu onaylama/reddetme işlemi
// Bu fonksiyon artık teacher.actions.ts içinde (approveTeacher ve rejectTeacher olarak)
// ve doğrudan form action'ları olarak kullanılıyor. Bu nedenle buradaki handleApprovalAction'a gerek kalmadı.
// async function handleApprovalAction(teacherId: string, isApproved: boolean) {
//   let userId: string | null = null;
//   if (process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
//     userId = "test-admin-id";
//   } else {
//     return { success: false, message: "Yetkilendirme mekanizması eksik. Admin kimliği doğrulanamadı." };
//   }
//   if (!userId) {
//     return { success: false, message: "Yetkilendirme hatası. Admin kullanıcı ID'si alınamadı." };
//   }
//   try {
//     // updateTeacherApprovalStatus teacher.actions.ts'den kaldırıldı, yerine approveTeacher/deleteTeacher kullanılacak
//     // const updatedTeacher = await updateTeacherApprovalStatus(teacherId, isApproved, userId);
//     // Şimdilik bu kısmı yoruma alıyorum, çünkü approveTeacher ve rejectTeacher action'ları doğrudan kullanılacak.
//     // Bu fonksiyonun çağrıldığı yerler de bu action'ları kullanacak şekilde güncellenmeli.
//     // Bu PBI kapsamında bu fonksiyonun direkt kullanımı yok, form action'ları var.
//     console.warn("handleApprovalAction çağrıldı ancak updateTeacherApprovalStatus kaldırıldı. Form action'ları kullanılmalı.")
//     const updatedTeacher = true; // Geçici
    
//     if (!updatedTeacher) {
//       return {
//         success: false,
//         message: "Öğretmen başvurusu güncellenirken bir hata oluştu."
//       };
//     }
    
//     return {
//       success: true,
//       message: isApproved
//         ? "Öğretmen başvurusu başarıyla onaylandı."
//         : "Öğretmen başvurusu reddedildi."
//     };
//   } catch (error) {
//     console.error("Öğretmen onay durumu güncellenirken hata:", error);
//     return {
//       success: false,
//       message: "İşlem sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyin."
//     };
//   }
// }

// Öğretmen onaylama/reddetme server action'ları teacher.actions.ts'e taşındı (approveTeacher, deleteTeacher)
// Bu sayfadaki approveTeacher ve rejectTeacher fonksiyonları bu action'ları çağıracak şekilde güncellenecek
// veya doğrudan teacher.actions.ts'deki fonksiyonlar kullanılacak.
// Şimdilik bu fonksiyonlar kalabilir, ancak içlerindeki handleApprovalAction çağrısı düzeltilmeli
// ya da doğrudan teacher.actions.ts'deki approveTeacher/deleteTeacher (veya benzeri) fonksiyonları çağrılmalı.
// Bu PBI'da bu fonksiyonlar doğrudan kullanılmıyor, form action'ları var.
// Bu fonksiyonlar zaten teacher.actions.ts içinde. Bu sayfada tekrar tanımlamaya gerek yok.
// export async function approveTeacher(formData: FormData): Promise<void> { ... }
// export async function rejectTeacher(formData: FormData): Promise<void> { ... }


// Sayfa bileşeni

// Öğretmen onaylama form action'ı
export async function approveTeacherFormAction(formData: FormData): Promise<void> {
  const teacherId = formData.get("teacherId") as string;
  
  if (!teacherId) {
    toast.error("Öğretmen ID'si bulunamadı.");
    return;
  }
  
  const result = await approveTeacherAction(teacherId); // Doğrudan server action çağrısı
  
  if (result.success) {
    toast.success(result.message);
    redirect("/dashboard/list/teacher-applications");
  } else {
    toast.error(result.message);
  }
}

// Öğretmen reddetme form action'ı
export async function rejectTeacherFormAction(formData: FormData): Promise<void> {
  const teacherId = formData.get("teacherId") as string;
  
  if (!teacherId) {
    toast.error("Öğretmen ID'si bulunamadı.");
    return;
  }
  
  const result = await rejectTeacherApplicationAction(teacherId); // Doğrudan server action çağrısı
  
  if (result.success) {
    toast.success(result.message);
    redirect("/dashboard/list/teacher-applications");
  } else {
    toast.error(result.message);
  }
}

// Sayfa bileşeni
export default async function TeacherApplicationDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const teacher: TeacherDetailsForAdminResult | null = await getTeacherDetailsForAdmin(params.id); // getTeacherById -> getTeacherDetailsForAdmin
  
  if (!teacher) {
    notFound();
  }
  
  // Öğretmen verileri için yardımcı değişkenler
  const fullName = `${teacher.firstName || ""} ${teacher.lastName || ""}`.trim() || "İsimsiz Öğretmen";
  const hourlyRate = teacher.hourly_rate; // Bu zaten TeacherDetailsForAdminResult tipinde number | null
  
  // Sertifikalar (action'dan parse edilmiş geliyor)
  const certificatesDisplay: string[] = Array.isArray(teacher.certificates)
    ? teacher.certificates.map((cert: CertificateEntry) => cert.name || JSON.stringify(cert))
    : [];

  // Eğitim bilgileri (action'dan parse edilmiş geliyor)
  const educationDisplay: string[] = Array.isArray(teacher.education)
    ? teacher.education.map((edu: EducationEntry) =>
        edu.degree ? `${edu.degree} - ${edu.institution || ""} (${edu.year || ""})` : JSON.stringify(edu)
      )
    : [];
  
  // Dil seviyeleri (teacher.spokenLanguages kullanılacak)
  const spokenLanguagesDisplay: string[] = Array.isArray(teacher.spokenLanguages) ? teacher.spokenLanguages : [];
  
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Öğretmen Başvuru Detayları</h1>
        <Button variant="outline" asChild>
          <Link href="/dashboard/list/teacher-applications">
            Tüm Başvurulara Dön
          </Link>
        </Button>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Kişisel Bilgiler */}
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Kişisel Bilgiler</CardTitle>
            <CardDescription>Öğretmenin temel bilgileri</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Ad Soyad</h3>
              <p className="text-base">{fullName}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">E-posta</h3>
              <p className="text-base">{teacher.email || "Belirtilmemiş"}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Unvan</h3>
              <p className="text-base">{teacher.title || "Belirtilmemiş"}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Konum</h3>
              <p className="text-base">
                {teacher.city && teacher.country 
                  ? `${teacher.city}, ${teacher.country}` 
                  : teacher.city || teacher.country || "Belirtilmemiş"}
              </p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Başvuru Tarihi</h3>
              <p className="text-base">{formatDate(teacher.created_at)}</p>
            </div>
          </CardContent>
        </Card>
        
        {/* Mesleki Bilgiler */}
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Mesleki Bilgiler</CardTitle>
            <CardDescription>Öğretmenin deneyim ve uzmanlık alanları</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Deneyim</h3>
              <p className="text-base">Belirtilmemiş</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Uzmanlık Alanları</h3>
              <div className="flex flex-wrap gap-2 mt-1">
                {Array.isArray(teacher.specializations) && teacher.specializations.length > 0 ? (
                  teacher.specializations.map((spec: string, i: number) => (
                    <Badge key={i} variant="secondary">
                      {spec}
                    </Badge>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">Belirtilmemiş</p>
                )}
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Sertifikalar</h3>
              <div className="flex flex-wrap gap-2 mt-1">
                {certificatesDisplay.length > 0 ? (
                  certificatesDisplay.map((cert: string, i: number) => (
                    <Badge key={i} variant="outline">
                      {cert}
                    </Badge>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">Belirtilmemiş</p>
                )}
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Eğitim Seviyesi</h3>
              <p className="text-base">{educationDisplay.join(", ") || "Belirtilmemiş"}</p>
            </div>
          </CardContent>
        </Card>
        
        {/* Ek Bilgiler */}
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Ek Bilgiler</CardTitle>
            <CardDescription>Öğretmenin diğer bilgileri</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Hakkında</h3>
              <p className="text-sm mt-1 whitespace-pre-wrap">
                {teacher.shortBio || teacher.aboutMe?.join("\n") || "Henüz bir açıklama eklenmemiş."}
              </p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Ders Ücreti</h3>
              <p className="text-base">
                {hourlyRate !== null ? `${hourlyRate} € / saat` : "Belirtilmemiş"}
              </p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Konuştuğu Diller</h3>
              <div className="flex flex-wrap gap-2 mt-1">
                {spokenLanguagesDisplay.length > 0 ? (
                  spokenLanguagesDisplay.map((lang: string, i: number) => (
                    <Badge key={i} variant="outline">
                      {lang}
                    </Badge>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">Belirtilmemiş</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Başvuru Durumu ve İşlemler */}
      <Card className="mt-6 shadow-md">
        <CardHeader>
          <CardTitle>Başvuru Durumu</CardTitle>
          <CardDescription>Öğretmen başvurusunun mevcut durumu</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center">
            <Badge variant={teacher.is_approved ? "default" : "secondary"} className={teacher.is_approved ? "bg-green-500 hover:bg-green-600" : ""}>
              {teacher.is_approved ? "Onaylandı" : "Beklemede"}
            </Badge>
            <span className="ml-2 text-sm text-muted-foreground">
              {teacher.is_approved 
                ? `${formatDate(teacher.updated_at)} tarihinde onaylandı` 
                : "Henüz onaylanmadı"}
            </span>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end space-x-4">
          <Button variant="outline" asChild>
            <Link href={`/teachers/${teacher.id}`} target="_blank">
              Profili Görüntüle
            </Link>
          </Button>
          
          {!teacher.is_approved ? (
            <>
              <form action={approveTeacherFormAction}>
                <input type="hidden" name="teacherId" value={teacher.id} />
                <Button type="submit" variant="default">
                  Onayla
                </Button>
              </form>
              
              <form action={rejectTeacherFormAction}>
                <input type="hidden" name="teacherId" value={teacher.id} />
                <Button type="submit" variant="destructive">
                  Reddet
                </Button>
              </form>
            </>
          ) : (
            <form action={rejectTeacherFormAction}> {/* Onay iptali için de rejectTeacherFormAction kullanılabilir, is_approved: false yapar */}
              <input type="hidden" name="teacherId" value={teacher.id} />
              <Button type="submit" variant="destructive">
                Onayı İptal Et (Reddet)
              </Button>
            </form>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}

// Yükleme durumu için iskelet bileşeni
function TeacherDetailSkeleton() {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-10 w-40" />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="border rounded-lg p-6">
            <Skeleton className="h-6 w-40 mb-2" />
            <Skeleton className="h-4 w-full mb-4" />
            
            <div className="space-y-4">
              {[1, 2, 3, 4].map((j) => (
                <div key={j}>
                  <Skeleton className="h-4 w-24 mb-1" />
                  <Skeleton className="h-5 w-full" />
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
