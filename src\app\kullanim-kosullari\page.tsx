import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { ChevronLeft, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const metadata: Metadata = {
  title: 'Kullanım Koşulları | AlmancaABC',
  description: 'AlmancaABC platformunun kullanımına ilişkin şartlar ve koşullar.',
};

const KullanimKosullariPage = () => {
  return (
    (<div className="container mx-auto py-8 px-4 md:px-6">
      <div className="mb-8">
        <Button variant="outline" asChild className="text-sm">
          <Link href="/yardim">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Yardım Merkezine Dön
          </Link>
        </Button>
      </div>
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center space-x-3 mb-3">
            <FileText className="h-8 w-8 text-green-600" />
            <CardTitle className="text-3xl font-bold">Kullanım Koşulları</CardTitle>
          </div>
          <p className="text-muted-foreground">Son Güncelleme: 18 Mayıs 2025</p>
        </CardHeader>
        <CardContent className="prose prose-sm sm:prose lg:prose-lg xl:prose-xl dark:prose-invert max-w-none">
          <h2>1. Taraflar ve Tanımlar</h2>
          <p>
            Bu Kullanım Koşulları (&quot;Sözleşme&quot;), AlmancaABC platformu (&quot;Platform&quot;) ile Platform&apos;u kullanan
            öğretmenler (&quot;Öğretmen&quot;) ve öğrenciler (&quot;Öğrenci&quot;) (topluca &quot;Kullanıcılar&quot;) arasındaki
            ilişkileri düzenler.
          </p>
          <h2>2. Hizmetin Kapsamı</h2>
          <p>
            AlmancaABC, Almanca dil öğrenimi üzerine odaklanmış, Öğretmenler ile Öğrencileri buluşturan
            bir online platformdur. Platform üzerinden canlı dersler planlanabilir, gerçekleştirilebilir
            ve ek öğrenim materyallerine erişilebilir.
          </p>
          <h2>3. Üyelik ve Hesap Güvenliği</h2>
          <p>
            Kullanıcılar, Platform&apos;a üye olurken doğru ve güncel bilgiler vermekle yükümlüdür.
            Hesap güvenliğinden Kullanıcılar sorumludur.
          </p>
          <h2>4. Dersler ve Ödemeler</h2>
          <p>
            Ders ücretleri Öğretmenler tarafından belirlenir. Ödemeler Platform üzerinden güvenli
            bir şekilde yapılır. AlmancaABC, gerçekleşen dersler üzerinden komisyon alır.
            Detaylı komisyon oranları ve ödeme koşulları ayrıca belirtilmiştir.
          </p>
          <h2>5. İptal ve İade Koşulları</h2>
          <p>
            Ders iptal ve iade politikaları Öğretmenler tarafından belirlenebilir ve Platform&apos;un
            genel iade politikasına tabidir. Genellikle ders saatinden en az 24 saat önce yapılan
            iptallerde tam iade veya erteleme hakkı tanınır.
          </p>
          <h2>6. Fikri Mülkiyet Hakları</h2>
          <p>
            Platform içeriği ve markası AlmancaABC&apos;ye aittir. Öğretmenler tarafından sunulan ders
            materyallerinin fikri mülkiyeti Öğretmenlere aittir, ancak Platform&apos;da kullanım hakkı
            AlmancaABC&apos;ye tanınmıştır.
          </p>
          <h2>7. Sorumluluk Sınırlaması</h2>
          <p>
            AlmancaABC, derslerin içeriği veya kalitesi konusunda doğrudan sorumlu değildir.
            Platform, Öğretmen ve Öğrenciler arasında bir aracı rolü üstlenir.
          </p>
          <h2>8. Değişiklikler</h2>
          <p>
            AlmancaABC, bu Kullanım Koşulları&apos;nı dilediği zaman güncelleme hakkını saklı tutar.
            Güncellemeler Platform üzerinden duyurulur.
          </p>
          <h2>9. İletişim</h2>
          <p>
            Her türlü soru ve öneriniz için lütfen <Link href="/iletisim" className="text-blue-600 hover:underline">iletişim sayfamızdan</Link> bize ulaşın.
          </p>
          <p>
            {/* Buraya daha fazla detay ve yasal metin eklenecektir. */}
            Bu belge genel bir taslaktır ve yasal geçerliliği için bir hukuk danışmanına
            başvurulması önerilir.
          </p>
        </CardContent>
      </Card>
    </div>)
  );
};

export default KullanimKosullariPage;