import { NextRequest, NextResponse } from "next/server";
import { stripe } from "@/lib/stripe";
import { updatePaymentStatus } from "@/lib/actions/payment.actions";
import Stripe from "stripe";

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = req.headers.get("stripe-signature");

  if (!signature) {
    return NextResponse.json(
      { error: "Missing stripe-signature header" },
      { status: 400 }
    );
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (error) {
    console.error("Webhook signature verification failed:", error);
    return NextResponse.json(
      { error: "Invalid signature" },
      { status: 400 }
    );
  }

  try {
    switch (event.type) {
      case "payment_intent.succeeded":
        const paymentIntentSucceeded = event.data.object as Stripe.PaymentIntent;
        await updatePaymentStatus(paymentIntentSucceeded.id, "succeeded");
        console.log("Payment succeeded:", paymentIntentSucceeded.id);
        break;

      case "payment_intent.payment_failed":
        const paymentIntentFailed = event.data.object as Stripe.PaymentIntent;
        await updatePaymentStatus(paymentIntentFailed.id, "failed");
        console.log("Payment failed:", paymentIntentFailed.id);
        break;

      case "payment_intent.canceled":
        const paymentIntentCanceled = event.data.object as Stripe.PaymentIntent;
        await updatePaymentStatus(paymentIntentCanceled.id, "canceled");
        console.log("Payment canceled:", paymentIntentCanceled.id);
        break;

      case "payment_intent.requires_action":
        const paymentIntentRequiresAction = event.data.object as Stripe.PaymentIntent;
        await updatePaymentStatus(paymentIntentRequiresAction.id, "requires_action");
        console.log("Payment requires action:", paymentIntentRequiresAction.id);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Webhook handler error:", error);
    return NextResponse.json(
      { error: "Webhook handler failed" },
      { status: 500 }
    );
  }
}
