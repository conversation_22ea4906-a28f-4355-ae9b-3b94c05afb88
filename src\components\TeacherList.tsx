"use client"

import React, { useRef, useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
// import { useLanguage } from "@/lib/i18n/LanguageContext"
import { motion, AnimatePresence } from "framer-motion"
import { Star, Calendar, MessageCircle, ArrowRight, ChevronLeft, ChevronRight } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import TeacherSchema from "./TeacherSchema"
import { VerifiedBadge } from "@/components/VerifiedBadge" // VerifiedBadge import edildi

// Örnek öğretmen verileri (verified alanı eklendi)
const teachers = [
  {
    id: 1,
    name: "<PERSON><PERSON>şe Yılmaz",
    image: "https://randomuser.me/api/portraits/women/44.jpg",
    rating: 4.9,
    reviews: 127,
    specialization: "A1-B2 Seviye, <PERSON>ş Almancası",
    hourlyRate: 150,
    description: "10 yıllık den<PERSON>, sabırlı ve öğrenci odaklı bir öğretmenim.",
    verified: true, // Eklendi
  },
  {
    id: 2,
    name: "Ahmet Kaya",
    image: "https://randomuser.me/api/portraits/men/32.jpg",
    rating: 4.8,
    reviews: 98,
    specialization: "Tüm Seviyeler, Sınav Hazırlık",
    hourlyRate: 180,
    description: "Goethe Enstitüsü'nde 5 yıl çalıştım. TestDaF ve telc sınavlarında uzmanlığım var.",
    verified: false, // Eklendi
  },
  {
    id: 3,
    name: "Zeynep Demir",
    image: "https://randomuser.me/api/portraits/women/68.jpg",
    rating: 5.0,
    reviews: 56,
    specialization: "Konuşma Pratiği, Günlük Almanca",
    hourlyRate: 140,
    description: "Almanya'da doğdum ve büyüdüm. Doğal bir Almanca konuşma pratiği sunuyorum.",
    verified: true, // Eklendi
  },
  {
    id: 4,
    name: "Can Öztürk",
    image: "https://randomuser.me/api/portraits/men/75.jpg",
    rating: 4.7,
    reviews: 84,
    specialization: "Çocuklar için Almanca, A1-C1",
    hourlyRate: 160,
    description: "Çocuklara ve gençlere Almanca öğretme konusunda 8 yıllık deneyime sahibim.",
    verified: false, // Eklendi
  },
  {
    id: 5,
    name: "Fatma Şahin",
    image: "https://randomuser.me/api/portraits/women/55.jpg",
    rating: 4.9,
    reviews: 110,
    specialization: "A1-C1 Seviye, YDS Hazırlık",
    hourlyRate: 170,
    description: "Akademik Almanca ve sınav hazırlığı konusunda uzmanım.",
    verified: true, // Eklendi
  },
  {
    id: 6,
    name: "Ali Vural",
    image: "https://randomuser.me/api/portraits/men/41.jpg",
    rating: 4.6,
    reviews: 75,
    specialization: "Tıbbi Almanca, Mesleki Almanca",
    hourlyRate: 190,
    description: "Sağlık sektöründe çalışanlar için özel Almanca dersleri veriyorum.",
    verified: true, // Eklendi
  },
  {
    id: 7,
    name: "Elif Aksoy",
    image: "https://randomuser.me/api/portraits/women/72.jpg",
    rating: 4.8,
    reviews: 92,
    specialization: "Çeviri Teknikleri, İleri Gramer",
    hourlyRate: 165,
    description: "Almanca-Türkçe çeviri ve dilbilgisi konularında yardımcı olabilirim.",
    verified: false, // Eklendi
  },
  {
    id: 8,
    name: "Mustafa Yıldız",
    image: "https://randomuser.me/api/portraits/men/88.jpg",
    rating: 4.7,
    reviews: 88,
    specialization: "Günlük Konuşma, Kültür",
    hourlyRate: 155,
    description: "Almanya kültürü ve günlük yaşam hakkında konuşarak pratik yapalım.",
    verified: true, // Eklendi
  },
]

export default function TeacherList() {
  // const { t } = useLanguage()
  const router = useRouter()
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [showLeftArrow, setShowLeftArrow] = useState(false)
  const [showRightArrow, setShowRightArrow] = useState(true)
  const [isFirstLoad, setIsFirstLoad] = useState(true)

  // Kaydırma işlevi
  const scroll = (direction: 'left' | 'right') => {
    const container = scrollContainerRef.current
    if (!container) return

    const scrollAmount = 300 // Kaydırma miktarı
    const scrollLeft = direction === 'left' 
      ? container.scrollLeft - scrollAmount 
      : container.scrollLeft + scrollAmount
    
    container.scrollTo({
      left: scrollLeft,
      behavior: 'smooth'
    })
  }

  // Scroll pozisyonunu izle
  const handleScroll = () => {
    const container = scrollContainerRef.current
    if (!container) return

    // Sol ok gösterimi için kontrol
    setShowLeftArrow(container.scrollLeft > 20)
    
    // Sağ ok gösterimi için kontrol
    const isAtEnd = container.scrollLeft + container.clientWidth >= container.scrollWidth - 20
    setShowRightArrow(!isAtEnd)
  }

  // İlk yükleme animasyonu için
  useEffect(() => {
    if (isFirstLoad) {
      const timer = setTimeout(() => {
        setIsFirstLoad(false)
      }, 1500)
      
      return () => clearTimeout(timer)
    }
  }, [isFirstLoad])

  // Scroll olayını dinle
  useEffect(() => {
    const container = scrollContainerRef.current
    if (container) {
      container.addEventListener('scroll', handleScroll)
      // İlk yükleme kontrolü
      handleScroll()
      
      return () => {
        container.removeEventListener('scroll', handleScroll)
      }
    }
  }, [])

  return (
    (<section className="py-16 bg-gray-50">
      <TeacherSchema teachers={teachers} />
      <div className="container mx-auto px-4">
        {/* Başlık ve "Tümünü Gör" Butonu */}
        <div className="flex justify-between items-center mb-8">
          <motion.h2
            initial={{ opacity: 0, y: -10 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl font-bold text-gray-900"
          >
            Öne Çıkan Almanca Öğretmenleri
          </motion.h2>
          <motion.div
            initial={{ opacity: 0, x: 10 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <Button asChild variant="ghost" className="text-primary font-medium">
              <Link href="/ogretmenler">
                Tümünü Gör
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </motion.div>
        </div>

        {/* Kaydırılabilir Öğretmen Listesi Konteyneri */}
        <div className="relative overflow-visible">
          {/* Sol Ok */}
          <AnimatePresence>
            {showLeftArrow && (
              <motion.button 
                onClick={() => scroll('left')}
                initial={{ opacity: 0, x: 10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 10 }}
                className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 backdrop-blur-sm rounded-full p-2 shadow-md text-primary border border-gray-200 hover:bg-primary/10 transition-all duration-200 -ml-3"
                aria-label="Sola kaydır"
                whileHover={{ scale: 1.1, boxShadow: "0 4px 12px rgba(0,0,0,0.1)" }}
              >
                <ChevronLeft className="h-5 w-5" />
              </motion.button>
            )}
          </AnimatePresence>
          
          {/* Sağ Ok */}
          <AnimatePresence>
            {showRightArrow && (
              <motion.button 
                onClick={() => scroll('right')}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 backdrop-blur-sm rounded-full p-2 shadow-md text-primary border border-gray-200 hover:bg-primary/10 transition-all duration-200 -mr-3"
                aria-label="Sağa kaydır"
                whileHover={{ scale: 1.1, boxShadow: "0 4px 12px rgba(0,0,0,0.1)" }}
                // İlk yükleme animasyonu
                {...(isFirstLoad && {
                  animate: {
                    x: [0, 5, 0],
                    opacity: [0.7, 1, 0.7],
                    scale: [1, 1.1, 1],
                    transition: { 
                      repeat: 3,
                      duration: 1.5,
                      repeatType: "reverse" 
                    }
                  }
                })}
              >
                <ChevronRight className="h-5 w-5" />
              </motion.button>
            )}
          </AnimatePresence>

          {/* Kaydırılabilir Alan */}
          <div 
            ref={scrollContainerRef}
            className="flex overflow-x-auto space-x-6 pb-4 -mx-4 px-4 items-stretch scrollbar-hide scroll-smooth"
            data-component-name="TeacherList"
          >
            {teachers.map((teacher, index) => (
              <motion.div
                key={teacher.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.08 }}
                viewport={{ once: true }}
                className="w-[240px] md:w-[260px] flex-shrink-0 relative overflow-visible"
              >
                {/* Sıra Rozeti: Sol üstte, büyük ve modern */}
                <div
                  className="absolute left-2 top-2 z-10 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 text-sm font-bold shadow"
                >
                  {index + 1}
                </div>
                <Card className="flex flex-col gap-6 rounded-xl border py-6 shadow-sm h-full hover:shadow-lg transition-shadow overflow-visible">
                  <CardContent className="p-4 flex flex-col items-center text-center">
                    <Image
                      src={teacher.image}
                      alt={teacher.name}
                      width={96}
                      height={96}
                      className="rounded-full object-cover mb-4"
                    />
                    <div>
                      <div className="mb-2">
                        {/* İsim ve Onay Rozeti */}                        <div className="flex items-baseline justify-center gap-1"> {/* items-baseline eklendi */}
                           <h3 className="font-semibold text-lg">{teacher.name}</h3>
                           <VerifiedBadge 
                             isVerified={teacher.verified} 
                             size="sm"
                             variant="simple" 
                           /> {/* VerifiedBadge eklendi ve boyutu küçültüldü */}
                        </div>
                        {/* Rating */}
                        <div className="flex items-center justify-center">
                          <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                          <span className="text-sm ml-1">
                            {teacher.rating} ({teacher.reviews})
                          </span>
                        </div>
                      </div>
                      <div className="text-center mb-2">
                        <span className="font-semibold text-primary">{teacher.hourlyRate} ₺/saat</span>
                      </div>
                      <p className="text-sm font-medium text-primary mb-2">{teacher.specialization}</p>
                      <p className="text-sm text-gray-600 mb-4 line-clamp-2">{teacher.description}</p>
                    </div>
                    <div className="w-full mt-auto pt-4 border-t"> {/* mt-auto eklendi */}
                      <div className="flex justify-center space-x-2">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push(`/teachers/${teacher.id}#mesaj`);
                            }}
                          >
                            <MessageCircle className="h-4 w-4 mr-1" />
                            Mesaj
                          </Button>
                          <Button asChild size="sm" onClick={(e) => e.stopPropagation()}>
                            <Link
                              href={`/teachers/${teacher.id}`}
                              onClick={(e) => e.stopPropagation()}
                            >
                              <Calendar className="h-4 w-4 mr-1" />
                              Ders Al
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}

            {/* "Tüm Öğretmenler" Kartı */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: teachers.length * 0.1 }}
              viewport={{ once: true }}
              className="w-[240px] md:w-[260px] flex-shrink-0"
            >
              <Link href="/teachers" className="h-full flex group">
                <Card className="bg-primary/10 hover:bg-primary/20 transition-colors h-full w-full">
                  <CardContent className="flex flex-col items-center justify-center text-center h-full p-4">
                    <div className="bg-white rounded-full p-3 mb-3 border">
                      <ArrowRight className="h-6 w-6 text-primary transition-transform duration-200 group-hover:translate-x-1" />
                    </div>
                    <h3 className="font-bold text-lg text-primary mb-1">Tüm Öğretmenler</h3>
                    <p className="text-sm text-primary/80">500+ uzman öğretmen</p>
                  </CardContent>
                </Card>
              </Link>
            </motion.div>
          </div>
        </div>
      </div>
    </section>)
  );
}
