"use client" // Bu bileşen state tutmuyor, sonra kaldırılabilir

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card" // Düzeltildi
import { Star, Users, Clock, CheckCircle } from "lucide-react"
import type { TeacherStats as TeacherStatsType } from "@/types/teacher" // Düzeltildi

interface TeacherStatsProps {
  stats: TeacherStatsType
}

export function TeacherStats({ stats }: TeacherStatsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>İstatistik<PERSON></CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          {/* Ortalama Puan */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Star className="w-4 h-4" />
              Puan
            </div>
            <p className="text-2xl font-bold">{stats.avgRating?.toFixed(1) ?? 'N/A'}</p> {/* Null check eklendi */}
          </div>

          {/* Toplam Öğrenci */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Users className="w-4 h-4" />
              Öğrenci
            </div>
            <p className="text-2xl font-bold">{stats.totalStudents ?? 'N/A'}</p> {/* Null check eklendi */}
          </div>

          {/* Toplam Ders */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="w-4 h-4" />
              Ders
            </div>
            <p className="text-2xl font-bold">{stats.totalLessons ?? 'N/A'}</p> {/* Null check eklendi */}
          </div>

          {/* Tamamlama Oranı */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <CheckCircle className="w-4 h-4" />
              Tamamlama
            </div>
            {/* Yüzde işareti eklendi */}
            <p className="text-2xl font-bold">{stats.completionRate != null ? `${stats.completionRate}%` : 'N/A'}</p>
          </div>
        </div>

        {/* Yanıt Oranı ve Süresi */}
        <div className="mt-6 space-y-4">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Yanıt Oranı</span>
            {/* Yüzde işareti eklendi */}
            <span className="font-medium">{stats.responseRate != null ? `${stats.responseRate}%` : 'N/A'}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Yanıt Süresi</span>
            <span className="font-medium">{stats.responseTime || 'N/A'}</span> {/* Null check eklendi */}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}