// src/lib/actions/parent.actions.ts
"use server";

// Kaldırıldı: import { PrismaClient } from "@prisma/client";
import { prisma } from '@/lib/prisma'; // Eklendi
// Prisma namespace'i bu dosyada do<PERSON><PERSON> kull<PERSON>, @prisma/client'tan ayrıca import etmeye gerek yok.
import { unstable_noStore as noStore } from 'next/cache';
// import { auth } from "@clerk/nextjs/server"; // Clerk kaldırıldı

// Varsayım: Parent modeli ve Student ile ilişkisi schema.prisma'da tanımlı.
// Varsayım: Giriş yapmış kullanıcının Clerk ID'si Parent ID'si ile aynı.

// Giriş yapmış velinin çocuklarının ID'lerini ve isimlerini getirir
// TODO: Prisma şeması güncellenince Student modelinden isimleri de seç
async function getChildrenForParent() {
    // const { userId: parentId } = await auth(); // Clerk kaldırıldı
    let parentId = null;

    if ((parentId === null || parentId === undefined) && process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
        // console.warn("DEV_SKIP_AUTH_MIDDLEWARE: getChildrenForParent - Using default 'test-parent-id'.");
        parentId = "test-parent-id";
    }

    if (!parentId) {
        // console.error("getChildrenForParent: parentId is missing and DEV_SKIP_AUTH_MIDDLEWARE is not providing a fallback.");
        return [];
    }

    try {
        const children = await prisma.student.findMany({
            where: { parentId: parentId },
            select: {
                id: true,
                // firstName: true, // Prisma şemasına eklendiğinde açılacak
                // lastName: true,  // Prisma şemasına eklendiğinde açılacak
            }
        });
        return children.map(child => ({
            ...child,
            // name: `${child.firstName || 'Çocuk'} ${child.lastName || child.id.substring(0,4)}`.trim() // İsimler eklenince bu kullanılacak
            name: `Çocuk ${child.id.substring(0,4)}`
        }));
    } catch (error) {
        // Removed console.error - return empty array for error cases
        return [];
    }
}

export async function getUpcomingLessonsForChildren(limitPerChild: number = 3) {
     // const { userId: parentId } = await auth(); // Clerk kaldırıldı
     let parentId = null;

     if ((parentId === null || parentId === undefined) && process.env.DEV_SKIP_AUTH_MIDDLEWARE === "true") {
        // console.warn("DEV_SKIP_AUTH_MIDDLEWARE: getUpcomingLessonsForChildren - Using default 'test-parent-id'.");
        parentId = "test-parent-id";
     }

     if (!parentId) {
        // console.error("getUpcomingLessonsForChildren: parentId is missing and DEV_SKIP_AUTH_MIDDLEWARE is not providing a fallback.");
        return [];
     }

     try {
        // getChildrenForParent fonksiyonu artık parentId'yi kendi içinde yönetiyor.
        // Ancak, getUpcomingLessonsForChildren'ın kendi parentId'sini kullanması daha doğru olur.
        const children = await prisma.student.findMany({
            where: { parentId: parentId },
            select: {
                id: true,
                firstName: true, // Eklendi
                lastName: true   // Eklendi
            }
        });
        if (children.length === 0) return [];

        const childrenIds = children.map(child => child.id);
        const now = new Date();

        // Her çocuk için ayrı ayrı sorgu yapmak yerine tek sorguda birleştirelim
        const upcomingBookings = await prisma.booking.findMany({
             where: {
                 studentId: {
                     in: childrenIds, // Çocukların ID listesi ile filtrele
                 },
                 lessonTime: {
                     gte: now,
                 },
                 // status: 'CONFIRMED',
             },
             orderBy: {
                 lessonTime: 'asc',
             },
             // take: limitPerChild * childrenIds.length, // Toplam limiti ayarla? Veya her çocuk için ayrı limit? Şimdilik hepsini alalım.
             include: {
                 teacher: {
                     select: { firstName: true, lastName: true, profile_image_url: true } // profile_image_url eklendi
                 },
                 student: { // Hangi çocuğa ait olduğunu bilmek için
                     select: { id: true } // Student modeline isim eklenince onu da al
                 }
             }
        });

         // Çocuk isimlerini ve öğretmen isimlerini ekleyelim
         const childrenMap = new Map(children.map(c => [c.id, `${c.firstName || 'Çocuk'} ${c.lastName || c.id.substring(0,4)}`.trim()]));

         const formattedBookings = upcomingBookings.map(booking => ({
             ...booking,
             studentName: childrenMap.get(booking.studentId) || `Öğrenci ${booking.studentId.substring(0,4)}`,
             teacherName: `${booking.teacher.firstName ?? ''} ${booking.teacher.lastName ?? ''}`.trim(),
             teacherProfileImageUrl: booking.teacher.profile_image_url, // teacherProfileImageUrl eklendi
         }));

         // TODO: Belki her çocuk için ayrı limit uygulamak daha iyi olabilir. Şimdilik hepsi bir arada.
         return formattedBookings.slice(0, limitPerChild * childrenIds.length); // Genel bir limit uygulayalım

     } catch (error) {
         // Removed console.error - return empty array for error cases
         return [];
     }
}

// TODO: Diğer veli'ye özel action'lar buraya eklenebilir (çocuk ekleme/çıkarma, profil güncelleme vb.)