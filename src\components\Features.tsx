"use client"

import React from "react"
import { motion } from "framer-motion"
// import { useLanguage } from "@/lib/i18n/LanguageContext"
import { Card, CardContent } from "@/components/ui/card"
import { Video, Calendar, MessageCircle, BookOpen, Award, Users } from "lucide-react"

const features = [
  {
    icon: <Video className="h-10 w-10 text-primary" />,
    title: "Canlı Video Dersler",
    description: "HD kalitesinde, kesintisiz video derslerle yüz yüze eğitim deneyimi yaşayın.",
  },
  {
    icon: <Calendar className="h-10 w-10 text-primary" />,
    title: "Esnek Planlama",
    description: "<PERSON><PERSON> uygun saatlerde, istediğ<PERSON>z sıklıkta ders planlayın.",
  },
  {
    icon: <MessageCircle className="h-10 w-10 text-primary" />,
    title: "Anında İletişim",
    description: "Öğretmeninizle mesajlaşarak sorularınızı anında sorun, hızlı yanıt alın.",
  },
  {
    icon: <BookOpen className="h-10 w-10 text-primary" />,
    title: "Zengin İçerikler",
    description: "Ders notları, alıştırmalar ve özel hazırlanmış materyallerle öğrenmenizi pekiştirin.",
  },
  {
    icon: <Award className="h-10 w-10 text-primary" />,
    title: "Sertifikalı Öğretmenler",
    description: "Alanında uzman, sertifikalı ve deneyimli öğretmenlerle çalışın.",
  },
  {
    icon: <Users className="h-10 w-10 text-primary" />,
    title: "Grup Dersleri",
    description: "Arkadaşlarınızla birlikte grup dersleri alarak hem öğrenin hem tasarruf edin.",
  },
]

export default function Features() {
  // const { t } = useLanguage()

  return (
    <section className="py-6 sm:py-8 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl font-bold mb-4"
          >
            Neden AlmancaABC?
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-gray-600 max-w-2xl mx-auto"
          >
            AlmancaABC ile Almanca öğrenmek hızlı, etkili ve eğlenceli. İşte platformumuzun sunduğu avantajlar.
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full hover:shadow-md transition-shadow border-t-4 border-t-primary">
                <CardContent className="p-4">
                  <div className="mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}