// src/app/(dashboard)/admin/students/page.tsx
// import { Button } from "@/components/ui/button"; // Şimdilik kullanılmıyor
import { DataTable } from "@/components/dashboard/DataTable";
import { columns } from "./_components/columns"; // Öğrenci sütun tanımları
// import { PlusCircle } from "lucide-react"; // Şimdilik kullanılmıyor
// import Link from "next/link"; // Şimdilik kullanılmıyor

// TODO: Gerçek öğrenci verilerini Prisma ve Server Action ile çek.
// Prisma'dan dönecek Student tipine benzer bir tip varsayalım.
// Veya doğrudan Prisma tipini kullanabiliriz (import { Student } from "@prisma/client";)
// Clerk'ten email gibi ek bilgiler gerekebilir.
type StudentData = {
  id: string;
  // Clerk'ten alınacak veya Student modeline eklenecek
  firstName: string | null;
  lastName: string | null;
  email: string;
  proficiencyLevel: string | null; // Öğrencinin seviyesi
  created_at: Date;
};

async function getStudents(): Promise<StudentData[]> {
  // Şimdilik örnek veriler döndürelim
  // Gerçek implementasyonda: await prisma.student.findMany(...) veya ilgili action
  return [
    { id: "student-1", firstName: "Ali", lastName: "Veli", email: "<EMAIL>", proficiencyLevel: "A2", created_at: new Date() },
    { id: "student-2", firstName: "Zeynep", lastName: "Demir", email: "<EMAIL>", proficiencyLevel: "B1", created_at: new Date() },
    { id: "student-3", firstName: "Mehmet", lastName: "Kaya", email: "<EMAIL>", proficiencyLevel: "A1", created_at: new Date() },
  ];
}

export default async function AdminStudentsPage() {
  const students = await getStudents();

  return (
    <div className="flex flex-col gap-6">
       <div className="flex justify-between items-center">
         <h1 className="text-2xl font-semibold">Öğrenci Yönetimi</h1>
         {/* TODO: Yeni öğrenci ekleme butonu gerekli mi? Genellikle öğrenciler kendileri kaydolur. */}
         {/* <Link href="/admin/students/new">
            <Button>
                <PlusCircle className="mr-2 h-4 w-4" /> Yeni Öğrenci Ekle
            </Button>
         </Link> */}
       </div>

      {/* Öğrenci Veri Tablosu */}
      <DataTable
        columns={columns}
        data={students}
        searchColumnId="email" // E-posta sütununda arama yap
        searchPlaceholder="E-posta ile ara..."
       />
    </div>
  );
}