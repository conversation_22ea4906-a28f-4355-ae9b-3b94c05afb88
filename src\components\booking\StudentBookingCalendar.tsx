"use client";

import { useState, useEffect } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "@/hooks/use-toast";
import { getAvailabilitySlots } from "@/lib/actions/availability.actions";
import type { AvailabilitySlot } from "@prisma/client";
import { EventClickArg } from "@fullcalendar/core";
import { BookingForm } from "./BookingForm";

interface StudentBookingCalendarProps {
  teacherId: string;
  teacherName: string;
  hourlyRate: number;
  studentId?: string;
  onSuccess?: () => void;
}

export default function StudentBookingCalendar({
  teacherId,
  teacherName,
  hourlyRate,
  studentId,
  onSuccess
}: StudentBookingCalendarProps) {
  const [availabilitySlots, setAvailabilitySlots] = useState<AvailabilitySlot[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedSlot, setSelectedSlot] = useState<AvailabilitySlot | null>(null);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);

  // Müsaitlik slotlarını yükle
  useEffect(() => {
    const loadAvailabilitySlots = async () => {
      try {
        const slots = await getAvailabilitySlots(teacherId);
        // Sadece rezerve edilmemiş ve gelecekteki slotları göster
        const availableSlots = slots.filter(slot => 
          !slot.isBooked && new Date(slot.startTime) > new Date()
        );
        setAvailabilitySlots(availableSlots);
      } catch (error) {
        toast({
          title: "Hata",
          description: "Müsaitlik bilgileri yüklenirken bir hata oluştu.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadAvailabilitySlots();
  }, [teacherId]);

  // AvailabilitySlot'ları FullCalendar event formatına çevir
  const events = availabilitySlots.map((slot) => ({
    id: slot.id,
    title: "Müsait",
    start: slot.startTime,
    end: slot.endTime,
    backgroundColor: "#22c55e", // Yeşil: müsait
    borderColor: "#16a34a",
    textColor: "#ffffff",
    extendedProps: {
      slotId: slot.id,
      slot: slot,
    },
  }));

  // Slot seçimi
  const handleEventClick = (clickInfo: EventClickArg) => {
    const slot = clickInfo.event.extendedProps.slot as AvailabilitySlot;
    setSelectedSlot(slot);
    setIsBookingModalOpen(true);
  };

  // Rezervasyon başarılı olduğunda
  const handleBookingSuccess = () => {
    setIsBookingModalOpen(false);
    setSelectedSlot(null);

    // Üst bileşene bildir
    if (onSuccess) {
      onSuccess();
    }

    // Slotları yeniden yükle
    const loadAvailabilitySlots = async () => {
      try {
        const slots = await getAvailabilitySlots(teacherId);
        const availableSlots = slots.filter(slot =>
          !slot.isBooked && new Date(slot.startTime) > new Date()
        );
        setAvailabilitySlots(availableSlots);
      } catch (error) {
        toast({
          title: "Hata",
          description: "Müsaitlik bilgileri yüklenirken bir hata oluştu.",
          variant: "destructive",
        });
      }
    };
    loadAvailabilitySlots();
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Ders Rezervasyonu</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Müsait zamanlar yükleniyor...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Ders Rezervasyonu - {teacherName}</CardTitle>
            <div className="flex gap-2 items-center">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                Müsait
              </Badge>
              <Badge variant="secondary">
                {hourlyRate} TL/saat
              </Badge>
            </div>
          </div>
          <p className="text-sm text-muted-foreground">
            Ders rezervasyonu yapmak için müsait zaman dilimlerinden birini seçin.
          </p>
        </CardHeader>
        <CardContent>
          <div className="h-[600px]">
            <FullCalendar
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              initialView="timeGridWeek"
              headerToolbar={{
                left: "prev,next today",
                center: "title",
                right: "dayGridMonth,timeGridWeek,timeGridDay",
              }}
              events={events}
              editable={false}
              selectable={false}
              dayMaxEvents={true}
              weekends={true}
              locale="tr"
              buttonText={{
                today: "Bugün",
                month: "Ay",
                week: "Hafta",
                day: "Gün",
              }}
              allDaySlot={false}
              slotMinTime="06:00:00"
              slotMaxTime="24:00:00"
              slotDuration="01:00:00"
              eventClick={handleEventClick}
              height="100%"
              contentHeight="auto"
              businessHours={{
                daysOfWeek: [1, 2, 3, 4, 5, 6, 0], // Pazartesi-Pazar
                startTime: "08:00",
                endTime: "22:00",
              }}
              eventDisplay="block"
              eventClassNames="cursor-pointer hover:opacity-80"
            />
          </div>
        </CardContent>
      </Card>

      {/* Rezervasyon Modal */}
      <Dialog open={isBookingModalOpen} onOpenChange={setIsBookingModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Ders Rezervasyonu</DialogTitle>
          </DialogHeader>
          {selectedSlot && (
            <BookingForm
              slot={selectedSlot}
              teacherId={teacherId}
              teacherName={teacherName}
              hourlyRate={hourlyRate}
              studentId={studentId}
              onSuccess={handleBookingSuccess}
              onCancel={() => setIsBookingModalOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
