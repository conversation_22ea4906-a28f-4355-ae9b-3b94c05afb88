"use client"

import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Star, Briefcase, BookOpen, Languages, Clock, GraduationCap } from "lucide-react" // Award kaldırıldı
import type { TeacherProfile } from "@/types/teacher"
import { VerifiedBadge } from "@/components/VerifiedBadge"

interface TeacherHeroProps {
  teacher: TeacherProfile
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 100,
    },
  },
}

export function TeacherHero({ teacher }: TeacherHeroProps) {
  // Deneyim yılını hesaplamak için basit bir varsayım
  // TODO: <PERSON><PERSON> sağlam bir deneyim yılı hesaplaması yapılabilir (örn: duration'dan parse ederek)
  const experienceYears = teacher.experience?.length > 0 ? teacher.experience[0].duration.split(' ')[0] : 'N/A';

  return (
    // Ana sarmalayıcı, arka plan vb.
    (<div className="relative bg-[#f2f4f7]">
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 opacity-30 bg-[radial-gradient(circle_at_50%_50%,rgba(255,98,0,0.1),transparent_50%)]" />
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23e5e7eb' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E")`,
            backgroundSize: "80px 80px",
            animation: "background-move 30s linear infinite",
          }}
        />
      </div>
      {/* İçerik Alanı */}
      <div className="container mx-auto px-4 py-16 relative z-10">
        <motion.div className="max-w-6xl mx-auto" variants={containerVariants} initial="hidden" animate="visible">
          <div className="bg-white rounded-3xl border border-gray-100 shadow-xl">
            <div className="p-8 lg:p-12">
              <div className="flex flex-col lg:flex-row gap-12 items-start">
                {/* Sol Sütun - Profil Bilgileri */}
                <motion.div className="flex-shrink-0 w-full lg:w-auto space-y-6" variants={itemVariants}> {/* space-y azaltıldı */}
                  {/* Avatar */}
                  <div className="relative mx-auto lg:mx-0 w-fit">
                    <div className="absolute inset-0 bg-gradient-to-r from-[#FF6200] to-[#FF8A00] rounded-2xl blur-xl opacity-20 animate-pulse" />
                    <Avatar className="w-36 h-36 lg:w-48 lg:h-48 rounded-2xl border-2 border-gray-100 relative">
                      <AvatarImage src={teacher.avatar} alt={teacher.name} className="object-cover" />
                      <AvatarFallback className="text-4xl bg-orange-50 text-orange-600">
                        {teacher.name[0]}
                      </AvatarFallback>
                    </Avatar>
                  </div>

                  {/* İsim, Başlık, Onay */}
                  <div className="text-center lg:text-left space-y-1">
                    <div className="flex items-baseline gap-2 flex-wrap justify-center lg:justify-start">
                      <h1 className="text-3xl lg:text-4xl font-bold text-gray-900">{teacher.name}</h1>
                      <VerifiedBadge isVerified={teacher.verified} className="[&>svg]:w-6 [&>svg]:h-6" />
                    </div>
                    <p className="text-xl text-gray-600 font-medium">{teacher.title}</p>
                  </div>

                  {/* Kompakt İstatistikler */}
                  <div className="grid grid-cols-2 gap-x-6 gap-y-3 text-center lg:text-left border-t pt-4">
                     <div className="flex items-center justify-center lg:justify-start gap-1.5">
                        <Star className="w-5 h-5 text-yellow-400 fill-current" />
                        <div>
                           <span className="font-semibold">{teacher.stats.avgRating.toFixed(1)}</span>
                           <span className="text-sm text-muted-foreground"> ({teacher.stats.reviewCount})</span>
                        </div>
                     </div>
                     <div className="flex items-center justify-center lg:justify-start gap-1.5">
                        <span className="text-lg font-semibold text-primary">{teacher.hourlyRate}₺</span>
                        <span className="text-sm text-muted-foreground">/ders</span>
                     </div>
                     <div className="flex items-center justify-center lg:justify-start gap-1.5">
                        <Briefcase className="w-5 h-5 text-muted-foreground" />
                        <span className="text-sm">{experienceYears} Yıl Deneyim</span>
                     </div>
                     <div className="flex items-center justify-center lg:justify-start gap-1.5">
                        <BookOpen className="w-5 h-5 text-muted-foreground" />
                        <span className="text-sm">{teacher.stats.totalLessons} Ders</span>
                     </div>
                  </div>

                  {/* Ek Bilgiler (Diller, Uzmanlık, Yanıt Süresi) */}
                  <div className="space-y-3 border-t pt-4"> {/* border-t ve pt-4 eklendi */}
                     <div className="flex items-center gap-2 text-sm text-gray-600">
                       <Languages className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                       <span>Konuştuğu Diller: {teacher.languages?.join(', ') || 'N/A'}</span>
                     </div>
                     <div className="flex items-center gap-2 text-sm text-gray-600">
                       <GraduationCap className="w-5 h-5 text-muted-foreground flex-shrink-0" /> {/* Uzmanlık için GraduationCap kullanıldı */}
                       <span>Uzmanlıklar: {teacher.specialties?.slice(0, 2).join(', ') || 'N/A'}{teacher.specialties?.length > 2 ? '...' : ''}</span> {/* İlk 2 uzmanlık */}
                     </div>
                     {teacher.stats.responseTime && (
                       <div className="flex items-center gap-2 text-sm text-gray-600">
                         <Clock className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                         <span>Genellikle {teacher.stats.responseTime} içinde yanıt verir</span>
                       </div>
                     )}
                  </div>

                  {/* Rozetler */}
                  <div className="flex flex-wrap gap-2 justify-center lg:justify-start">
                    {teacher.badges.map((badge) => (
                      <Badge
                        key={badge}
                        variant="secondary"
                        className="bg-orange-50 text-orange-700 hover:bg-orange-100 border-0 shadow-sm px-3 py-1"
                      >
                        {badge}
                      </Badge>
                    ))}
                  </div>

                </motion.div>

                {/* Sağ Sütun - İstatistikler ve Eylemler (Bu kısım TeacherStats ve TeacherBookingBar'a ait, şimdilik kaldırıldı) */}

              </div>
            </div>
          </div>
        </motion.div>
      </div>
      {/* Arka plan animasyonu için stil */}
      <style jsx global>{`
        @keyframes background-move {
          0% {
            background-position: 0 0;
          }
          100% {
            background-position: 160px 160px;
          }
        }
      `}</style>
    </div>)
  );
}