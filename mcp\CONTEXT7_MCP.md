# Context7 MCP Sunucu <PERSON> (AlmancaABC Projesi)

## <PERSON><PERSON>u Bilgileri

- **Tan<PERSON>mlayıcı:** `github.com/upstash/context7-mcp`
- **<PERSON><PERSON><PERSON><PERSON><PERSON>:** `cmd /c npx -y @upstash/context7-mcp@latest`
- **Açıklama:** Context7 kullanarak çeşitli kütüphaneler için güncel dokümantasyon çekme araçları sağlar.

## Kullanım İpuçları

- Context7'yi kullanırken, prompt'unuzda "use context7" ifadesini belirtmeniz gerekir.

## Kullanılabilir Araçlar

### `resolve-library-id`
- **Açıklama:** İlk adım olarak gereklidir. Genel bir paket adını Context7 uyumlu bir kütüphane kimliğine dönüştürür. `get-library-docs` aracından önce çağrılmalıdır.
- **<PERSON><PERSON><PERSON>:**
  ```json
  {
    "type": "object",
    "properties": {
      "libraryName": { "type": "string", "description": "Aranacak kütüphane adı" }
    },
    "required": ["libraryName"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "libraryName": "react"
  }
  ```

### `get-library-docs`
- **Açıklama:** Bir kütüphane için güncel dokümantasyon çeker. `resolve-library-id` aracından alınan Context7 uyumlu kütüphane kimliğini gerektirir.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "context7CompatibleLibraryID": { "type": "string", "description": "Context7 kütüphane kimliği (örn: 'mongodb/docs')" },
      "topic": { "type": "string", "description": "Dokümantasyonun odaklanacağı konu (örn: 'hooks')" },
      "tokens": { "type": "number", "description": "Çekilecek maksimum token sayısı (varsayılan: 5000)" }
    },
    "required": ["context7CompatibleLibraryID"],
    "additionalProperties": false
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "context7CompatibleLibraryID": "react",
    "topic": "hooks",
    "tokens": 2000
  }
