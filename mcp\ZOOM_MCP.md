# Zoom MCP Sunucusu (Topluluk Sunucusu)

## Neden Gerekli Olabilir?
AlmancaABC proje planında ([`AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md`](AlmancaABC_Proje_Plani_ve_Yol_Haritasi.md:1)) online dersler için Zoom SDK entegrasyonu hedeflenmektedir. Zoom MCP sunucusu, Zoom toplantılarının platform üzerinden programatik olarak yönetilmesini sağlayarak manuel işlemleri azaltabilir ve kullanıcı deneyimini iyileştirebilir.

## Potansiyel Kullanım Alanları
- Platform üzerinden yeni bir ders planlandığında otomatik olarak bir Zoom toplantısı oluşturulması.
- Oluşturulan Zoom toplantı linklerinin ve bilgilerinin ilgili dersle ve katılımcılarla (öğretmen ve öğrenci) eşleştirilmesi.
- Ders başlangıç saatinden önce katılımcılara Zoom toplantı hatırlatıcıları gönderilmesi.
- Mevcut Zoom toplantılarının listelenmesi, güncellenmesi veya iptal edilmesi.
- Ders kayıtlarının yönetimi (eğer Zoom bu özelliği destekliyorsa ve MCP sunucusu bu bilgiye erişebiliyorsa), örneğin kayıt linklerinin ders sonrası öğrencilere sunulması.
- Katılımcı yönetimi ve toplantı güvenliği ayarlarının yapılması.

## Entegrasyon Durumu
**Not:** Bu MCP sunucusu henüz AlmancaABC projesine entegre edilmemiştir. Entegrasyon tamamlandığında bu doküman, entegrasyon detayları ve kullanım örnekleriyle güncellenmelidir.