# WhatsApp Entegrasyon Rehberi (AlmancaABC Projesi)

## <PERSON><PERSON><PERSON> (Windows)

### Temel Dizin Yapısı

```text
C:\Users\<USER>\almancaabc\
└── whatsapp-mcp/
    ├── whatsapp-bridge/    # Go köprüsü
    └── whatsapp-mcp-server/ # Python sunucusu
```
*(Not: G<PERSON><PERSON>ek sunucu, başlangıç komutuna göre `C:\Users\<USER>\Documents\Cline\MCP\whatsapp-mcp\whatsapp-mcp-server` konumundan çalışır)*

## Sunucu Bilgileri

- **Tanımlayıcı:** `github.com/lharries/whatsapp-mcp`
- **<PERSON><PERSON>langıç Komutu:** `uv --directory C:/Users/<USER>/Documents/Cline/MCP/whatsapp-mcp/whatsapp-mcp-server run main.py`
- **Açıklama:** Go köprüsü ve Python sunucusu aracılığıyla WhatsApp ile etkileşim için araçlar sağlar.

## Çalıştırma Adımları

#### 1. Go Köprüsünü Başlat

Aşağıdaki adımları sırasıyla PowerShell terminaline yapıştırarak WhatsApp köprüsünü başlatabilirsin:

```
cd "C:\Users\<USER>\Documents\Cline\MCP\whatsapp-mcp\whatsapp-bridge"
$env:Path = 'C:\msys64\ucrt64\bin;' + $env:Path
$env:CGO_ENABLED=1
go run main.go
```

- Komutları tek tek veya blok olarak kopyalayıp çalıştırabilirsin.
- Başarılı bağlantıdan sonra terminalde `[Client INFO]` ile başlayan satırlar göreceksin.
- QR kodu çıktığında WhatsApp uygulamasında "Bağlı Cihazlar" menüsünden okutabilirsin.

#### 2. QR Kodu Alın

- Yukarıdaki komut çalıştıktan sonra terminalde QR kodu görünecek
- WhatsApp uygulamanızda "Bağlı Cihazlar" > "Bağlan" diyerek bu QR kodu taratın
- Eğer daha önce taratmışsanız 20 gün boyunca taratmaya gerek yok

#### 3. Python Sunucusunu Başlat (MCP Server)

```powershell
uv --directory "C:\Users\<USER>\Documents\Cline\MCP\whatsapp-mcp\whatsapp-mcp-server" run main.py
```

## MCP Araçları (WhatsApp Entegrasyonu)

Claude aşağıdaki araçları kullanarak WhatsApp ile etkileşim kurabilir:

### `search_contacts`
- **Açıklama:** WhatsApp kişilerinde ad veya telefon numarasına göre arama yapar.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "query": { "title": "Query", "type": "string" }
    },
    "required": ["query"],
    "title": "search_contactsArguments"
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "query": "Ahmet"
  }
  ```

### `list_messages`
- **Açıklama:** İsteğe bağlı bağlam ile belirtilen kriterlere uyan WhatsApp mesajlarını alın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "after": { "anyOf": [{ "type": "string" }, { "type": "null" }], "default": null, "title": "After" },
      "before": { "anyOf": [{ "type": "string" }, { "type": "null" }], "default": null, "title": "Before" },
      "sender_phone_number": { "anyOf": [{ "type": "string" }, { "type": "null" }], "default": null, "title": "Sender Phone Number" },
      "chat_jid": { "anyOf": [{ "type": "string" }, { "type": "null" }], "default": null, "title": "Chat Jid" },
      "query": { "anyOf": [{ "type": "string" }, { "type": "null" }], "default": null, "title": "Query" },
      "limit": { "default": 20, "title": "Limit", "type": "integer" },
      "page": { "default": 0, "title": "Page", "type": "integer" },
      "include_context": { "default": true, "title": "Include Context", "type": "boolean" },
      "context_before": { "default": 1, "title": "Context Before", "type": "integer" },
      "context_after": { "default": 1, "title": "Context After", "type": "integer" }
    },
    "title": "list_messagesArguments"
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "query": "merhaba",
    "limit": 5
  }
  ```

### `list_chats`
- **Açıklama:** Belirtilen kriterlere uyan WhatsApp sohbetlerini alın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "query": { "anyOf": [{ "type": "string" }, { "type": "null" }], "default": null, "title": "Query" },
      "limit": { "default": 20, "title": "Limit", "type": "integer" },
      "page": { "default": 0, "title": "Page", "type": "integer" },
      "include_last_message": { "default": true, "title": "Include Last Message", "type": "boolean" },
      "sort_by": { "default": "last_active", "title": "Sort By", "type": "string" }
    },
    "title": "list_chatsArguments"
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "query": "aile",
    "limit": 3
  }
  ```

### `get_chat`
- **Açıklama:** JID'ye göre WhatsApp sohbet meta verilerini alın.
  ```json
  {
    "type": "object",
    "properties": {
      "chat_jid": { "title": "Chat Jid", "type": "string" },
      "include_last_message": { "default": true, "title": "Include Last Message", "type": "boolean" }
    },
    "required": ["chat_jid"],
    "title": "get_chatArguments"
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "chat_jid": "<EMAIL>"
  }
  ```

### `get_direct_chat_by_contact`
- **Açıklama:** Gönderen telefon numarasına göre WhatsApp sohbet meta verilerini alın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "sender_phone_number": { "title": "Sender Phone Number", "type": "string" }
    },
    "required": ["sender_phone_number"],
    "title": "get_direct_chat_by_contactArguments"
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "sender_phone_number": "1234567890"
  }
  ```

### `get_contact_chats`
- **Açıklama:** Kişiyle ilgili tüm WhatsApp sohbetlerini alın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "jid": { "title": "Jid", "type": "string" },
      "limit": { "default": 20, "title": "Limit", "type": "integer" },
      "page": { "default": 0, "title": "Page", "type": "integer" }
    },
    "required": ["jid"],
    "title": "get_contact_chatsArguments"
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "jid": "<EMAIL>",
    "limit": 10
  }
  ```

### `get_last_interaction`
- **Açıklama:** Kişiyle ilgili en son WhatsApp mesajını alın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "jid": { "title": "Jid", "type": "string" }
    },
    "required": ["jid"],
    "title": "get_last_interactionArguments"
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "jid": "<EMAIL>"
  }
  ```

### `get_message_context`
- **Açıklama:** Belirli bir WhatsApp mesajının bağlamını alın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "message_id": { "title": "Message Id", "type": "string" },
      "before": { "default": 5, "title": "Before", "type": "integer" },
      "after": { "default": 5, "title": "After", "type": "integer" }
    },
    "required": ["message_id"],
    "title": "get_message_contextArguments"
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "message_id": "mesaj-kimliği",
    "before": 3,
    "after": 3
  }
  ```

### `send_message`
- **Açıklama:** Bir kişiye veya gruba WhatsApp mesajı gönderin. Gruplar için JID kullanın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "recipient": { "title": "Recipient", "type": "string", "description": "Telefon numarası ( +/sembol yok) veya JID" },
      "message": { "title": "Message", "type": "string" }
    },
    "required": ["recipient", "message"],
    "title": "send_messageArguments"
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "recipient": "1234567890",
    "message": "Merhaba!"
  }
  ```

### `send_file`
- **Açıklama:** WhatsApp aracılığıyla bir dosya (resim, ses, video, belge) gönderin. Gruplar için JID kullanın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "recipient": { "title": "Recipient", "type": "string", "description": "Telefon numarası ( +/sembol yok) veya JID" },
      "media_path": { "title": "Media Path", "type": "string", "description": "Medya dosyasının mutlak yolu" }
    },
    "required": ["recipient", "media_path"],
    "title": "send_fileArguments"
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "recipient": "1234567890",
    "media_path": "C:/Users/<USER>/Desktop/resim.jpg"
  }
  ```

### `send_audio_message`
- **Açıklama:** Bir ses dosyasını WhatsApp sesli mesajı olarak gönderin. Gruplar için JID kullanın. FFmpeg gerektirir (ogg olmayan dosyalar için), aksi takdirde `send_file` kullanın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "recipient": { "title": "Recipient", "type": "string", "description": "Telefon numarası ( +/sembol yok) veya JID" },
      "media_path": { "title": "Media Path", "type": "string", "description": "Ses dosyasının mutlak yolu" }
    },
    "required": ["recipient", "media_path"],
    "title": "send_audio_messageArguments"
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "recipient": "1234567890",
    "media_path": "C:/Users/<USER>/Desktop/ses.ogg"
  }
  ```

### `download_media`
- **Açıklama:** Bir WhatsApp mesajından medyayı indirin ve yerel dosya yolunu alın.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "message_id": { "title": "Message Id", "type": "string" },
      "chat_jid": { "title": "Chat Jid", "type": "string" }
    },
    "required": ["message_id", "chat_jid"],
    "title": "download_mediaArguments"
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "message_id": "mesaj-kimliği",
    "chat_jid": "<EMAIL>"
  }
  ```

## Medya Yönetimi Özellikleri

MCP sunucusu çeşitli medya türlerini hem göndermeyi hem de almayı destekler:

### Medya Gönderme

WhatsApp kişilerinize çeşitli medya türleri gönderebilirsiniz:

- **Resimler, Videolar, Belgeler**: Desteklenen herhangi bir medya türünü paylaşmak için `send_file` aracını kullanın.
- **Sesli Mesajlar**: Ses dosyalarını çalınabilir WhatsApp sesli mesajları olarak göndermek için `send_audio_message` aracını kullanın.
  - En uyumlu sonuç için ses dosyaları .ogg Opus formatında olmalıdır.
  - FFmpeg kuruluysa, sistem diğer ses formatlarını (MP3, WAV vb.) otomatik olarak dönüştürecektir.
  - FFmpeg yoksa, `send_file` aracını kullanarak ham ses dosyaları gönderebilirsiniz, ancak bunlar çalınabilir sesli mesajlar olarak görünmeyecektir.

### Medya İndirme

Varsayılan olarak, medyanın yalnızca meta verileri yerel veritabanında saklanır. Mesaj, medya gönderildiğini belirtecektir. Bu medyaya erişmek için, mesajı yazdırırken gösterilen `message_id` ve `chat_jid`'yi alan `download_media` aracını kullanmanız gerekir. Bu araç medyayı indirir ve daha sonra açılabilecek veya başka bir araca aktarılabilecek dosya yolunu döndürür.

## Durdurma İşlemleri

### Terminalden Durdurma
```powershell
# Go köprüsünü durdur (varsa)
taskkill /IM go.exe /F

# Python MCP sunucusunu durdur
# (Çalışan uv/python işlemini manuel olarak durdurmak gerekebilir)
taskkill /IM python.exe /F
```

### Manuel Durdurma
1. Çalışan terminal pencerelerinde `Ctrl+C` tuş kombinasyonu
2. WhatsApp'tan cihaz bağlantısını kesme (Ayarlar > Bağlı Cihazlar)

## Teknik Detaylar

- Claude, istekleri Python MCP sunucusuna gönderir
- MCP sunucusu, WhatsApp verileri için Go köprüsünü sorgular veya doğrudan SQLite veritabanına erişir
- Go, WhatsApp API'sine erişir ve SQLite veritabanını güncel tutar
- Veriler zincir boyunca Claude'a geri akar
- Mesaj gönderirken, istek Claude'dan MCP sunucusuna, oradan Go köprüsüne ve WhatsApp'a akar

## Kaynak

Bu entegrasyon [whatsapp-mcp](https://github.com/lharries/whatsapp-mcp) projesi kullanılarak gerçekleştirilmiştir.

## Sorun Giderme

- `uv` çalıştırırken izin sorunları yaşarsanız, PATH'inize eklemeniz veya çalıştırılabilirin tam yolunu kullanmanız gerekebilir.
- Entegrasyonun düzgün çalışması için hem Go uygulamasının hem de Python sunucusunun çalışıyor olduğundan emin olun.

### Kimlik Doğrulama Sorunları

- **QR Kodu Görüntülenmiyor**: QR kodu görünmüyorsa, kimlik doğrulama betiğini yeniden başlatmayı deneyin. Sorun devam ederse, terminalinizin QR kodlarını görüntülemeyi desteklediğinden emin olun.
- **WhatsApp Zaten Giriş Yapmış**: Oturumunuz zaten aktifse, Go köprüsü QR kodu göstermeden otomatik olarak yeniden bağlanacaktır.
- **Cihaz Limiti Aşıldı**: WhatsApp bağlı cihaz sayısını sınırlar. Bu limite ulaşırsanız, telefonunuzdaki WhatsApp'tan mevcut bir cihazı kaldırmanız gerekir (Ayarlar > Bağlı Cihazlar).
- **Mesajlar Yüklenmiyor**: İlk kimlik doğrulamadan sonra, özellikle çok sayıda sohbetiniz varsa, mesaj geçmişinizin yüklenmesi birkaç dakika sürebilir.
- **WhatsApp Senkronizasyon Dışı**: WhatsApp mesajlarınız köprü ile senkronizasyon dışı kalırsa, her iki veritabanı dosyasını da silin (`whatsapp-bridge/store/messages.db` ve `whatsapp-bridge/store/whatsapp.db`) ve köprüyü yeniden başlatıp yeniden kimlik doğrulama yapın.

## Önemli Notlar

- İlk kez başlatıldığında, Go köprüsünün başlatılması ve QR kodun görüntülenmesi 1-2 dakika kadar sürebilir. Sabırlı olun.
- QR kodu bir kez okuttuktan sonra 20 gün boyunca tekrar okutmanıza gerek yoktur.
- Sonraki kullanımlarda sistem daha hızlı başlayacaktır.

## Güncelleme Geçmişi
- 22 Nisan 2025
