"use client";

import React, { useState } from 'react';
import { ChevronDown, HelpCircle } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TeacherProfileData } from '../TeacherProfileClient';

interface FAQTabProps {
  teacher: TeacherProfileData;
}

export const FAQTab: React.FC<FAQTabProps> = ({ teacher }) => {
  const [openFAQId, setOpenFAQId] = useState<string | null>(null);

  const handleToggleFAQ = (id: string) => {
    setOpenFAQId(openFAQId === id ? null : id);
  };

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg">
              <HelpCircle className="w-6 h-6 text-white" />
            </div>
            <span className="text-2xl font-bold text-gray-900">Sıkça Sorulan Sorular</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {teacher.faqs?.length > 0 ? (
            teacher.faqs.map((faq) => (
              <div key={faq.id} className="border-b border-gray-200 pb-4 last:border-b-0 last:pb-0">
                <button
                  className="flex justify-between items-center w-full text-left font-semibold text-lg text-gray-800 hover:text-purple-700 transition-colors duration-200"
                  onClick={() => handleToggleFAQ(faq.id)}
                >
                  {faq.question}
                  <ChevronDown
                    className={`w-5 h-5 transition-transform duration-300 ${openFAQId === faq.id ? 'rotate-180' : ''}`}
                  />
                </button>
                <div
                  className={`overflow-hidden transition-all duration-300 ease-in-out ${openFAQId === faq.id ? 'max-h-screen opacity-100 mt-2' : 'max-h-0 opacity-0'}`}
                >
                  <p className="text-gray-600 leading-relaxed pr-8">{faq.answer}</p>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>Henüz sıkça sorulan soru bulunmamaktadır.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};