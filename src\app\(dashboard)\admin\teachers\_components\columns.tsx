// src/app/(dashboard)/admin/teachers/_components/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
// import { Checkbox } from "@/components/ui/checkbox"; // Row selection için eklendi (opsiyonel) - <PERSON><PERSON><PERSON>lik kullanılmıyor
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { approveTeacher, deleteTeacher } from "@/lib/actions/teacher.actions"; // approveTeacher ve deleteTeacher import edildi
// import { useTransition } from "react"; // Yüklenme durumu için - Ş<PERSON><PERSON>lik kullanılmıyor
import { toast } from "sonner"; // Bildirim için (varsa)
import { useRouter } from "next/navigation"; // Router import edildi
import React from "react"; // React import edildi

// TeacherData tipini page.tsx'teki ile aynı veya Prisma'dan import edilecek şekilde tanımla
// import { Teacher } from "@prisma/client"; type TeacherData = Teacher;
type TeacherData = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  is_approved: boolean;
  created_at: Date;
};

export const columns: ColumnDef<TeacherData>[] = [
  // Opsiyonel: Satır Seçimi Checkbox'ı
  // {
  //   id: "select",
  //   header: ({ table }) => (
  //     <Checkbox
  //       checked={
  //         table.getIsAllPageRowsSelected() ||
  //         (table.getIsSomePageRowsSelected() && "indeterminate")
  //       }
  //       onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
  //       aria-label="Tümünü seç"
  //     />
  //   ),
  //   cell: ({ row }) => (
  //     <Checkbox
  //       checked={row.getIsSelected()}
  //       onCheckedChange={(value) => row.toggleSelected(!!value)}
  //       aria-label="Satırı seç"
  //     />
  //   ),
  //   enableSorting: false,
  //   enableHiding: false,
  // },
  {
    accessorKey: "firstName",
    header: ({ column }) => {
       return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Ad
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
        const firstName = row.getValue("firstName") as string | null;
        const lastName = row.original.lastName; // Soyadı da alalım
        return <div className="font-medium">{`${firstName ?? ''} ${lastName ?? ''}`}</div>;
    }
  },
  {
    accessorKey: "email",
     header: ({ column }) => {
       return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          E-posta
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
  },
  {
    accessorKey: "is_approved",
    header: "Onay Durumu",
    cell: ({ row }) => {
      const isApproved = row.getValue("is_approved");
      return (
        <Badge variant={isApproved ? "default" : "destructive"}>
          {isApproved ? "Onaylı" : "Bekliyor"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => {
       return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Kayıt Tarihi
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const date = new Date(row.getValue("created_at"));
      const formatted = date.toLocaleDateString("tr-TR"); // Türkçe format
      return <div className="text-right font-medium">{formatted}</div>;
    },
  },
   {
    id: "actions",
    cell: ({ row }) => <TeacherActionsCell teacher={row.original} />,
     enableSorting: false,
     enableHiding: false,
  },
];

// Ayrı bir bileşen olarak Actions hücresi
const TeacherActionsCell: React.FC<{ teacher: TeacherData }> = ({ teacher }) => {
  const router = useRouter();
  // const [isPending, startTransition] = useTransition(); // Yüklenme durumu için

  const handleDelete = async () => {
    // TODO: Silme onayı dialog'u eklenebilir (Shadcn Alert Dialog)
    // startTransition(async () => { // Yüklenme durumu için
      try {
        const result = await deleteTeacher(teacher.id);
        if (result.success) {
          toast.success(result.message || "Öğretmen başarıyla silindi.");
          router.refresh(); // Sayfayı yenile
        } else {
          toast.error(result.message || "Silme sırasında bir hata oluştu.");
        }
      } catch (error) {
        console.error("Delete teacher error:", error);
        toast.error("Silme sırasında beklenmedik bir hata oluştu.");
      }
    // });
  };

  const handleApprove = async () => {
    // startTransition(async () => { // Yüklenme durumu için
      try {
        const result = await approveTeacher(teacher.id);
        if (result.success) {
          toast.success(result.message || "Öğretmen başarıyla onaylandı.");
          router.refresh(); // Sayfayı yenile (revalidatePath action içinde de var)
        } else {
          toast.error(result.message || "Onaylama sırasında bir hata oluştu.");
        }
      } catch (error) {
        console.error("Approve teacher error:", error);
        toast.error("Onaylama sırasında beklenmedik bir hata oluştu.");
      }
    // });
  };

  return (
    (<DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Menüyü aç</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Eylemler</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => navigator.clipboard.writeText(teacher.id)}
        >
          Öğretmen ID Kopyala
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href={`/admin/teachers/${teacher.id}`} passHref>Profili Görüntüle</Link>
        </DropdownMenuItem>
        {!teacher.is_approved && (
          <DropdownMenuItem onClick={handleApprove /* TODO: disabled={isPending} */}>
            Onayla
          </DropdownMenuItem>
        )}
        <DropdownMenuItem
          className="text-destructive"
          onClick={handleDelete} // TODO: disabled={isPending}
        >
          Sil
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>)
  );
};