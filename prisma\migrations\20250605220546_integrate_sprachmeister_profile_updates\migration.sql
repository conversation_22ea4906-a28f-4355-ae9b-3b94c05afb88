/*
  Warnings:

  - You are about to drop the column `aboutMe` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `averageResponseTimeHours` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `benefits` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `experience` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `experience_years` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `is_verified` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `newStudentsLast30Days` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `offersGroupLessons` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `profileViewCount` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `profile_image_url` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `satisfactionRate` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `spokenLanguages` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `teachingMethodology` on the `Teacher` table. All the data in the column will be lost.
  - You are about to drop the column `videoCourses` on the `Teacher` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[email]` on the table `Teacher` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Teacher" DROP COLUMN "aboutMe",
DROP COLUMN "averageResponseTimeHours",
DROP COLUMN "benefits",
DROP COLUMN "experience",
DROP COLUMN "experience_years",
DROP COLUMN "is_verified",
DROP COLUMN "newStudentsLast30Days",
DROP COLUMN "offersGroupLessons",
DROP COLUMN "profileViewCount",
DROP COLUMN "profile_image_url",
DROP COLUMN "satisfactionRate",
DROP COLUMN "spokenLanguages",
DROP COLUMN "teachingMethodology",
DROP COLUMN "videoCourses",
ADD COLUMN     "avatar" TEXT,
ADD COLUMN     "bio" TEXT,
ADD COLUMN     "email" TEXT,
ADD COLUMN     "experienceYears" INTEGER,
ADD COLUMN     "isVerified" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "languages" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "name" TEXT,
ADD COLUMN     "timezone" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "Teacher_email_key" ON "Teacher"("email");
