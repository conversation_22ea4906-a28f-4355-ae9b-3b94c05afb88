# Fetch MCP Sunucu <PERSON> (AlmancaABC Projesi)

## <PERSON><PERSON><PERSON> Bilgileri

- **Tan<PERSON>mlayıcı:** `github.com/zcaceres/fetch-mcp`
- **<PERSON><PERSON>lang<PERSON><PERSON> Komutu:** `node C:/Users/<USER>/Documents/Cline/MCP/fetch-mcp/dist/index.js`
- **Açıklama:** Web içeriğini çeşitli formatlarda (HTML, Markdown, TXT, JSON) çekme araçları sağlar.

## Kullanılabilir Araçlar

### `fetch_html`
- **Açıklama:** Bir web sitesini çeker ve içeriğini HTML olarak döndürür.
- **<PERSON><PERSON><PERSON> Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "url": { "type": "string", "description": "Çekilecek web sitesinin URL'si" },
      "headers": { "type": "object", "description": "İsteğe bağlı başlıklar" }
    },
    "required": ["url"]
  }
  ```
- **<PERSON><PERSON><PERSON>llanım:**
  ```json
  {
    "url": "https://www.example.com"
  }
  ```

### `fetch_markdown`
- **Açıklama:** Bir web sitesini çeker ve içeriğini Markdown olarak döndürür.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "url": { "type": "string", "description": "Çekilecek web sitesinin URL'si" },
      "headers": { "type": "object", "description": "İsteğe bağlı başlıklar" }
    },
    "required": ["url"]
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "url": "https://www.example.com"
  }
  ```

### `fetch_txt`
- **Açıklama:** Bir web sitesini çeker ve içeriğini düz metin olarak döndürür (HTML olmadan).
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "url": { "type": "string", "description": "Çekilecek web sitesinin URL'si" },
      "headers": { "type": "object", "description": "İsteğe bağlı başlıklar" }
    },
    "required": ["url"]
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "url": "https://www.example.com"
  }
  ```

### `fetch_json`
- **Açıklama:** Bir URL'den bir JSON dosyası çeker.
- **Giriş Şeması:**
  ```json
  {
    "type": "object",
    "properties": {
      "url": { "type": "string", "description": "Çekilecek JSON dosyasının URL'si" },
      "headers": { "type": "object", "description": "İsteğe bağlı başlıklar" }
    },
    "required": ["url"]
  }
  ```
- **Örnek Kullanım:**
  ```json
  {
    "url": "https://www.example.com/data.json"
  }
